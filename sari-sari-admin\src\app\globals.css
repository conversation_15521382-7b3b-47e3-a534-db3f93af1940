@import "tailwindcss";

:root {
  --background: #ffffff;
  --foreground: #171717;

  /* Revantad Store Brand Colors - Green and Mustard */
  --primary-green: #22c55e;
  --primary-green-dark: #16a34a;
  --secondary-mustard: #facc15;
  --secondary-mustard-dark: #eab308;
}

@media (prefers-color-scheme: dark) {
  :root {
    --background: #0f172a;
    --foreground: #f1f5f9;
    --primary-green: #15803d;
    --secondary-mustard: #a16207;
  }
}

body {
  background: var(--background);
  color: var(--foreground);
  font-family: 'Inter', Arial, Helvetica, sans-serif;
  font-feature-settings: "rlig" 1, "calt" 1;
}

html {
  scroll-behavior: smooth;
}

/* Revantad Store Custom Component Classes */
.btn-primary {
  @apply bg-green-500 hover:bg-green-600 text-white font-medium px-6 py-3 rounded-lg transition-all duration-200 shadow-md hover:shadow-lg transform hover:-translate-y-0.5;
}

.btn-secondary {
  @apply bg-yellow-400 hover:bg-yellow-500 text-white font-medium px-6 py-3 rounded-lg transition-all duration-200 shadow-md hover:shadow-lg transform hover:-translate-y-0.5;
}

.btn-outline {
  @apply border-2 border-green-500 text-green-500 hover:bg-green-500 hover:text-white font-medium px-6 py-3 rounded-lg transition-all duration-200;
}

.card {
  @apply bg-white dark:bg-slate-800 rounded-xl shadow-md border border-gray-100 dark:border-slate-700;
}

.hero-gradient {
  @apply bg-gradient-to-br from-green-500 via-green-600 to-yellow-400;
}

.text-gradient {
  @apply bg-gradient-to-r from-green-600 to-yellow-500 bg-clip-text text-transparent;
}

.glass-effect {
  @apply backdrop-blur-md bg-white/10 border border-white/20;
}

.animate-fade-in {
  animation: fadeIn 0.5s ease-in-out;
}

.animate-slide-up {
  animation: slideUp 0.5s ease-out;
}

.animate-bounce-gentle {
  animation: bounceGentle 2s infinite;
}

@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

@keyframes slideUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes bounceGentle {
  0%, 100% { transform: translateY(0); }
  50% { transform: translateY(-10px); }
}

/* Custom scrollbar */
::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

::-webkit-scrollbar-track {
  background: transparent;
  border-radius: 4px;
}

::-webkit-scrollbar-thumb {
  background: rgba(156, 163, 175, 0.4);
  border-radius: 4px;
  transition: background-color 0.2s ease;
}

::-webkit-scrollbar-thumb:hover {
  background: rgba(156, 163, 175, 0.6);
}

.dark ::-webkit-scrollbar-thumb {
  background: rgba(75, 85, 99, 0.4);
}

.dark ::-webkit-scrollbar-thumb:hover {
  background: rgba(75, 85, 99, 0.6);
}

/* Sidebar specific scrollbar */
.sidebar-scroll::-webkit-scrollbar {
  width: 6px;
}

.sidebar-scroll::-webkit-scrollbar-thumb {
  background: rgba(156, 163, 175, 0.3);
  border-radius: 3px;
}

.sidebar-scroll::-webkit-scrollbar-thumb:hover {
  background: rgba(156, 163, 175, 0.5);
}

.dark .sidebar-scroll::-webkit-scrollbar-thumb {
  background: rgba(75, 85, 99, 0.3);
}

.dark .sidebar-scroll::-webkit-scrollbar-thumb:hover {
  background: rgba(75, 85, 99, 0.5);
}

/* Prevent text blurring on transforms */
.sidebar-nav-item {
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-rendering: optimizeLegibility;
  backface-visibility: hidden;
  transform: translateZ(0);
}

/* Ensure crisp text rendering */
.crisp-text {
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-rendering: optimizeLegibility;
}

/* Enhanced backdrop blur for sticky elements */
.backdrop-blur-enhanced {
  backdrop-filter: blur(8px) saturate(180%);
  -webkit-backdrop-filter: blur(8px) saturate(180%);
}

/* Smooth scrolling for sidebar navigation */
.sidebar-nav-scroll {
  scroll-behavior: smooth;
  scrollbar-width: thin;
}

/* Enhanced scrollbar for navigation area - Professional & Larger */
.sidebar-nav-scroll {
  scrollbar-width: auto;
  scrollbar-color: rgba(34, 197, 94, 0.4) rgba(243, 244, 246, 0.3);
}

.sidebar-nav-scroll::-webkit-scrollbar {
  width: 12px;
  background: rgba(243, 244, 246, 0.3);
  border-radius: 6px;
}

.sidebar-nav-scroll::-webkit-scrollbar-track {
  background: rgba(243, 244, 246, 0.3);
  border-radius: 6px;
  margin: 4px 0;
  border: 1px solid rgba(229, 231, 235, 0.4);
}

.sidebar-nav-scroll::-webkit-scrollbar-thumb {
  background: linear-gradient(135deg, rgba(34, 197, 94, 0.6) 0%, rgba(16, 185, 129, 0.5) 100%);
  border-radius: 6px;
  border: 2px solid rgba(255, 255, 255, 0.2);
  transition: all 0.3s ease;
  box-shadow: 0 2px 4px rgba(34, 197, 94, 0.2);
}

.sidebar-nav-scroll::-webkit-scrollbar-thumb:hover {
  background: linear-gradient(135deg, rgba(34, 197, 94, 0.8) 0%, rgba(16, 185, 129, 0.7) 100%);
  border-color: rgba(255, 255, 255, 0.3);
  box-shadow: 0 4px 8px rgba(34, 197, 94, 0.3);
  transform: scale(1.05);
}

.sidebar-nav-scroll::-webkit-scrollbar-thumb:active {
  background: linear-gradient(135deg, rgba(34, 197, 94, 0.9) 0%, rgba(16, 185, 129, 0.8) 100%);
  box-shadow: 0 2px 4px rgba(34, 197, 94, 0.4);
}

/* Dark theme enhanced scrollbar */
.dark .sidebar-nav-scroll {
  scrollbar-color: rgba(34, 197, 94, 0.5) rgba(51, 65, 85, 0.4);
}

.dark .sidebar-nav-scroll::-webkit-scrollbar {
  background: rgba(51, 65, 85, 0.4);
}

.dark .sidebar-nav-scroll::-webkit-scrollbar-track {
  background: rgba(51, 65, 85, 0.4);
  border: 1px solid rgba(71, 85, 105, 0.5);
}

.dark .sidebar-nav-scroll::-webkit-scrollbar-thumb {
  background: linear-gradient(135deg, rgba(34, 197, 94, 0.7) 0%, rgba(16, 185, 129, 0.6) 100%);
  border: 2px solid rgba(30, 41, 59, 0.3);
  box-shadow: 0 2px 4px rgba(34, 197, 94, 0.3);
}

.dark .sidebar-nav-scroll::-webkit-scrollbar-thumb:hover {
  background: linear-gradient(135deg, rgba(34, 197, 94, 0.9) 0%, rgba(16, 185, 129, 0.8) 100%);
  border-color: rgba(30, 41, 59, 0.4);
  box-shadow: 0 4px 8px rgba(34, 197, 94, 0.4);
}

.dark .sidebar-nav-scroll::-webkit-scrollbar-thumb:active {
  background: linear-gradient(135deg, rgba(34, 197, 94, 1) 0%, rgba(16, 185, 129, 0.9) 100%);
  box-shadow: 0 2px 4px rgba(34, 197, 94, 0.5);
}

/* Professional gradient overlays */
.gradient-overlay-top {
  background: linear-gradient(to bottom, rgba(255, 255, 255, 0.9), transparent);
  pointer-events: none;
}

.dark .gradient-overlay-top {
  background: linear-gradient(to bottom, rgba(30, 41, 59, 0.9), transparent);
}

.gradient-overlay-bottom {
  background: linear-gradient(to top, rgba(249, 250, 251, 0.9), transparent);
  pointer-events: none;
}

.dark .gradient-overlay-bottom {
  background: linear-gradient(to top, rgba(15, 23, 42, 0.9), transparent);
}

/* Fade indicators for scrollable content */
.scroll-fade-top::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 20px;
  background: linear-gradient(to bottom, rgba(255, 255, 255, 0.8), transparent);
  pointer-events: none;
  z-index: 5;
}

.dark .scroll-fade-top::before {
  background: linear-gradient(to bottom, rgba(30, 41, 59, 0.8), transparent);
}

.scroll-fade-bottom::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  height: 20px;
  background: linear-gradient(to top, rgba(249, 250, 251, 0.8), transparent);
  pointer-events: none;
  z-index: 5;
}

.dark .scroll-fade-bottom::after {
  background: linear-gradient(to top, rgba(15, 23, 42, 0.8), transparent);
}

/* Focus styles */
.focus-ring {
  @apply focus:outline-none focus:ring-2 focus:ring-green-500 focus:ring-offset-2 dark:focus:ring-offset-slate-800;
}

/* Hover effects */
.hover-lift {
  @apply transition-transform duration-200 hover:-translate-y-1;
}

.hover-glow {
  @apply transition-shadow duration-200 hover:shadow-lg hover:shadow-green-500/25;
}

/* Sidebar Collapse/Expand Animations */
.sidebar-collapsed {
  width: 88px !important;
  min-width: 88px !important;
}

.sidebar-expanded {
  width: 320px !important;
  min-width: 320px !important;
}

.sidebar-transition {
  transition: width 0.4s cubic-bezier(0.4, 0, 0.2, 1), min-width 0.4s cubic-bezier(0.4, 0, 0.2, 1);
}

.sidebar-content-hidden {
  opacity: 0;
  transform: translateX(-10px);
  transition: opacity 0.2s ease, transform 0.2s ease;
}

.sidebar-content-visible {
  opacity: 1;
  transform: translateX(0);
  transition: opacity 0.3s ease 0.1s, transform 0.3s ease 0.1s;
}

/* Enhanced Icon-Only Mode */
.sidebar-icon-only {
  justify-content: center !important;
  padding: 0.875rem !important;
  margin: 0 0.5rem !important;
  border-radius: 1rem !important;
  position: relative;
  min-height: 3.5rem;
  display: flex;
  align-items: center;
}

.sidebar-icon-only .sidebar-text {
  display: none;
}

.sidebar-icon-only .icon-container {
  margin: 0 !important;
  padding: 0.75rem !important;
  border-radius: 0.875rem !important;
  position: relative;
  overflow: visible;
}

/* Professional Icon Hover Effects */
.sidebar-icon-only:hover {
  transform: translateY(-2px);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.sidebar-icon-only:hover .icon-container {
  transform: scale(1.1);
  transition: transform 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

/* Active State Indicator for Collapsed Icons */
.sidebar-icon-only.active::after {
  content: '';
  position: absolute;
  right: -4px;
  top: 50%;
  transform: translateY(-50%);
  width: 4px;
  height: 24px;
  border-radius: 2px;
  background: linear-gradient(135deg, #22c55e 0%, #16a34a 100%);
  box-shadow: 0 2px 8px rgba(34, 197, 94, 0.4);
  animation: pulse-indicator 2s infinite;
}

@keyframes pulse-indicator {
  0%, 100% { opacity: 1; transform: translateY(-50%) scale(1); }
  50% { opacity: 0.7; transform: translateY(-50%) scale(0.95); }
}

/* Enhanced Icon Styling */
.collapsed-icon {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.1));
}

.collapsed-icon:hover {
  filter: drop-shadow(0 4px 8px rgba(0, 0, 0, 0.15));
}

/* Professional Chevron Toggle Button - Attached to Scrollbar Top */
.sidebar-toggle-btn {
  position: absolute;
  top: 8px;
  right: 2px;
  z-index: 40;
  width: 28px;
  height: 28px;
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  cursor: pointer;
  border: 2px solid;
  backdrop-filter: blur(12px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15), 0 2px 4px rgba(0, 0, 0, 0.08);
  transform: translateX(0);
}

.sidebar-toggle-btn:hover {
  transform: translateY(-50%) scale(1.15);
  box-shadow: 0 8px 24px rgba(34, 197, 94, 0.4), 0 4px 8px rgba(34, 197, 94, 0.2);
  border-radius: 16px;
}

.sidebar-toggle-btn:active {
  transform: translateY(-50%) scale(1.05);
  transition: all 0.1s ease;
}

/* Enhanced Chevron Icon */
.sidebar-toggle-icon {
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  filter: drop-shadow(0 1px 2px rgba(0, 0, 0, 0.1));
}

.sidebar-collapsed .sidebar-toggle-icon {
  transform: rotate(180deg);
}

.sidebar-toggle-btn:hover .sidebar-toggle-icon {
  transform: scale(1.2);
  filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.2));
}

.sidebar-collapsed .sidebar-toggle-btn:hover .sidebar-toggle-icon {
  transform: rotate(180deg) scale(1.2);
}

/* Professional Gradient Background */
.sidebar-toggle-btn::before {
  content: '';
  position: absolute;
  inset: 0;
  border-radius: inherit;
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.2) 0%, rgba(255, 255, 255, 0.05) 100%);
  opacity: 0;
  transition: opacity 0.3s ease;
}

.sidebar-toggle-btn:hover::before {
  opacity: 1;
}

/* Pulse Effect for Better UX */
.sidebar-toggle-btn::after {
  content: '';
  position: absolute;
  inset: -4px;
  border-radius: 20px;
  background: linear-gradient(135deg, rgba(34, 197, 94, 0.2) 0%, rgba(16, 185, 129, 0.1) 100%);
  opacity: 0;
  transform: scale(0.8);
  transition: all 0.3s ease;
  z-index: -1;
}

.sidebar-toggle-btn:hover::after {
  opacity: 1;
  transform: scale(1);
}

/* Enhanced Visual Feedback */
.sidebar-toggle-btn:focus {
  outline: none;
  box-shadow: 0 0 0 3px rgba(34, 197, 94, 0.3), 0 8px 24px rgba(34, 197, 94, 0.4);
}

/* Responsive Positioning */
@media (max-height: 768px) {
  .sidebar-toggle-btn {
    width: 28px;
    height: 28px;
    right: -14px;
  }
}

/* Professional State Indicators */
.sidebar-toggle-btn.expanding {
  animation: expand-pulse 0.6s ease-out;
}

.sidebar-toggle-btn.collapsing {
  animation: collapse-pulse 0.6s ease-out;
}

@keyframes expand-pulse {
  0% { transform: translateY(-50%) scale(1); }
  50% { transform: translateY(-50%) scale(1.2); box-shadow: 0 8px 24px rgba(34, 197, 94, 0.5); }
  100% { transform: translateY(-50%) scale(1); }
}

@keyframes collapse-pulse {
  0% { transform: translateY(-50%) scale(1); }
  50% { transform: translateY(-50%) scale(1.2); box-shadow: 0 8px 24px rgba(59, 130, 246, 0.5); }
  100% { transform: translateY(-50%) scale(1); }
}

/* Enhanced Chevron Styling */
.sidebar-toggle-icon {
  position: relative;
}

.sidebar-toggle-icon::before {
  content: '';
  position: absolute;
  inset: -2px;
  border-radius: 4px;
  background: linear-gradient(45deg, rgba(255, 255, 255, 0.1) 0%, transparent 50%, rgba(255, 255, 255, 0.1) 100%);
  opacity: 0;
  transition: opacity 0.3s ease;
}

.sidebar-toggle-btn:hover .sidebar-toggle-icon::before {
  opacity: 1;
}

/* Improved Visual Hierarchy */
.sidebar-toggle-btn {
  border-style: solid;
  border-width: 2px;
  position: relative;
  overflow: hidden;
}

.sidebar-toggle-btn:before {
  z-index: 1;
}

.sidebar-toggle-icon {
  position: relative;
  z-index: 2;
}

/* Professional Tooltip Styling */
.sidebar-tooltip {
  position: absolute;
  left: calc(100% + 16px);
  top: 50%;
  transform: translateY(-50%);
  background: linear-gradient(135deg, rgba(0, 0, 0, 0.95) 0%, rgba(30, 30, 30, 0.9) 100%);
  color: white;
  padding: 12px 16px;
  border-radius: 12px;
  font-size: 0.875rem;
  font-weight: 500;
  white-space: nowrap;
  opacity: 0;
  visibility: hidden;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  z-index: 1000;
  pointer-events: none;
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.2), 0 4px 8px rgba(0, 0, 0, 0.1);
  backdrop-filter: blur(12px);
  border: 1px solid rgba(255, 255, 255, 0.1);
  max-width: 280px;
}

.sidebar-tooltip::before {
  content: '';
  position: absolute;
  left: -6px;
  top: 50%;
  transform: translateY(-50%);
  width: 0;
  height: 0;
  border-style: solid;
  border-width: 6px 6px 6px 0;
  border-color: transparent rgba(0, 0, 0, 0.95) transparent transparent;
  filter: drop-shadow(-1px 0 1px rgba(0, 0, 0, 0.1));
}

.dark .sidebar-tooltip {
  background: linear-gradient(135deg, rgba(15, 23, 42, 0.98) 0%, rgba(30, 41, 59, 0.95) 100%);
  border: 1px solid rgba(71, 85, 105, 0.4);
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.4), 0 4px 8px rgba(0, 0, 0, 0.2);
}

.dark .sidebar-tooltip::before {
  border-color: transparent rgba(15, 23, 42, 0.98) transparent transparent;
}

.sidebar-icon-only:hover .sidebar-tooltip {
  opacity: 1;
  visibility: visible;
  transform: translateY(-50%) translateX(4px);
}

/* Collapsed Header and Footer Enhancements */
.collapsed-header-icon {
  position: relative;
  transition: all 0.3s ease;
}

.collapsed-header-icon:hover {
  transform: scale(1.1) rotate(5deg);
}

.collapsed-footer-icon {
  position: relative;
  transition: all 0.3s ease;
}

.collapsed-footer-icon:hover {
  transform: scale(1.1);
  filter: brightness(1.2);
}

/* Collapsed Sidebar Layout Optimizations */
.sidebar-collapsed .sidebar-nav-scroll {
  padding-left: 0.75rem !important;
  padding-right: 0.75rem !important;
}

.sidebar-collapsed nav {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 0.75rem;
}

/* Enhanced Spacing for Collapsed Mode */
.sidebar-collapsed .flex-col {
  padding-top: 0.5rem;
  padding-bottom: 0.5rem;
}

/* Smooth Icon Transitions */
.sidebar-icon-only .icon-container .collapsed-icon {
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
}

.sidebar-icon-only:hover .icon-container .collapsed-icon {
  transform: scale(1.15);
  filter: drop-shadow(0 6px 12px rgba(0, 0, 0, 0.2));
}

/* Active State Glow Effect */
.sidebar-icon-only.active .icon-container {
  box-shadow: 0 0 20px rgba(34, 197, 94, 0.3), 0 4px 12px rgba(34, 197, 94, 0.2);
  border: 1px solid rgba(34, 197, 94, 0.3);
}

.dark .sidebar-icon-only.active .icon-container {
  box-shadow: 0 0 20px rgba(34, 197, 94, 0.4), 0 4px 12px rgba(34, 197, 94, 0.3);
  border: 1px solid rgba(34, 197, 94, 0.4);
}

/* Responsive Tooltip Positioning */
@media (max-height: 600px) {
  .sidebar-tooltip {
    font-size: 0.8rem;
    padding: 8px 12px;
  }
}
