{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 148, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/tindahan/sari-sari-admin/src/lib/supabase.ts"], "sourcesContent": ["import { createClient } from '@supabase/supabase-js'\n\nconst supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL!\nconst supabaseAnonKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!\n\nexport const supabase = createClient(supabaseUrl, supabase<PERSON>nonKey)\n\n// Database Types\nexport interface Product {\n  id: string\n  name: string\n  image_url?: string\n  net_weight: string\n  price: number\n  stock_quantity: number\n  category: string\n  created_at: string\n  updated_at: string\n}\n\nexport interface CustomerDebt {\n  id: string\n  customer_name: string\n  customer_family_name: string\n  product_name: string\n  product_price: number\n  quantity: number\n  total_amount: number\n  debt_date: string\n  created_at: string\n  updated_at: string\n}\n\n// Product Categories\nexport const PRODUCT_CATEGORIES = [\n  'Snacks',\n  'Canned Goods',\n  'Beverages',\n  'Personal Care',\n  'Household Items',\n  'Condiments',\n  'Rice & Grains',\n  'Instant Foods',\n  'Dairy Products',\n  'Others'\n] as const\n\nexport type ProductCategory = typeof PRODUCT_CATEGORIES[number]\n"], "names": [], "mappings": ";;;;AAAA;;AAEA,MAAM;AACN,MAAM;AAEC,MAAM,WAAW,CAAA,GAAA,yLAAA,CAAA,eAAY,AAAD,EAAE,aAAa;AA6B3C,MAAM,qBAAqB;IAChC;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;CACD", "debugId": null}}, {"offset": {"line": 175, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/tindahan/sari-sari-admin/src/app/api/products/route.ts"], "sourcesContent": ["import { NextRequest, NextResponse } from 'next/server'\nimport { supabase } from '@/lib/supabase'\n\n// GET - Fetch all products\nexport async function GET() {\n  try {\n    const { data: products, error } = await supabase\n      .from('products')\n      .select('*')\n      .order('created_at', { ascending: false })\n\n    if (error) {\n      return NextResponse.json({ error: error.message }, { status: 500 })\n    }\n\n    return NextResponse.json({ products })\n  } catch {\n    return NextResponse.json(\n      { error: 'Failed to fetch products' },\n      { status: 500 }\n    )\n  }\n}\n\n// POST - Create new product\nexport async function POST(request: NextRequest) {\n  try {\n    const body = await request.json()\n    const { name, image_url, net_weight, price, stock_quantity, category } = body\n\n    // Validate required fields\n    if (!name || !net_weight || !price || !category) {\n      return NextResponse.json(\n        { error: 'Missing required fields' },\n        { status: 400 }\n      )\n    }\n\n    const { data: product, error } = await supabase\n      .from('products')\n      .insert([\n        {\n          name,\n          image_url,\n          net_weight,\n          price: parseFloat(price),\n          stock_quantity: parseInt(stock_quantity) || 0,\n          category,\n        },\n      ])\n      .select()\n      .single()\n\n    if (error) {\n      return NextResponse.json({ error: error.message }, { status: 500 })\n    }\n\n    return NextResponse.json({ product }, { status: 201 })\n  } catch {\n    return NextResponse.json(\n      { error: 'Failed to create product' },\n      { status: 500 }\n    )\n  }\n}\n"], "names": [], "mappings": ";;;;AAAA;AACA;;;AAGO,eAAe;IACpB,IAAI;QACF,MAAM,EAAE,MAAM,QAAQ,EAAE,KAAK,EAAE,GAAG,MAAM,wHAAA,CAAA,WAAQ,CAC7C,IAAI,CAAC,YACL,MAAM,CAAC,KACP,KAAK,CAAC,cAAc;YAAE,WAAW;QAAM;QAE1C,IAAI,OAAO;YACT,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;gBAAE,OAAO,MAAM,OAAO;YAAC,GAAG;gBAAE,QAAQ;YAAI;QACnE;QAEA,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;YAAE;QAAS;IACtC,EAAE,OAAM;QACN,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;YAAE,OAAO;QAA2B,GACpC;YAAE,QAAQ;QAAI;IAElB;AACF;AAGO,eAAe,KAAK,OAAoB;IAC7C,IAAI;QACF,MAAM,OAAO,MAAM,QAAQ,IAAI;QAC/B,MAAM,EAAE,IAAI,EAAE,SAAS,EAAE,UAAU,EAAE,KAAK,EAAE,cAAc,EAAE,QAAQ,EAAE,GAAG;QAEzE,2BAA2B;QAC3B,IAAI,CAAC,QAAQ,CAAC,cAAc,CAAC,SAAS,CAAC,UAAU;YAC/C,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBAAE,OAAO;YAA0B,GACnC;gBAAE,QAAQ;YAAI;QAElB;QAEA,MAAM,EAAE,MAAM,OAAO,EAAE,KAAK,EAAE,GAAG,MAAM,wHAAA,CAAA,WAAQ,CAC5C,IAAI,CAAC,YACL,MAAM,CAAC;YACN;gBACE;gBACA;gBACA;gBACA,OAAO,WAAW;gBAClB,gBAAgB,SAAS,mBAAmB;gBAC5C;YACF;SACD,EACA,MAAM,GACN,MAAM;QAET,IAAI,OAAO;YACT,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;gBAAE,OAAO,MAAM,OAAO;YAAC,GAAG;gBAAE,QAAQ;YAAI;QACnE;QAEA,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;YAAE;QAAQ,GAAG;YAAE,QAAQ;QAAI;IACtD,EAAE,OAAM;QACN,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;YAAE,OAAO;QAA2B,GACpC;YAAE,QAAQ;QAAI;IAElB;AACF", "debugId": null}}]}