{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/tindahan/sari-sari-admin/node_modules/echarts/lib/data/helper/sourceHelper.js"], "sourcesContent": ["\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\r\n* Licensed to the Apache Software Foundation (ASF) under one\r\n* or more contributor license agreements.  See the NOTICE file\r\n* distributed with this work for additional information\r\n* regarding copyright ownership.  The ASF licenses this file\r\n* to you under the Apache License, Version 2.0 (the\r\n* \"License\"); you may not use this file except in compliance\r\n* with the License.  You may obtain a copy of the License at\r\n*\r\n*   http://www.apache.org/licenses/LICENSE-2.0\r\n*\r\n* Unless required by applicable law or agreed to in writing,\r\n* software distributed under the License is distributed on an\r\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\r\n* KIND, either express or implied.  See the License for the\r\n* specific language governing permissions and limitations\r\n* under the License.\r\n*/\nimport { makeInner, getDataItemValue, queryReferringComponents, SINGLE_REFERRING } from '../../util/model.js';\nimport { createHashMap, each, isArray, isString, isObject, isTypedArray } from 'zrender/lib/core/util.js';\nimport { SOURCE_FORMAT_ORIGINAL, SOURCE_FORMAT_ARRAY_ROWS, SOURCE_FORMAT_OBJECT_ROWS, SERIES_LAYOUT_BY_ROW, SOURCE_FORMAT_KEYED_COLUMNS } from '../../util/types.js';\n// The result of `guessOrdinal`.\nexport var BE_ORDINAL = {\n  Must: 1,\n  Might: 2,\n  Not: 3 // Other cases\n};\nvar innerGlobalModel = makeInner();\n/**\r\n * MUST be called before mergeOption of all series.\r\n */\nexport function resetSourceDefaulter(ecModel) {\n  // `datasetMap` is used to make default encode.\n  innerGlobalModel(ecModel).datasetMap = createHashMap();\n}\n/**\r\n * [The strategy of the arrengment of data dimensions for dataset]:\r\n * \"value way\": all axes are non-category axes. So series one by one take\r\n *     several (the number is coordSysDims.length) dimensions from dataset.\r\n *     The result of data arrengment of data dimensions like:\r\n *     | ser0_x | ser0_y | ser1_x | ser1_y | ser2_x | ser2_y |\r\n * \"category way\": at least one axis is category axis. So the the first data\r\n *     dimension is always mapped to the first category axis and shared by\r\n *     all of the series. The other data dimensions are taken by series like\r\n *     \"value way\" does.\r\n *     The result of data arrengment of data dimensions like:\r\n *     | ser_shared_x | ser0_y | ser1_y | ser2_y |\r\n *\r\n * @return encode Never be `null/undefined`.\r\n */\nexport function makeSeriesEncodeForAxisCoordSys(coordDimensions, seriesModel, source) {\n  var encode = {};\n  var datasetModel = querySeriesUpstreamDatasetModel(seriesModel);\n  // Currently only make default when using dataset, util more reqirements occur.\n  if (!datasetModel || !coordDimensions) {\n    return encode;\n  }\n  var encodeItemName = [];\n  var encodeSeriesName = [];\n  var ecModel = seriesModel.ecModel;\n  var datasetMap = innerGlobalModel(ecModel).datasetMap;\n  var key = datasetModel.uid + '_' + source.seriesLayoutBy;\n  var baseCategoryDimIndex;\n  var categoryWayValueDimStart;\n  coordDimensions = coordDimensions.slice();\n  each(coordDimensions, function (coordDimInfoLoose, coordDimIdx) {\n    var coordDimInfo = isObject(coordDimInfoLoose) ? coordDimInfoLoose : coordDimensions[coordDimIdx] = {\n      name: coordDimInfoLoose\n    };\n    if (coordDimInfo.type === 'ordinal' && baseCategoryDimIndex == null) {\n      baseCategoryDimIndex = coordDimIdx;\n      categoryWayValueDimStart = getDataDimCountOnCoordDim(coordDimInfo);\n    }\n    encode[coordDimInfo.name] = [];\n  });\n  var datasetRecord = datasetMap.get(key) || datasetMap.set(key, {\n    categoryWayDim: categoryWayValueDimStart,\n    valueWayDim: 0\n  });\n  // TODO\n  // Auto detect first time axis and do arrangement.\n  each(coordDimensions, function (coordDimInfo, coordDimIdx) {\n    var coordDimName = coordDimInfo.name;\n    var count = getDataDimCountOnCoordDim(coordDimInfo);\n    // In value way.\n    if (baseCategoryDimIndex == null) {\n      var start = datasetRecord.valueWayDim;\n      pushDim(encode[coordDimName], start, count);\n      pushDim(encodeSeriesName, start, count);\n      datasetRecord.valueWayDim += count;\n      // ??? TODO give a better default series name rule?\n      // especially when encode x y specified.\n      // consider: when multiple series share one dimension\n      // category axis, series name should better use\n      // the other dimension name. On the other hand, use\n      // both dimensions name.\n    }\n    // In category way, the first category axis.\n    else if (baseCategoryDimIndex === coordDimIdx) {\n      pushDim(encode[coordDimName], 0, count);\n      pushDim(encodeItemName, 0, count);\n    }\n    // In category way, the other axis.\n    else {\n      var start = datasetRecord.categoryWayDim;\n      pushDim(encode[coordDimName], start, count);\n      pushDim(encodeSeriesName, start, count);\n      datasetRecord.categoryWayDim += count;\n    }\n  });\n  function pushDim(dimIdxArr, idxFrom, idxCount) {\n    for (var i = 0; i < idxCount; i++) {\n      dimIdxArr.push(idxFrom + i);\n    }\n  }\n  function getDataDimCountOnCoordDim(coordDimInfo) {\n    var dimsDef = coordDimInfo.dimsDef;\n    return dimsDef ? dimsDef.length : 1;\n  }\n  encodeItemName.length && (encode.itemName = encodeItemName);\n  encodeSeriesName.length && (encode.seriesName = encodeSeriesName);\n  return encode;\n}\n/**\r\n * Work for data like [{name: ..., value: ...}, ...].\r\n *\r\n * @return encode Never be `null/undefined`.\r\n */\nexport function makeSeriesEncodeForNameBased(seriesModel, source, dimCount) {\n  var encode = {};\n  var datasetModel = querySeriesUpstreamDatasetModel(seriesModel);\n  // Currently only make default when using dataset, util more reqirements occur.\n  if (!datasetModel) {\n    return encode;\n  }\n  var sourceFormat = source.sourceFormat;\n  var dimensionsDefine = source.dimensionsDefine;\n  var potentialNameDimIndex;\n  if (sourceFormat === SOURCE_FORMAT_OBJECT_ROWS || sourceFormat === SOURCE_FORMAT_KEYED_COLUMNS) {\n    each(dimensionsDefine, function (dim, idx) {\n      if ((isObject(dim) ? dim.name : dim) === 'name') {\n        potentialNameDimIndex = idx;\n      }\n    });\n  }\n  var idxResult = function () {\n    var idxRes0 = {};\n    var idxRes1 = {};\n    var guessRecords = [];\n    // 5 is an experience value.\n    for (var i = 0, len = Math.min(5, dimCount); i < len; i++) {\n      var guessResult = doGuessOrdinal(source.data, sourceFormat, source.seriesLayoutBy, dimensionsDefine, source.startIndex, i);\n      guessRecords.push(guessResult);\n      var isPureNumber = guessResult === BE_ORDINAL.Not;\n      // [Strategy of idxRes0]: find the first BE_ORDINAL.Not as the value dim,\n      // and then find a name dim with the priority:\n      // \"BE_ORDINAL.Might|BE_ORDINAL.Must\" > \"other dim\" > \"the value dim itself\".\n      if (isPureNumber && idxRes0.v == null && i !== potentialNameDimIndex) {\n        idxRes0.v = i;\n      }\n      if (idxRes0.n == null || idxRes0.n === idxRes0.v || !isPureNumber && guessRecords[idxRes0.n] === BE_ORDINAL.Not) {\n        idxRes0.n = i;\n      }\n      if (fulfilled(idxRes0) && guessRecords[idxRes0.n] !== BE_ORDINAL.Not) {\n        return idxRes0;\n      }\n      // [Strategy of idxRes1]: if idxRes0 not satisfied (that is, no BE_ORDINAL.Not),\n      // find the first BE_ORDINAL.Might as the value dim,\n      // and then find a name dim with the priority:\n      // \"other dim\" > \"the value dim itself\".\n      // That is for backward compat: number-like (e.g., `'3'`, `'55'`) can be\n      // treated as number.\n      if (!isPureNumber) {\n        if (guessResult === BE_ORDINAL.Might && idxRes1.v == null && i !== potentialNameDimIndex) {\n          idxRes1.v = i;\n        }\n        if (idxRes1.n == null || idxRes1.n === idxRes1.v) {\n          idxRes1.n = i;\n        }\n      }\n    }\n    function fulfilled(idxResult) {\n      return idxResult.v != null && idxResult.n != null;\n    }\n    return fulfilled(idxRes0) ? idxRes0 : fulfilled(idxRes1) ? idxRes1 : null;\n  }();\n  if (idxResult) {\n    encode.value = [idxResult.v];\n    // `potentialNameDimIndex` has highest priority.\n    var nameDimIndex = potentialNameDimIndex != null ? potentialNameDimIndex : idxResult.n;\n    // By default, label uses itemName in charts.\n    // So we don't set encodeLabel here.\n    encode.itemName = [nameDimIndex];\n    encode.seriesName = [nameDimIndex];\n  }\n  return encode;\n}\n/**\r\n * @return If return null/undefined, indicate that should not use datasetModel.\r\n */\nexport function querySeriesUpstreamDatasetModel(seriesModel) {\n  // Caution: consider the scenario:\n  // A dataset is declared and a series is not expected to use the dataset,\n  // and at the beginning `setOption({series: { noData })` (just prepare other\n  // option but no data), then `setOption({series: {data: [...]}); In this case,\n  // the user should set an empty array to avoid that dataset is used by default.\n  var thisData = seriesModel.get('data', true);\n  if (!thisData) {\n    return queryReferringComponents(seriesModel.ecModel, 'dataset', {\n      index: seriesModel.get('datasetIndex', true),\n      id: seriesModel.get('datasetId', true)\n    }, SINGLE_REFERRING).models[0];\n  }\n}\n/**\r\n * @return Always return an array event empty.\r\n */\nexport function queryDatasetUpstreamDatasetModels(datasetModel) {\n  // Only these attributes declared, we by default reference to `datasetIndex: 0`.\n  // Otherwise, no reference.\n  if (!datasetModel.get('transform', true) && !datasetModel.get('fromTransformResult', true)) {\n    return [];\n  }\n  return queryReferringComponents(datasetModel.ecModel, 'dataset', {\n    index: datasetModel.get('fromDatasetIndex', true),\n    id: datasetModel.get('fromDatasetId', true)\n  }, SINGLE_REFERRING).models;\n}\n/**\r\n * The rule should not be complex, otherwise user might not\r\n * be able to known where the data is wrong.\r\n * The code is ugly, but how to make it neat?\r\n */\nexport function guessOrdinal(source, dimIndex) {\n  return doGuessOrdinal(source.data, source.sourceFormat, source.seriesLayoutBy, source.dimensionsDefine, source.startIndex, dimIndex);\n}\n// dimIndex may be overflow source data.\n// return {BE_ORDINAL}\nfunction doGuessOrdinal(data, sourceFormat, seriesLayoutBy, dimensionsDefine, startIndex, dimIndex) {\n  var result;\n  // Experience value.\n  var maxLoop = 5;\n  if (isTypedArray(data)) {\n    return BE_ORDINAL.Not;\n  }\n  // When sourceType is 'objectRows' or 'keyedColumns', dimensionsDefine\n  // always exists in source.\n  var dimName;\n  var dimType;\n  if (dimensionsDefine) {\n    var dimDefItem = dimensionsDefine[dimIndex];\n    if (isObject(dimDefItem)) {\n      dimName = dimDefItem.name;\n      dimType = dimDefItem.type;\n    } else if (isString(dimDefItem)) {\n      dimName = dimDefItem;\n    }\n  }\n  if (dimType != null) {\n    return dimType === 'ordinal' ? BE_ORDINAL.Must : BE_ORDINAL.Not;\n  }\n  if (sourceFormat === SOURCE_FORMAT_ARRAY_ROWS) {\n    var dataArrayRows = data;\n    if (seriesLayoutBy === SERIES_LAYOUT_BY_ROW) {\n      var sample = dataArrayRows[dimIndex];\n      for (var i = 0; i < (sample || []).length && i < maxLoop; i++) {\n        if ((result = detectValue(sample[startIndex + i])) != null) {\n          return result;\n        }\n      }\n    } else {\n      for (var i = 0; i < dataArrayRows.length && i < maxLoop; i++) {\n        var row = dataArrayRows[startIndex + i];\n        if (row && (result = detectValue(row[dimIndex])) != null) {\n          return result;\n        }\n      }\n    }\n  } else if (sourceFormat === SOURCE_FORMAT_OBJECT_ROWS) {\n    var dataObjectRows = data;\n    if (!dimName) {\n      return BE_ORDINAL.Not;\n    }\n    for (var i = 0; i < dataObjectRows.length && i < maxLoop; i++) {\n      var item = dataObjectRows[i];\n      if (item && (result = detectValue(item[dimName])) != null) {\n        return result;\n      }\n    }\n  } else if (sourceFormat === SOURCE_FORMAT_KEYED_COLUMNS) {\n    var dataKeyedColumns = data;\n    if (!dimName) {\n      return BE_ORDINAL.Not;\n    }\n    var sample = dataKeyedColumns[dimName];\n    if (!sample || isTypedArray(sample)) {\n      return BE_ORDINAL.Not;\n    }\n    for (var i = 0; i < sample.length && i < maxLoop; i++) {\n      if ((result = detectValue(sample[i])) != null) {\n        return result;\n      }\n    }\n  } else if (sourceFormat === SOURCE_FORMAT_ORIGINAL) {\n    var dataOriginal = data;\n    for (var i = 0; i < dataOriginal.length && i < maxLoop; i++) {\n      var item = dataOriginal[i];\n      var val = getDataItemValue(item);\n      if (!isArray(val)) {\n        return BE_ORDINAL.Not;\n      }\n      if ((result = detectValue(val[dimIndex])) != null) {\n        return result;\n      }\n    }\n  }\n  function detectValue(val) {\n    var beStr = isString(val);\n    // Consider usage convenience, '1', '2' will be treated as \"number\".\n    // `Number('')` (or any whitespace) is `0`.\n    if (val != null && Number.isFinite(Number(val)) && val !== '') {\n      return beStr ? BE_ORDINAL.Might : BE_ORDINAL.Not;\n    } else if (beStr && val !== '-') {\n      return BE_ORDINAL.Must;\n    }\n  }\n  return BE_ORDINAL.Not;\n}"], "names": [], "mappings": "AACA;;;;;;;;;;;;;;;;;AAiBA,GAGA;;CAEC,GAED;;;;;;;;;;;;;;;;;AAiBA;;;;;;;;;AACA;AACA;AACA;;;;AAEO,IAAI,aAAa;IACtB,MAAM;IACN,OAAO;IACP,KAAK,EAAE,cAAc;AACvB;AACA,IAAI,mBAAmB,CAAA,GAAA,+IAAA,CAAA,YAAS,AAAD;AAIxB,SAAS,qBAAqB,OAAO;IAC1C,+CAA+C;IAC/C,iBAAiB,SAAS,UAAU,GAAG,CAAA,GAAA,8IAAA,CAAA,gBAAa,AAAD;AACrD;AAgBO,SAAS,gCAAgC,eAAe,EAAE,WAAW,EAAE,MAAM;IAClF,IAAI,SAAS,CAAC;IACd,IAAI,eAAe,gCAAgC;IACnD,+EAA+E;IAC/E,IAAI,CAAC,gBAAgB,CAAC,iBAAiB;QACrC,OAAO;IACT;IACA,IAAI,iBAAiB,EAAE;IACvB,IAAI,mBAAmB,EAAE;IACzB,IAAI,UAAU,YAAY,OAAO;IACjC,IAAI,aAAa,iBAAiB,SAAS,UAAU;IACrD,IAAI,MAAM,aAAa,GAAG,GAAG,MAAM,OAAO,cAAc;IACxD,IAAI;IACJ,IAAI;IACJ,kBAAkB,gBAAgB,KAAK;IACvC,CAAA,GAAA,8IAAA,CAAA,OAAI,AAAD,EAAE,iBAAiB,SAAU,iBAAiB,EAAE,WAAW;QAC5D,IAAI,eAAe,CAAA,GAAA,8IAAA,CAAA,WAAQ,AAAD,EAAE,qBAAqB,oBAAoB,eAAe,CAAC,YAAY,GAAG;YAClG,MAAM;QACR;QACA,IAAI,aAAa,IAAI,KAAK,aAAa,wBAAwB,MAAM;YACnE,uBAAuB;YACvB,2BAA2B,0BAA0B;QACvD;QACA,MAAM,CAAC,aAAa,IAAI,CAAC,GAAG,EAAE;IAChC;IACA,IAAI,gBAAgB,WAAW,GAAG,CAAC,QAAQ,WAAW,GAAG,CAAC,KAAK;QAC7D,gBAAgB;QAChB,aAAa;IACf;IACA,OAAO;IACP,kDAAkD;IAClD,CAAA,GAAA,8IAAA,CAAA,OAAI,AAAD,EAAE,iBAAiB,SAAU,YAAY,EAAE,WAAW;QACvD,IAAI,eAAe,aAAa,IAAI;QACpC,IAAI,QAAQ,0BAA0B;QACtC,gBAAgB;QAChB,IAAI,wBAAwB,MAAM;YAChC,IAAI,QAAQ,cAAc,WAAW;YACrC,QAAQ,MAAM,CAAC,aAAa,EAAE,OAAO;YACrC,QAAQ,kBAAkB,OAAO;YACjC,cAAc,WAAW,IAAI;QAC7B,mDAAmD;QACnD,wCAAwC;QACxC,qDAAqD;QACrD,+CAA+C;QAC/C,mDAAmD;QACnD,wBAAwB;QAC1B,OAEK,IAAI,yBAAyB,aAAa;YAC7C,QAAQ,MAAM,CAAC,aAAa,EAAE,GAAG;YACjC,QAAQ,gBAAgB,GAAG;QAC7B,OAEK;YACH,IAAI,QAAQ,cAAc,cAAc;YACxC,QAAQ,MAAM,CAAC,aAAa,EAAE,OAAO;YACrC,QAAQ,kBAAkB,OAAO;YACjC,cAAc,cAAc,IAAI;QAClC;IACF;IACA,SAAS,QAAQ,SAAS,EAAE,OAAO,EAAE,QAAQ;QAC3C,IAAK,IAAI,IAAI,GAAG,IAAI,UAAU,IAAK;YACjC,UAAU,IAAI,CAAC,UAAU;QAC3B;IACF;IACA,SAAS,0BAA0B,YAAY;QAC7C,IAAI,UAAU,aAAa,OAAO;QAClC,OAAO,UAAU,QAAQ,MAAM,GAAG;IACpC;IACA,eAAe,MAAM,IAAI,CAAC,OAAO,QAAQ,GAAG,cAAc;IAC1D,iBAAiB,MAAM,IAAI,CAAC,OAAO,UAAU,GAAG,gBAAgB;IAChE,OAAO;AACT;AAMO,SAAS,6BAA6B,WAAW,EAAE,MAAM,EAAE,QAAQ;IACxE,IAAI,SAAS,CAAC;IACd,IAAI,eAAe,gCAAgC;IACnD,+EAA+E;IAC/E,IAAI,CAAC,cAAc;QACjB,OAAO;IACT;IACA,IAAI,eAAe,OAAO,YAAY;IACtC,IAAI,mBAAmB,OAAO,gBAAgB;IAC9C,IAAI;IACJ,IAAI,iBAAiB,+IAAA,CAAA,4BAAyB,IAAI,iBAAiB,+IAAA,CAAA,8BAA2B,EAAE;QAC9F,CAAA,GAAA,8IAAA,CAAA,OAAI,AAAD,EAAE,kBAAkB,SAAU,GAAG,EAAE,GAAG;YACvC,IAAI,CAAC,CAAA,GAAA,8IAAA,CAAA,WAAQ,AAAD,EAAE,OAAO,IAAI,IAAI,GAAG,GAAG,MAAM,QAAQ;gBAC/C,wBAAwB;YAC1B;QACF;IACF;IACA,IAAI,YAAY;QACd,IAAI,UAAU,CAAC;QACf,IAAI,UAAU,CAAC;QACf,IAAI,eAAe,EAAE;QACrB,4BAA4B;QAC5B,IAAK,IAAI,IAAI,GAAG,MAAM,KAAK,GAAG,CAAC,GAAG,WAAW,IAAI,KAAK,IAAK;YACzD,IAAI,cAAc,eAAe,OAAO,IAAI,EAAE,cAAc,OAAO,cAAc,EAAE,kBAAkB,OAAO,UAAU,EAAE;YACxH,aAAa,IAAI,CAAC;YAClB,IAAI,eAAe,gBAAgB,WAAW,GAAG;YACjD,yEAAyE;YACzE,8CAA8C;YAC9C,6EAA6E;YAC7E,IAAI,gBAAgB,QAAQ,CAAC,IAAI,QAAQ,MAAM,uBAAuB;gBACpE,QAAQ,CAAC,GAAG;YACd;YACA,IAAI,QAAQ,CAAC,IAAI,QAAQ,QAAQ,CAAC,KAAK,QAAQ,CAAC,IAAI,CAAC,gBAAgB,YAAY,CAAC,QAAQ,CAAC,CAAC,KAAK,WAAW,GAAG,EAAE;gBAC/G,QAAQ,CAAC,GAAG;YACd;YACA,IAAI,UAAU,YAAY,YAAY,CAAC,QAAQ,CAAC,CAAC,KAAK,WAAW,GAAG,EAAE;gBACpE,OAAO;YACT;YACA,gFAAgF;YAChF,oDAAoD;YACpD,8CAA8C;YAC9C,wCAAwC;YACxC,wEAAwE;YACxE,qBAAqB;YACrB,IAAI,CAAC,cAAc;gBACjB,IAAI,gBAAgB,WAAW,KAAK,IAAI,QAAQ,CAAC,IAAI,QAAQ,MAAM,uBAAuB;oBACxF,QAAQ,CAAC,GAAG;gBACd;gBACA,IAAI,QAAQ,CAAC,IAAI,QAAQ,QAAQ,CAAC,KAAK,QAAQ,CAAC,EAAE;oBAChD,QAAQ,CAAC,GAAG;gBACd;YACF;QACF;QACA,SAAS,UAAU,SAAS;YAC1B,OAAO,UAAU,CAAC,IAAI,QAAQ,UAAU,CAAC,IAAI;QAC/C;QACA,OAAO,UAAU,WAAW,UAAU,UAAU,WAAW,UAAU;IACvE;IACA,IAAI,WAAW;QACb,OAAO,KAAK,GAAG;YAAC,UAAU,CAAC;SAAC;QAC5B,gDAAgD;QAChD,IAAI,eAAe,yBAAyB,OAAO,wBAAwB,UAAU,CAAC;QACtF,6CAA6C;QAC7C,oCAAoC;QACpC,OAAO,QAAQ,GAAG;YAAC;SAAa;QAChC,OAAO,UAAU,GAAG;YAAC;SAAa;IACpC;IACA,OAAO;AACT;AAIO,SAAS,gCAAgC,WAAW;IACzD,kCAAkC;IAClC,yEAAyE;IACzE,4EAA4E;IAC5E,8EAA8E;IAC9E,+EAA+E;IAC/E,IAAI,WAAW,YAAY,GAAG,CAAC,QAAQ;IACvC,IAAI,CAAC,UAAU;QACb,OAAO,CAAA,GAAA,+IAAA,CAAA,2BAAwB,AAAD,EAAE,YAAY,OAAO,EAAE,WAAW;YAC9D,OAAO,YAAY,GAAG,CAAC,gBAAgB;YACvC,IAAI,YAAY,GAAG,CAAC,aAAa;QACnC,GAAG,+IAAA,CAAA,mBAAgB,EAAE,MAAM,CAAC,EAAE;IAChC;AACF;AAIO,SAAS,kCAAkC,YAAY;IAC5D,gFAAgF;IAChF,2BAA2B;IAC3B,IAAI,CAAC,aAAa,GAAG,CAAC,aAAa,SAAS,CAAC,aAAa,GAAG,CAAC,uBAAuB,OAAO;QAC1F,OAAO,EAAE;IACX;IACA,OAAO,CAAA,GAAA,+IAAA,CAAA,2BAAwB,AAAD,EAAE,aAAa,OAAO,EAAE,WAAW;QAC/D,OAAO,aAAa,GAAG,CAAC,oBAAoB;QAC5C,IAAI,aAAa,GAAG,CAAC,iBAAiB;IACxC,GAAG,+IAAA,CAAA,mBAAgB,EAAE,MAAM;AAC7B;AAMO,SAAS,aAAa,MAAM,EAAE,QAAQ;IAC3C,OAAO,eAAe,OAAO,IAAI,EAAE,OAAO,YAAY,EAAE,OAAO,cAAc,EAAE,OAAO,gBAAgB,EAAE,OAAO,UAAU,EAAE;AAC7H;AACA,wCAAwC;AACxC,sBAAsB;AACtB,SAAS,eAAe,IAAI,EAAE,YAAY,EAAE,cAAc,EAAE,gBAAgB,EAAE,UAAU,EAAE,QAAQ;IAChG,IAAI;IACJ,oBAAoB;IACpB,IAAI,UAAU;IACd,IAAI,CAAA,GAAA,8IAAA,CAAA,eAAY,AAAD,EAAE,OAAO;QACtB,OAAO,WAAW,GAAG;IACvB;IACA,sEAAsE;IACtE,2BAA2B;IAC3B,IAAI;IACJ,IAAI;IACJ,IAAI,kBAAkB;QACpB,IAAI,aAAa,gBAAgB,CAAC,SAAS;QAC3C,IAAI,CAAA,GAAA,8IAAA,CAAA,WAAQ,AAAD,EAAE,aAAa;YACxB,UAAU,WAAW,IAAI;YACzB,UAAU,WAAW,IAAI;QAC3B,OAAO,IAAI,CAAA,GAAA,8IAAA,CAAA,WAAQ,AAAD,EAAE,aAAa;YAC/B,UAAU;QACZ;IACF;IACA,IAAI,WAAW,MAAM;QACnB,OAAO,YAAY,YAAY,WAAW,IAAI,GAAG,WAAW,GAAG;IACjE;IACA,IAAI,iBAAiB,+IAAA,CAAA,2BAAwB,EAAE;QAC7C,IAAI,gBAAgB;QACpB,IAAI,mBAAmB,+IAAA,CAAA,uBAAoB,EAAE;YAC3C,IAAI,SAAS,aAAa,CAAC,SAAS;YACpC,IAAK,IAAI,IAAI,GAAG,IAAI,CAAC,UAAU,EAAE,EAAE,MAAM,IAAI,IAAI,SAAS,IAAK;gBAC7D,IAAI,CAAC,SAAS,YAAY,MAAM,CAAC,aAAa,EAAE,CAAC,KAAK,MAAM;oBAC1D,OAAO;gBACT;YACF;QACF,OAAO;YACL,IAAK,IAAI,IAAI,GAAG,IAAI,cAAc,MAAM,IAAI,IAAI,SAAS,IAAK;gBAC5D,IAAI,MAAM,aAAa,CAAC,aAAa,EAAE;gBACvC,IAAI,OAAO,CAAC,SAAS,YAAY,GAAG,CAAC,SAAS,CAAC,KAAK,MAAM;oBACxD,OAAO;gBACT;YACF;QACF;IACF,OAAO,IAAI,iBAAiB,+IAAA,CAAA,4BAAyB,EAAE;QACrD,IAAI,iBAAiB;QACrB,IAAI,CAAC,SAAS;YACZ,OAAO,WAAW,GAAG;QACvB;QACA,IAAK,IAAI,IAAI,GAAG,IAAI,eAAe,MAAM,IAAI,IAAI,SAAS,IAAK;YAC7D,IAAI,OAAO,cAAc,CAAC,EAAE;YAC5B,IAAI,QAAQ,CAAC,SAAS,YAAY,IAAI,CAAC,QAAQ,CAAC,KAAK,MAAM;gBACzD,OAAO;YACT;QACF;IACF,OAAO,IAAI,iBAAiB,+IAAA,CAAA,8BAA2B,EAAE;QACvD,IAAI,mBAAmB;QACvB,IAAI,CAAC,SAAS;YACZ,OAAO,WAAW,GAAG;QACvB;QACA,IAAI,SAAS,gBAAgB,CAAC,QAAQ;QACtC,IAAI,CAAC,UAAU,CAAA,GAAA,8IAAA,CAAA,eAAY,AAAD,EAAE,SAAS;YACnC,OAAO,WAAW,GAAG;QACvB;QACA,IAAK,IAAI,IAAI,GAAG,IAAI,OAAO,MAAM,IAAI,IAAI,SAAS,IAAK;YACrD,IAAI,CAAC,SAAS,YAAY,MAAM,CAAC,EAAE,CAAC,KAAK,MAAM;gBAC7C,OAAO;YACT;QACF;IACF,OAAO,IAAI,iBAAiB,+IAAA,CAAA,yBAAsB,EAAE;QAClD,IAAI,eAAe;QACnB,IAAK,IAAI,IAAI,GAAG,IAAI,aAAa,MAAM,IAAI,IAAI,SAAS,IAAK;YAC3D,IAAI,OAAO,YAAY,CAAC,EAAE;YAC1B,IAAI,MAAM,CAAA,GAAA,+IAAA,CAAA,mBAAgB,AAAD,EAAE;YAC3B,IAAI,CAAC,CAAA,GAAA,8IAAA,CAAA,UAAO,AAAD,EAAE,MAAM;gBACjB,OAAO,WAAW,GAAG;YACvB;YACA,IAAI,CAAC,SAAS,YAAY,GAAG,CAAC,SAAS,CAAC,KAAK,MAAM;gBACjD,OAAO;YACT;QACF;IACF;IACA,SAAS,YAAY,GAAG;QACtB,IAAI,QAAQ,CAAA,GAAA,8IAAA,CAAA,WAAQ,AAAD,EAAE;QACrB,oEAAoE;QACpE,2CAA2C;QAC3C,IAAI,OAAO,QAAQ,OAAO,QAAQ,CAAC,OAAO,SAAS,QAAQ,IAAI;YAC7D,OAAO,QAAQ,WAAW,KAAK,GAAG,WAAW,GAAG;QAClD,OAAO,IAAI,SAAS,QAAQ,KAAK;YAC/B,OAAO,WAAW,IAAI;QACxB;IACF;IACA,OAAO,WAAW,GAAG;AACvB", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 338, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/tindahan/sari-sari-admin/node_modules/echarts/lib/data/Source.js"], "sourcesContent": ["\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\r\n* Licensed to the Apache Software Foundation (ASF) under one\r\n* or more contributor license agreements.  See the NOTICE file\r\n* distributed with this work for additional information\r\n* regarding copyright ownership.  The ASF licenses this file\r\n* to you under the Apache License, Version 2.0 (the\r\n* \"License\"); you may not use this file except in compliance\r\n* with the License.  You may obtain a copy of the License at\r\n*\r\n*   http://www.apache.org/licenses/LICENSE-2.0\r\n*\r\n* Unless required by applicable law or agreed to in writing,\r\n* software distributed under the License is distributed on an\r\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\r\n* KIND, either express or implied.  See the License for the\r\n* specific language governing permissions and limitations\r\n* under the License.\r\n*/\nimport { isTypedArray, clone, createHashMap, isArray, isObject, isArrayLike, hasOwn, assert, each, map, isNumber, isString, keys } from 'zrender/lib/core/util.js';\nimport { SOURCE_FORMAT_ORIGINAL, SERIES_LAYOUT_BY_COLUMN, SOURCE_FORMAT_UNKNOWN, SOURCE_FORMAT_KEYED_COLUMNS, SOURCE_FORMAT_TYPED_ARRAY, SOURCE_FORMAT_ARRAY_ROWS, SOURCE_FORMAT_OBJECT_ROWS, SERIES_LAYOUT_BY_ROW } from '../util/types.js';\nimport { getDataItemValue } from '../util/model.js';\nimport { BE_ORDINAL, guessOrdinal } from './helper/sourceHelper.js';\n;\n// @inner\nvar SourceImpl = /** @class */function () {\n  function SourceImpl(fields) {\n    this.data = fields.data || (fields.sourceFormat === SOURCE_FORMAT_KEYED_COLUMNS ? {} : []);\n    this.sourceFormat = fields.sourceFormat || SOURCE_FORMAT_UNKNOWN;\n    // Visit config\n    this.seriesLayoutBy = fields.seriesLayoutBy || SERIES_LAYOUT_BY_COLUMN;\n    this.startIndex = fields.startIndex || 0;\n    this.dimensionsDetectedCount = fields.dimensionsDetectedCount;\n    this.metaRawOption = fields.metaRawOption;\n    var dimensionsDefine = this.dimensionsDefine = fields.dimensionsDefine;\n    if (dimensionsDefine) {\n      for (var i = 0; i < dimensionsDefine.length; i++) {\n        var dim = dimensionsDefine[i];\n        if (dim.type == null) {\n          if (guessOrdinal(this, i) === BE_ORDINAL.Must) {\n            dim.type = 'ordinal';\n          }\n        }\n      }\n    }\n  }\n  return SourceImpl;\n}();\nexport function isSourceInstance(val) {\n  return val instanceof SourceImpl;\n}\n/**\r\n * Create a source from option.\r\n * NOTE: Created source is immutable. Don't change any properties in it.\r\n */\nexport function createSource(sourceData, thisMetaRawOption,\n// can be null. If not provided, auto detect it from `sourceData`.\nsourceFormat) {\n  sourceFormat = sourceFormat || detectSourceFormat(sourceData);\n  var seriesLayoutBy = thisMetaRawOption.seriesLayoutBy;\n  var determined = determineSourceDimensions(sourceData, sourceFormat, seriesLayoutBy, thisMetaRawOption.sourceHeader, thisMetaRawOption.dimensions);\n  var source = new SourceImpl({\n    data: sourceData,\n    sourceFormat: sourceFormat,\n    seriesLayoutBy: seriesLayoutBy,\n    dimensionsDefine: determined.dimensionsDefine,\n    startIndex: determined.startIndex,\n    dimensionsDetectedCount: determined.dimensionsDetectedCount,\n    metaRawOption: clone(thisMetaRawOption)\n  });\n  return source;\n}\n/**\r\n * Wrap original series data for some compatibility cases.\r\n */\nexport function createSourceFromSeriesDataOption(data) {\n  return new SourceImpl({\n    data: data,\n    sourceFormat: isTypedArray(data) ? SOURCE_FORMAT_TYPED_ARRAY : SOURCE_FORMAT_ORIGINAL\n  });\n}\n/**\r\n * Clone source but excludes source data.\r\n */\nexport function cloneSourceShallow(source) {\n  return new SourceImpl({\n    data: source.data,\n    sourceFormat: source.sourceFormat,\n    seriesLayoutBy: source.seriesLayoutBy,\n    dimensionsDefine: clone(source.dimensionsDefine),\n    startIndex: source.startIndex,\n    dimensionsDetectedCount: source.dimensionsDetectedCount\n  });\n}\n/**\r\n * Note: An empty array will be detected as `SOURCE_FORMAT_ARRAY_ROWS`.\r\n */\nexport function detectSourceFormat(data) {\n  var sourceFormat = SOURCE_FORMAT_UNKNOWN;\n  if (isTypedArray(data)) {\n    sourceFormat = SOURCE_FORMAT_TYPED_ARRAY;\n  } else if (isArray(data)) {\n    // FIXME Whether tolerate null in top level array?\n    if (data.length === 0) {\n      sourceFormat = SOURCE_FORMAT_ARRAY_ROWS;\n    }\n    for (var i = 0, len = data.length; i < len; i++) {\n      var item = data[i];\n      if (item == null) {\n        continue;\n      } else if (isArray(item) || isTypedArray(item)) {\n        sourceFormat = SOURCE_FORMAT_ARRAY_ROWS;\n        break;\n      } else if (isObject(item)) {\n        sourceFormat = SOURCE_FORMAT_OBJECT_ROWS;\n        break;\n      }\n    }\n  } else if (isObject(data)) {\n    for (var key in data) {\n      if (hasOwn(data, key) && isArrayLike(data[key])) {\n        sourceFormat = SOURCE_FORMAT_KEYED_COLUMNS;\n        break;\n      }\n    }\n  }\n  return sourceFormat;\n}\n/**\r\n * Determine the source definitions from data standalone dimensions definitions\r\n * are not specified.\r\n */\nfunction determineSourceDimensions(data, sourceFormat, seriesLayoutBy, sourceHeader,\n// standalone raw dimensions definition, like:\n// {\n//     dimensions: ['aa', 'bb', { name: 'cc', type: 'time' }]\n// }\n// in `dataset` or `series`\ndimensionsDefine) {\n  var dimensionsDetectedCount;\n  var startIndex;\n  // PENDING: Could data be null/undefined here?\n  // currently, if `dataset.source` not specified, error thrown.\n  // if `series.data` not specified, nothing rendered without error thrown.\n  // Should test these cases.\n  if (!data) {\n    return {\n      dimensionsDefine: normalizeDimensionsOption(dimensionsDefine),\n      startIndex: startIndex,\n      dimensionsDetectedCount: dimensionsDetectedCount\n    };\n  }\n  if (sourceFormat === SOURCE_FORMAT_ARRAY_ROWS) {\n    var dataArrayRows = data;\n    // Rule: Most of the first line are string: it is header.\n    // Caution: consider a line with 5 string and 1 number,\n    // it still can not be sure it is a head, because the\n    // 5 string may be 5 values of category columns.\n    if (sourceHeader === 'auto' || sourceHeader == null) {\n      arrayRowsTravelFirst(function (val) {\n        // '-' is regarded as null/undefined.\n        if (val != null && val !== '-') {\n          if (isString(val)) {\n            startIndex == null && (startIndex = 1);\n          } else {\n            startIndex = 0;\n          }\n        }\n        // 10 is an experience number, avoid long loop.\n      }, seriesLayoutBy, dataArrayRows, 10);\n    } else {\n      startIndex = isNumber(sourceHeader) ? sourceHeader : sourceHeader ? 1 : 0;\n    }\n    if (!dimensionsDefine && startIndex === 1) {\n      dimensionsDefine = [];\n      arrayRowsTravelFirst(function (val, index) {\n        dimensionsDefine[index] = val != null ? val + '' : '';\n      }, seriesLayoutBy, dataArrayRows, Infinity);\n    }\n    dimensionsDetectedCount = dimensionsDefine ? dimensionsDefine.length : seriesLayoutBy === SERIES_LAYOUT_BY_ROW ? dataArrayRows.length : dataArrayRows[0] ? dataArrayRows[0].length : null;\n  } else if (sourceFormat === SOURCE_FORMAT_OBJECT_ROWS) {\n    if (!dimensionsDefine) {\n      dimensionsDefine = objectRowsCollectDimensions(data);\n    }\n  } else if (sourceFormat === SOURCE_FORMAT_KEYED_COLUMNS) {\n    if (!dimensionsDefine) {\n      dimensionsDefine = [];\n      each(data, function (colArr, key) {\n        dimensionsDefine.push(key);\n      });\n    }\n  } else if (sourceFormat === SOURCE_FORMAT_ORIGINAL) {\n    var value0 = getDataItemValue(data[0]);\n    dimensionsDetectedCount = isArray(value0) && value0.length || 1;\n  } else if (sourceFormat === SOURCE_FORMAT_TYPED_ARRAY) {\n    if (process.env.NODE_ENV !== 'production') {\n      assert(!!dimensionsDefine, 'dimensions must be given if data is TypedArray.');\n    }\n  }\n  return {\n    startIndex: startIndex,\n    dimensionsDefine: normalizeDimensionsOption(dimensionsDefine),\n    dimensionsDetectedCount: dimensionsDetectedCount\n  };\n}\nfunction objectRowsCollectDimensions(data) {\n  var firstIndex = 0;\n  var obj;\n  while (firstIndex < data.length && !(obj = data[firstIndex++])) {} // jshint ignore: line\n  if (obj) {\n    return keys(obj);\n  }\n}\n// Consider dimensions defined like ['A', 'price', 'B', 'price', 'C', 'price'],\n// which is reasonable. But dimension name is duplicated.\n// Returns undefined or an array contains only object without null/undefined or string.\nfunction normalizeDimensionsOption(dimensionsDefine) {\n  if (!dimensionsDefine) {\n    // The meaning of null/undefined is different from empty array.\n    return;\n  }\n  var nameMap = createHashMap();\n  return map(dimensionsDefine, function (rawItem, index) {\n    rawItem = isObject(rawItem) ? rawItem : {\n      name: rawItem\n    };\n    // Other fields will be discarded.\n    var item = {\n      name: rawItem.name,\n      displayName: rawItem.displayName,\n      type: rawItem.type\n    };\n    // User can set null in dimensions.\n    // We don't auto specify name, otherwise a given name may\n    // cause it to be referred unexpectedly.\n    if (item.name == null) {\n      return item;\n    }\n    // Also consider number form like 2012.\n    item.name += '';\n    // User may also specify displayName.\n    // displayName will always exists except user not\n    // specified or dim name is not specified or detected.\n    // (A auto generated dim name will not be used as\n    // displayName).\n    if (item.displayName == null) {\n      item.displayName = item.name;\n    }\n    var exist = nameMap.get(item.name);\n    if (!exist) {\n      nameMap.set(item.name, {\n        count: 1\n      });\n    } else {\n      item.name += '-' + exist.count++;\n    }\n    return item;\n  });\n}\nfunction arrayRowsTravelFirst(cb, seriesLayoutBy, data, maxLoop) {\n  if (seriesLayoutBy === SERIES_LAYOUT_BY_ROW) {\n    for (var i = 0; i < data.length && i < maxLoop; i++) {\n      cb(data[i] ? data[i][0] : null, i);\n    }\n  } else {\n    var value0 = data[0] || [];\n    for (var i = 0; i < value0.length && i < maxLoop; i++) {\n      cb(value0[i], i);\n    }\n  }\n}\nexport function shouldRetrieveDataByName(source) {\n  var sourceFormat = source.sourceFormat;\n  return sourceFormat === SOURCE_FORMAT_OBJECT_ROWS || sourceFormat === SOURCE_FORMAT_KEYED_COLUMNS;\n}"], "names": [], "mappings": "AACA;;;;;;;;;;;;;;;;;AAiBA,GAGA;;CAEC,GAED;;;;;;;;;;;;;;;;;AAiBA;;;;;;;;AACA;AACA;AACA;AACA;;;;;;AAEA,SAAS;AACT,IAAI,aAAa,WAAW,GAAE;IAC5B,SAAS,WAAW,MAAM;QACxB,IAAI,CAAC,IAAI,GAAG,OAAO,IAAI,IAAI,CAAC,OAAO,YAAY,KAAK,+IAAA,CAAA,8BAA2B,GAAG,CAAC,IAAI,EAAE;QACzF,IAAI,CAAC,YAAY,GAAG,OAAO,YAAY,IAAI,+IAAA,CAAA,wBAAqB;QAChE,eAAe;QACf,IAAI,CAAC,cAAc,GAAG,OAAO,cAAc,IAAI,+IAAA,CAAA,0BAAuB;QACtE,IAAI,CAAC,UAAU,GAAG,OAAO,UAAU,IAAI;QACvC,IAAI,CAAC,uBAAuB,GAAG,OAAO,uBAAuB;QAC7D,IAAI,CAAC,aAAa,GAAG,OAAO,aAAa;QACzC,IAAI,mBAAmB,IAAI,CAAC,gBAAgB,GAAG,OAAO,gBAAgB;QACtE,IAAI,kBAAkB;YACpB,IAAK,IAAI,IAAI,GAAG,IAAI,iBAAiB,MAAM,EAAE,IAAK;gBAChD,IAAI,MAAM,gBAAgB,CAAC,EAAE;gBAC7B,IAAI,IAAI,IAAI,IAAI,MAAM;oBACpB,IAAI,CAAA,GAAA,gKAAA,CAAA,eAAY,AAAD,EAAE,IAAI,EAAE,OAAO,gKAAA,CAAA,aAAU,CAAC,IAAI,EAAE;wBAC7C,IAAI,IAAI,GAAG;oBACb;gBACF;YACF;QACF;IACF;IACA,OAAO;AACT;AACO,SAAS,iBAAiB,GAAG;IAClC,OAAO,eAAe;AACxB;AAKO,SAAS,aAAa,UAAU,EAAE,iBAAiB,EAC1D,kEAAkE;AAClE,YAAY;IACV,eAAe,gBAAgB,mBAAmB;IAClD,IAAI,iBAAiB,kBAAkB,cAAc;IACrD,IAAI,aAAa,0BAA0B,YAAY,cAAc,gBAAgB,kBAAkB,YAAY,EAAE,kBAAkB,UAAU;IACjJ,IAAI,SAAS,IAAI,WAAW;QAC1B,MAAM;QACN,cAAc;QACd,gBAAgB;QAChB,kBAAkB,WAAW,gBAAgB;QAC7C,YAAY,WAAW,UAAU;QACjC,yBAAyB,WAAW,uBAAuB;QAC3D,eAAe,CAAA,GAAA,8IAAA,CAAA,QAAK,AAAD,EAAE;IACvB;IACA,OAAO;AACT;AAIO,SAAS,iCAAiC,IAAI;IACnD,OAAO,IAAI,WAAW;QACpB,MAAM;QACN,cAAc,CAAA,GAAA,8IAAA,CAAA,eAAY,AAAD,EAAE,QAAQ,+IAAA,CAAA,4BAAyB,GAAG,+IAAA,CAAA,yBAAsB;IACvF;AACF;AAIO,SAAS,mBAAmB,MAAM;IACvC,OAAO,IAAI,WAAW;QACpB,MAAM,OAAO,IAAI;QACjB,cAAc,OAAO,YAAY;QACjC,gBAAgB,OAAO,cAAc;QACrC,kBAAkB,CAAA,GAAA,8IAAA,CAAA,QAAK,AAAD,EAAE,OAAO,gBAAgB;QAC/C,YAAY,OAAO,UAAU;QAC7B,yBAAyB,OAAO,uBAAuB;IACzD;AACF;AAIO,SAAS,mBAAmB,IAAI;IACrC,IAAI,eAAe,+IAAA,CAAA,wBAAqB;IACxC,IAAI,CAAA,GAAA,8IAAA,CAAA,eAAY,AAAD,EAAE,OAAO;QACtB,eAAe,+IAAA,CAAA,4BAAyB;IAC1C,OAAO,IAAI,CAAA,GAAA,8IAAA,CAAA,UAAO,AAAD,EAAE,OAAO;QACxB,kDAAkD;QAClD,IAAI,KAAK,MAAM,KAAK,GAAG;YACrB,eAAe,+IAAA,CAAA,2BAAwB;QACzC;QACA,IAAK,IAAI,IAAI,GAAG,MAAM,KAAK,MAAM,EAAE,IAAI,KAAK,IAAK;YAC/C,IAAI,OAAO,IAAI,CAAC,EAAE;YAClB,IAAI,QAAQ,MAAM;gBAChB;YACF,OAAO,IAAI,CAAA,GAAA,8IAAA,CAAA,UAAO,AAAD,EAAE,SAAS,CAAA,GAAA,8IAAA,CAAA,eAAY,AAAD,EAAE,OAAO;gBAC9C,eAAe,+IAAA,CAAA,2BAAwB;gBACvC;YACF,OAAO,IAAI,CAAA,GAAA,8IAAA,CAAA,WAAQ,AAAD,EAAE,OAAO;gBACzB,eAAe,+IAAA,CAAA,4BAAyB;gBACxC;YACF;QACF;IACF,OAAO,IAAI,CAAA,GAAA,8IAAA,CAAA,WAAQ,AAAD,EAAE,OAAO;QACzB,IAAK,IAAI,OAAO,KAAM;YACpB,IAAI,CAAA,GAAA,8IAAA,CAAA,SAAM,AAAD,EAAE,MAAM,QAAQ,CAAA,GAAA,8IAAA,CAAA,cAAW,AAAD,EAAE,IAAI,CAAC,IAAI,GAAG;gBAC/C,eAAe,+IAAA,CAAA,8BAA2B;gBAC1C;YACF;QACF;IACF;IACA,OAAO;AACT;AACA;;;CAGC,GACD,SAAS,0BAA0B,IAAI,EAAE,YAAY,EAAE,cAAc,EAAE,YAAY,EACnF,8CAA8C;AAC9C,IAAI;AACJ,6DAA6D;AAC7D,IAAI;AACJ,2BAA2B;AAC3B,gBAAgB;IACd,IAAI;IACJ,IAAI;IACJ,8CAA8C;IAC9C,8DAA8D;IAC9D,yEAAyE;IACzE,2BAA2B;IAC3B,IAAI,CAAC,MAAM;QACT,OAAO;YACL,kBAAkB,0BAA0B;YAC5C,YAAY;YACZ,yBAAyB;QAC3B;IACF;IACA,IAAI,iBAAiB,+IAAA,CAAA,2BAAwB,EAAE;QAC7C,IAAI,gBAAgB;QACpB,yDAAyD;QACzD,uDAAuD;QACvD,qDAAqD;QACrD,gDAAgD;QAChD,IAAI,iBAAiB,UAAU,gBAAgB,MAAM;YACnD,qBAAqB,SAAU,GAAG;gBAChC,qCAAqC;gBACrC,IAAI,OAAO,QAAQ,QAAQ,KAAK;oBAC9B,IAAI,CAAA,GAAA,8IAAA,CAAA,WAAQ,AAAD,EAAE,MAAM;wBACjB,cAAc,QAAQ,CAAC,aAAa,CAAC;oBACvC,OAAO;wBACL,aAAa;oBACf;gBACF;YACA,+CAA+C;YACjD,GAAG,gBAAgB,eAAe;QACpC,OAAO;YACL,aAAa,CAAA,GAAA,8IAAA,CAAA,WAAQ,AAAD,EAAE,gBAAgB,eAAe,eAAe,IAAI;QAC1E;QACA,IAAI,CAAC,oBAAoB,eAAe,GAAG;YACzC,mBAAmB,EAAE;YACrB,qBAAqB,SAAU,GAAG,EAAE,KAAK;gBACvC,gBAAgB,CAAC,MAAM,GAAG,OAAO,OAAO,MAAM,KAAK;YACrD,GAAG,gBAAgB,eAAe;QACpC;QACA,0BAA0B,mBAAmB,iBAAiB,MAAM,GAAG,mBAAmB,+IAAA,CAAA,uBAAoB,GAAG,cAAc,MAAM,GAAG,aAAa,CAAC,EAAE,GAAG,aAAa,CAAC,EAAE,CAAC,MAAM,GAAG;IACvL,OAAO,IAAI,iBAAiB,+IAAA,CAAA,4BAAyB,EAAE;QACrD,IAAI,CAAC,kBAAkB;YACrB,mBAAmB,4BAA4B;QACjD;IACF,OAAO,IAAI,iBAAiB,+IAAA,CAAA,8BAA2B,EAAE;QACvD,IAAI,CAAC,kBAAkB;YACrB,mBAAmB,EAAE;YACrB,CAAA,GAAA,8IAAA,CAAA,OAAI,AAAD,EAAE,MAAM,SAAU,MAAM,EAAE,GAAG;gBAC9B,iBAAiB,IAAI,CAAC;YACxB;QACF;IACF,OAAO,IAAI,iBAAiB,+IAAA,CAAA,yBAAsB,EAAE;QAClD,IAAI,SAAS,CAAA,GAAA,+IAAA,CAAA,mBAAgB,AAAD,EAAE,IAAI,CAAC,EAAE;QACrC,0BAA0B,CAAA,GAAA,8IAAA,CAAA,UAAO,AAAD,EAAE,WAAW,OAAO,MAAM,IAAI;IAChE,OAAO,IAAI,iBAAiB,+IAAA,CAAA,4BAAyB,EAAE;QACrD,wCAA2C;YACzC,CAAA,GAAA,8IAAA,CAAA,SAAM,AAAD,EAAE,CAAC,CAAC,kBAAkB;QAC7B;IACF;IACA,OAAO;QACL,YAAY;QACZ,kBAAkB,0BAA0B;QAC5C,yBAAyB;IAC3B;AACF;AACA,SAAS,4BAA4B,IAAI;IACvC,IAAI,aAAa;IACjB,IAAI;IACJ,MAAO,aAAa,KAAK,MAAM,IAAI,CAAC,CAAC,MAAM,IAAI,CAAC,aAAa,EAAG,CAAC,EAAE,sBAAsB;IACzF,IAAI,KAAK;QACP,OAAO,CAAA,GAAA,8IAAA,CAAA,OAAI,AAAD,EAAE;IACd;AACF;AACA,+EAA+E;AAC/E,yDAAyD;AACzD,uFAAuF;AACvF,SAAS,0BAA0B,gBAAgB;IACjD,IAAI,CAAC,kBAAkB;QACrB,+DAA+D;QAC/D;IACF;IACA,IAAI,UAAU,CAAA,GAAA,8IAAA,CAAA,gBAAa,AAAD;IAC1B,OAAO,CAAA,GAAA,8IAAA,CAAA,MAAG,AAAD,EAAE,kBAAkB,SAAU,OAAO,EAAE,KAAK;QACnD,UAAU,CAAA,GAAA,8IAAA,CAAA,WAAQ,AAAD,EAAE,WAAW,UAAU;YACtC,MAAM;QACR;QACA,kCAAkC;QAClC,IAAI,OAAO;YACT,MAAM,QAAQ,IAAI;YAClB,aAAa,QAAQ,WAAW;YAChC,MAAM,QAAQ,IAAI;QACpB;QACA,mCAAmC;QACnC,yDAAyD;QACzD,wCAAwC;QACxC,IAAI,KAAK,IAAI,IAAI,MAAM;YACrB,OAAO;QACT;QACA,uCAAuC;QACvC,KAAK,IAAI,IAAI;QACb,qCAAqC;QACrC,iDAAiD;QACjD,sDAAsD;QACtD,iDAAiD;QACjD,gBAAgB;QAChB,IAAI,KAAK,WAAW,IAAI,MAAM;YAC5B,KAAK,WAAW,GAAG,KAAK,IAAI;QAC9B;QACA,IAAI,QAAQ,QAAQ,GAAG,CAAC,KAAK,IAAI;QACjC,IAAI,CAAC,OAAO;YACV,QAAQ,GAAG,CAAC,KAAK,IAAI,EAAE;gBACrB,OAAO;YACT;QACF,OAAO;YACL,KAAK,IAAI,IAAI,MAAM,MAAM,KAAK;QAChC;QACA,OAAO;IACT;AACF;AACA,SAAS,qBAAqB,EAAE,EAAE,cAAc,EAAE,IAAI,EAAE,OAAO;IAC7D,IAAI,mBAAmB,+IAAA,CAAA,uBAAoB,EAAE;QAC3C,IAAK,IAAI,IAAI,GAAG,IAAI,KAAK,MAAM,IAAI,IAAI,SAAS,IAAK;YACnD,GAAG,IAAI,CAAC,EAAE,GAAG,IAAI,CAAC,EAAE,CAAC,EAAE,GAAG,MAAM;QAClC;IACF,OAAO;QACL,IAAI,SAAS,IAAI,CAAC,EAAE,IAAI,EAAE;QAC1B,IAAK,IAAI,IAAI,GAAG,IAAI,OAAO,MAAM,IAAI,IAAI,SAAS,IAAK;YACrD,GAAG,MAAM,CAAC,EAAE,EAAE;QAChB;IACF;AACF;AACO,SAAS,yBAAyB,MAAM;IAC7C,IAAI,eAAe,OAAO,YAAY;IACtC,OAAO,iBAAiB,+IAAA,CAAA,4BAAyB,IAAI,iBAAiB,+IAAA,CAAA,8BAA2B;AACnG", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 632, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/tindahan/sari-sari-admin/node_modules/echarts/lib/data/helper/dataProvider.js"], "sourcesContent": ["\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\r\n* Licensed to the Apache Software Foundation (ASF) under one\r\n* or more contributor license agreements.  See the NOTICE file\r\n* distributed with this work for additional information\r\n* regarding copyright ownership.  The ASF licenses this file\r\n* to you under the Apache License, Version 2.0 (the\r\n* \"License\"); you may not use this file except in compliance\r\n* with the License.  You may obtain a copy of the License at\r\n*\r\n*   http://www.apache.org/licenses/LICENSE-2.0\r\n*\r\n* Unless required by applicable law or agreed to in writing,\r\n* software distributed under the License is distributed on an\r\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\r\n* KIND, either express or implied.  See the License for the\r\n* specific language governing permissions and limitations\r\n* under the License.\r\n*/\nvar _a, _b, _c;\n// TODO\n// ??? refactor? check the outer usage of data provider.\n// merge with defaultDimValueGetter?\nimport { isTypedArray, extend, assert, each, isObject, bind } from 'zrender/lib/core/util.js';\nimport { getDataItemValue } from '../../util/model.js';\nimport { createSourceFromSeriesDataOption, isSourceInstance } from '../Source.js';\nimport { SOURCE_FORMAT_ORIGINAL, SOURCE_FORMAT_OBJECT_ROWS, SOURCE_FORMAT_KEYED_COLUMNS, SOURCE_FORMAT_TYPED_ARRAY, SOURCE_FORMAT_ARRAY_ROWS, SERIES_LAYOUT_BY_COLUMN, SERIES_LAYOUT_BY_ROW } from '../../util/types.js';\nvar providerMethods;\nvar mountMethods;\n/**\r\n * If normal array used, mutable chunk size is supported.\r\n * If typed array used, chunk size must be fixed.\r\n */\nvar DefaultDataProvider = /** @class */function () {\n  function DefaultDataProvider(sourceParam, dimSize) {\n    // let source: Source;\n    var source = !isSourceInstance(sourceParam) ? createSourceFromSeriesDataOption(sourceParam) : sourceParam;\n    // declare source is Source;\n    this._source = source;\n    var data = this._data = source.data;\n    // Typed array. TODO IE10+?\n    if (source.sourceFormat === SOURCE_FORMAT_TYPED_ARRAY) {\n      if (process.env.NODE_ENV !== 'production') {\n        if (dimSize == null) {\n          throw new Error('Typed array data must specify dimension size');\n        }\n      }\n      this._offset = 0;\n      this._dimSize = dimSize;\n      this._data = data;\n    }\n    mountMethods(this, data, source);\n  }\n  DefaultDataProvider.prototype.getSource = function () {\n    return this._source;\n  };\n  DefaultDataProvider.prototype.count = function () {\n    return 0;\n  };\n  DefaultDataProvider.prototype.getItem = function (idx, out) {\n    return;\n  };\n  DefaultDataProvider.prototype.appendData = function (newData) {};\n  DefaultDataProvider.prototype.clean = function () {};\n  DefaultDataProvider.protoInitialize = function () {\n    // PENDING: To avoid potential incompat (e.g., prototype\n    // is visited somewhere), still init them on prototype.\n    var proto = DefaultDataProvider.prototype;\n    proto.pure = false;\n    proto.persistent = true;\n  }();\n  DefaultDataProvider.internalField = function () {\n    var _a;\n    mountMethods = function (provider, data, source) {\n      var sourceFormat = source.sourceFormat;\n      var seriesLayoutBy = source.seriesLayoutBy;\n      var startIndex = source.startIndex;\n      var dimsDef = source.dimensionsDefine;\n      var methods = providerMethods[getMethodMapKey(sourceFormat, seriesLayoutBy)];\n      if (process.env.NODE_ENV !== 'production') {\n        assert(methods, 'Invalide sourceFormat: ' + sourceFormat);\n      }\n      extend(provider, methods);\n      if (sourceFormat === SOURCE_FORMAT_TYPED_ARRAY) {\n        provider.getItem = getItemForTypedArray;\n        provider.count = countForTypedArray;\n        provider.fillStorage = fillStorageForTypedArray;\n      } else {\n        var rawItemGetter = getRawSourceItemGetter(sourceFormat, seriesLayoutBy);\n        provider.getItem = bind(rawItemGetter, null, data, startIndex, dimsDef);\n        var rawCounter = getRawSourceDataCounter(sourceFormat, seriesLayoutBy);\n        provider.count = bind(rawCounter, null, data, startIndex, dimsDef);\n      }\n    };\n    var getItemForTypedArray = function (idx, out) {\n      idx = idx - this._offset;\n      out = out || [];\n      var data = this._data;\n      var dimSize = this._dimSize;\n      var offset = dimSize * idx;\n      for (var i = 0; i < dimSize; i++) {\n        out[i] = data[offset + i];\n      }\n      return out;\n    };\n    var fillStorageForTypedArray = function (start, end, storage, extent) {\n      var data = this._data;\n      var dimSize = this._dimSize;\n      for (var dim = 0; dim < dimSize; dim++) {\n        var dimExtent = extent[dim];\n        var min = dimExtent[0] == null ? Infinity : dimExtent[0];\n        var max = dimExtent[1] == null ? -Infinity : dimExtent[1];\n        var count = end - start;\n        var arr = storage[dim];\n        for (var i = 0; i < count; i++) {\n          // appendData with TypedArray will always do replace in provider.\n          var val = data[i * dimSize + dim];\n          arr[start + i] = val;\n          val < min && (min = val);\n          val > max && (max = val);\n        }\n        dimExtent[0] = min;\n        dimExtent[1] = max;\n      }\n    };\n    var countForTypedArray = function () {\n      return this._data ? this._data.length / this._dimSize : 0;\n    };\n    providerMethods = (_a = {}, _a[SOURCE_FORMAT_ARRAY_ROWS + '_' + SERIES_LAYOUT_BY_COLUMN] = {\n      pure: true,\n      appendData: appendDataSimply\n    }, _a[SOURCE_FORMAT_ARRAY_ROWS + '_' + SERIES_LAYOUT_BY_ROW] = {\n      pure: true,\n      appendData: function () {\n        throw new Error('Do not support appendData when set seriesLayoutBy: \"row\".');\n      }\n    }, _a[SOURCE_FORMAT_OBJECT_ROWS] = {\n      pure: true,\n      appendData: appendDataSimply\n    }, _a[SOURCE_FORMAT_KEYED_COLUMNS] = {\n      pure: true,\n      appendData: function (newData) {\n        var data = this._data;\n        each(newData, function (newCol, key) {\n          var oldCol = data[key] || (data[key] = []);\n          for (var i = 0; i < (newCol || []).length; i++) {\n            oldCol.push(newCol[i]);\n          }\n        });\n      }\n    }, _a[SOURCE_FORMAT_ORIGINAL] = {\n      appendData: appendDataSimply\n    }, _a[SOURCE_FORMAT_TYPED_ARRAY] = {\n      persistent: false,\n      pure: true,\n      appendData: function (newData) {\n        if (process.env.NODE_ENV !== 'production') {\n          assert(isTypedArray(newData), 'Added data must be TypedArray if data in initialization is TypedArray');\n        }\n        this._data = newData;\n      },\n      // Clean self if data is already used.\n      clean: function () {\n        // PENDING\n        this._offset += this.count();\n        this._data = null;\n      }\n    }, _a);\n    function appendDataSimply(newData) {\n      for (var i = 0; i < newData.length; i++) {\n        this._data.push(newData[i]);\n      }\n    }\n  }();\n  return DefaultDataProvider;\n}();\nexport { DefaultDataProvider };\nvar getItemSimply = function (rawData, startIndex, dimsDef, idx) {\n  return rawData[idx];\n};\nvar rawSourceItemGetterMap = (_a = {}, _a[SOURCE_FORMAT_ARRAY_ROWS + '_' + SERIES_LAYOUT_BY_COLUMN] = function (rawData, startIndex, dimsDef, idx) {\n  return rawData[idx + startIndex];\n}, _a[SOURCE_FORMAT_ARRAY_ROWS + '_' + SERIES_LAYOUT_BY_ROW] = function (rawData, startIndex, dimsDef, idx, out) {\n  idx += startIndex;\n  var item = out || [];\n  var data = rawData;\n  for (var i = 0; i < data.length; i++) {\n    var row = data[i];\n    item[i] = row ? row[idx] : null;\n  }\n  return item;\n}, _a[SOURCE_FORMAT_OBJECT_ROWS] = getItemSimply, _a[SOURCE_FORMAT_KEYED_COLUMNS] = function (rawData, startIndex, dimsDef, idx, out) {\n  var item = out || [];\n  for (var i = 0; i < dimsDef.length; i++) {\n    var dimName = dimsDef[i].name;\n    if (process.env.NODE_ENV !== 'production') {\n      if (dimName == null) {\n        throw new Error();\n      }\n    }\n    var col = rawData[dimName];\n    item[i] = col ? col[idx] : null;\n  }\n  return item;\n}, _a[SOURCE_FORMAT_ORIGINAL] = getItemSimply, _a);\nexport function getRawSourceItemGetter(sourceFormat, seriesLayoutBy) {\n  var method = rawSourceItemGetterMap[getMethodMapKey(sourceFormat, seriesLayoutBy)];\n  if (process.env.NODE_ENV !== 'production') {\n    assert(method, 'Do not support get item on \"' + sourceFormat + '\", \"' + seriesLayoutBy + '\".');\n  }\n  return method;\n}\nvar countSimply = function (rawData, startIndex, dimsDef) {\n  return rawData.length;\n};\nvar rawSourceDataCounterMap = (_b = {}, _b[SOURCE_FORMAT_ARRAY_ROWS + '_' + SERIES_LAYOUT_BY_COLUMN] = function (rawData, startIndex, dimsDef) {\n  return Math.max(0, rawData.length - startIndex);\n}, _b[SOURCE_FORMAT_ARRAY_ROWS + '_' + SERIES_LAYOUT_BY_ROW] = function (rawData, startIndex, dimsDef) {\n  var row = rawData[0];\n  return row ? Math.max(0, row.length - startIndex) : 0;\n}, _b[SOURCE_FORMAT_OBJECT_ROWS] = countSimply, _b[SOURCE_FORMAT_KEYED_COLUMNS] = function (rawData, startIndex, dimsDef) {\n  var dimName = dimsDef[0].name;\n  if (process.env.NODE_ENV !== 'production') {\n    if (dimName == null) {\n      throw new Error();\n    }\n  }\n  var col = rawData[dimName];\n  return col ? col.length : 0;\n}, _b[SOURCE_FORMAT_ORIGINAL] = countSimply, _b);\nexport function getRawSourceDataCounter(sourceFormat, seriesLayoutBy) {\n  var method = rawSourceDataCounterMap[getMethodMapKey(sourceFormat, seriesLayoutBy)];\n  if (process.env.NODE_ENV !== 'production') {\n    assert(method, 'Do not support count on \"' + sourceFormat + '\", \"' + seriesLayoutBy + '\".');\n  }\n  return method;\n}\nvar getRawValueSimply = function (dataItem, dimIndex, property) {\n  return dataItem[dimIndex];\n};\nvar rawSourceValueGetterMap = (_c = {}, _c[SOURCE_FORMAT_ARRAY_ROWS] = getRawValueSimply, _c[SOURCE_FORMAT_OBJECT_ROWS] = function (dataItem, dimIndex, property) {\n  return dataItem[property];\n}, _c[SOURCE_FORMAT_KEYED_COLUMNS] = getRawValueSimply, _c[SOURCE_FORMAT_ORIGINAL] = function (dataItem, dimIndex, property) {\n  // FIXME: In some case (markpoint in geo (geo-map.html)),\n  // dataItem is {coord: [...]}\n  var value = getDataItemValue(dataItem);\n  return !(value instanceof Array) ? value : value[dimIndex];\n}, _c[SOURCE_FORMAT_TYPED_ARRAY] = getRawValueSimply, _c);\nexport function getRawSourceValueGetter(sourceFormat) {\n  var method = rawSourceValueGetterMap[sourceFormat];\n  if (process.env.NODE_ENV !== 'production') {\n    assert(method, 'Do not support get value on \"' + sourceFormat + '\".');\n  }\n  return method;\n}\nfunction getMethodMapKey(sourceFormat, seriesLayoutBy) {\n  return sourceFormat === SOURCE_FORMAT_ARRAY_ROWS ? sourceFormat + '_' + seriesLayoutBy : sourceFormat;\n}\n// ??? FIXME can these logic be more neat: getRawValue, getRawDataItem,\n// Consider persistent.\n// Caution: why use raw value to display on label or tooltip?\n// A reason is to avoid format. For example time value we do not know\n// how to format is expected. More over, if stack is used, calculated\n// value may be 0.91000000001, which have brings trouble to display.\n// TODO: consider how to treat null/undefined/NaN when display?\nexport function retrieveRawValue(data, dataIndex,\n// If dimIndex is null/undefined, return OptionDataItem.\n// Otherwise, return OptionDataValue.\ndim) {\n  if (!data) {\n    return;\n  }\n  // Consider data may be not persistent.\n  var dataItem = data.getRawDataItem(dataIndex);\n  if (dataItem == null) {\n    return;\n  }\n  var store = data.getStore();\n  var sourceFormat = store.getSource().sourceFormat;\n  if (dim != null) {\n    var dimIndex = data.getDimensionIndex(dim);\n    var property = store.getDimensionProperty(dimIndex);\n    return getRawSourceValueGetter(sourceFormat)(dataItem, dimIndex, property);\n  } else {\n    var result = dataItem;\n    if (sourceFormat === SOURCE_FORMAT_ORIGINAL) {\n      result = getDataItemValue(dataItem);\n    }\n    return result;\n  }\n}\n/**\r\n * Compatible with some cases (in pie, map) like:\r\n * data: [{name: 'xx', value: 5, selected: true}, ...]\r\n * where only sourceFormat is 'original' and 'objectRows' supported.\r\n *\r\n * // TODO\r\n * Supported detail options in data item when using 'arrayRows'.\r\n *\r\n * @param data\r\n * @param dataIndex\r\n * @param attr like 'selected'\r\n */\nexport function retrieveRawAttr(data, dataIndex, attr) {\n  if (!data) {\n    return;\n  }\n  var sourceFormat = data.getStore().getSource().sourceFormat;\n  if (sourceFormat !== SOURCE_FORMAT_ORIGINAL && sourceFormat !== SOURCE_FORMAT_OBJECT_ROWS) {\n    return;\n  }\n  var dataItem = data.getRawDataItem(dataIndex);\n  if (sourceFormat === SOURCE_FORMAT_ORIGINAL && !isObject(dataItem)) {\n    dataItem = null;\n  }\n  if (dataItem) {\n    return dataItem[attr];\n  }\n}"], "names": [], "mappings": "AACA;;;;;;;;;;;;;;;;;AAiBA,GAGA;;CAEC,GAED;;;;;;;;;;;;;;;;;AAiBA;;;;;;;;AAEA,OAAO;AACP,wDAAwD;AACxD,oCAAoC;AACpC;AACA;AACA;AACA;AAPA,IAAI,IAAI,IAAI;;;;;AAQZ,IAAI;AACJ,IAAI;AACJ;;;CAGC,GACD,IAAI,sBAAsB,WAAW,GAAE;IACrC,SAAS,oBAAoB,WAAW,EAAE,OAAO;QAC/C,sBAAsB;QACtB,IAAI,SAAS,CAAC,CAAA,GAAA,gJAAA,CAAA,mBAAgB,AAAD,EAAE,eAAe,CAAA,GAAA,gJAAA,CAAA,mCAAgC,AAAD,EAAE,eAAe;QAC9F,4BAA4B;QAC5B,IAAI,CAAC,OAAO,GAAG;QACf,IAAI,OAAO,IAAI,CAAC,KAAK,GAAG,OAAO,IAAI;QACnC,2BAA2B;QAC3B,IAAI,OAAO,YAAY,KAAK,+IAAA,CAAA,4BAAyB,EAAE;YACrD,wCAA2C;gBACzC,IAAI,WAAW,MAAM;oBACnB,MAAM,IAAI,MAAM;gBAClB;YACF;YACA,IAAI,CAAC,OAAO,GAAG;YACf,IAAI,CAAC,QAAQ,GAAG;YAChB,IAAI,CAAC,KAAK,GAAG;QACf;QACA,aAAa,IAAI,EAAE,MAAM;IAC3B;IACA,oBAAoB,SAAS,CAAC,SAAS,GAAG;QACxC,OAAO,IAAI,CAAC,OAAO;IACrB;IACA,oBAAoB,SAAS,CAAC,KAAK,GAAG;QACpC,OAAO;IACT;IACA,oBAAoB,SAAS,CAAC,OAAO,GAAG,SAAU,GAAG,EAAE,GAAG;QACxD;IACF;IACA,oBAAoB,SAAS,CAAC,UAAU,GAAG,SAAU,OAAO,GAAG;IAC/D,oBAAoB,SAAS,CAAC,KAAK,GAAG,YAAa;IACnD,oBAAoB,eAAe,GAAG;QACpC,wDAAwD;QACxD,uDAAuD;QACvD,IAAI,QAAQ,oBAAoB,SAAS;QACzC,MAAM,IAAI,GAAG;QACb,MAAM,UAAU,GAAG;IACrB;IACA,oBAAoB,aAAa,GAAG;QAClC,IAAI;QACJ,eAAe,SAAU,QAAQ,EAAE,IAAI,EAAE,MAAM;YAC7C,IAAI,eAAe,OAAO,YAAY;YACtC,IAAI,iBAAiB,OAAO,cAAc;YAC1C,IAAI,aAAa,OAAO,UAAU;YAClC,IAAI,UAAU,OAAO,gBAAgB;YACrC,IAAI,UAAU,eAAe,CAAC,gBAAgB,cAAc,gBAAgB;YAC5E,wCAA2C;gBACzC,CAAA,GAAA,8IAAA,CAAA,SAAM,AAAD,EAAE,SAAS,4BAA4B;YAC9C;YACA,CAAA,GAAA,8IAAA,CAAA,SAAM,AAAD,EAAE,UAAU;YACjB,IAAI,iBAAiB,+IAAA,CAAA,4BAAyB,EAAE;gBAC9C,SAAS,OAAO,GAAG;gBACnB,SAAS,KAAK,GAAG;gBACjB,SAAS,WAAW,GAAG;YACzB,OAAO;gBACL,IAAI,gBAAgB,uBAAuB,cAAc;gBACzD,SAAS,OAAO,GAAG,CAAA,GAAA,8IAAA,CAAA,OAAI,AAAD,EAAE,eAAe,MAAM,MAAM,YAAY;gBAC/D,IAAI,aAAa,wBAAwB,cAAc;gBACvD,SAAS,KAAK,GAAG,CAAA,GAAA,8IAAA,CAAA,OAAI,AAAD,EAAE,YAAY,MAAM,MAAM,YAAY;YAC5D;QACF;QACA,IAAI,uBAAuB,SAAU,GAAG,EAAE,GAAG;YAC3C,MAAM,MAAM,IAAI,CAAC,OAAO;YACxB,MAAM,OAAO,EAAE;YACf,IAAI,OAAO,IAAI,CAAC,KAAK;YACrB,IAAI,UAAU,IAAI,CAAC,QAAQ;YAC3B,IAAI,SAAS,UAAU;YACvB,IAAK,IAAI,IAAI,GAAG,IAAI,SAAS,IAAK;gBAChC,GAAG,CAAC,EAAE,GAAG,IAAI,CAAC,SAAS,EAAE;YAC3B;YACA,OAAO;QACT;QACA,IAAI,2BAA2B,SAAU,KAAK,EAAE,GAAG,EAAE,OAAO,EAAE,MAAM;YAClE,IAAI,OAAO,IAAI,CAAC,KAAK;YACrB,IAAI,UAAU,IAAI,CAAC,QAAQ;YAC3B,IAAK,IAAI,MAAM,GAAG,MAAM,SAAS,MAAO;gBACtC,IAAI,YAAY,MAAM,CAAC,IAAI;gBAC3B,IAAI,MAAM,SAAS,CAAC,EAAE,IAAI,OAAO,WAAW,SAAS,CAAC,EAAE;gBACxD,IAAI,MAAM,SAAS,CAAC,EAAE,IAAI,OAAO,CAAC,WAAW,SAAS,CAAC,EAAE;gBACzD,IAAI,QAAQ,MAAM;gBAClB,IAAI,MAAM,OAAO,CAAC,IAAI;gBACtB,IAAK,IAAI,IAAI,GAAG,IAAI,OAAO,IAAK;oBAC9B,iEAAiE;oBACjE,IAAI,MAAM,IAAI,CAAC,IAAI,UAAU,IAAI;oBACjC,GAAG,CAAC,QAAQ,EAAE,GAAG;oBACjB,MAAM,OAAO,CAAC,MAAM,GAAG;oBACvB,MAAM,OAAO,CAAC,MAAM,GAAG;gBACzB;gBACA,SAAS,CAAC,EAAE,GAAG;gBACf,SAAS,CAAC,EAAE,GAAG;YACjB;QACF;QACA,IAAI,qBAAqB;YACvB,OAAO,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC,MAAM,GAAG,IAAI,CAAC,QAAQ,GAAG;QAC1D;QACA,kBAAkB,CAAC,KAAK,CAAC,GAAG,EAAE,CAAC,+IAAA,CAAA,2BAAwB,GAAG,MAAM,+IAAA,CAAA,0BAAuB,CAAC,GAAG;YACzF,MAAM;YACN,YAAY;QACd,GAAG,EAAE,CAAC,+IAAA,CAAA,2BAAwB,GAAG,MAAM,+IAAA,CAAA,uBAAoB,CAAC,GAAG;YAC7D,MAAM;YACN,YAAY;gBACV,MAAM,IAAI,MAAM;YAClB;QACF,GAAG,EAAE,CAAC,+IAAA,CAAA,4BAAyB,CAAC,GAAG;YACjC,MAAM;YACN,YAAY;QACd,GAAG,EAAE,CAAC,+IAAA,CAAA,8BAA2B,CAAC,GAAG;YACnC,MAAM;YACN,YAAY,SAAU,OAAO;gBAC3B,IAAI,OAAO,IAAI,CAAC,KAAK;gBACrB,CAAA,GAAA,8IAAA,CAAA,OAAI,AAAD,EAAE,SAAS,SAAU,MAAM,EAAE,GAAG;oBACjC,IAAI,SAAS,IAAI,CAAC,IAAI,IAAI,CAAC,IAAI,CAAC,IAAI,GAAG,EAAE;oBACzC,IAAK,IAAI,IAAI,GAAG,IAAI,CAAC,UAAU,EAAE,EAAE,MAAM,EAAE,IAAK;wBAC9C,OAAO,IAAI,CAAC,MAAM,CAAC,EAAE;oBACvB;gBACF;YACF;QACF,GAAG,EAAE,CAAC,+IAAA,CAAA,yBAAsB,CAAC,GAAG;YAC9B,YAAY;QACd,GAAG,EAAE,CAAC,+IAAA,CAAA,4BAAyB,CAAC,GAAG;YACjC,YAAY;YACZ,MAAM;YACN,YAAY,SAAU,OAAO;gBAC3B,wCAA2C;oBACzC,CAAA,GAAA,8IAAA,CAAA,SAAM,AAAD,EAAE,CAAA,GAAA,8IAAA,CAAA,eAAY,AAAD,EAAE,UAAU;gBAChC;gBACA,IAAI,CAAC,KAAK,GAAG;YACf;YACA,sCAAsC;YACtC,OAAO;gBACL,UAAU;gBACV,IAAI,CAAC,OAAO,IAAI,IAAI,CAAC,KAAK;gBAC1B,IAAI,CAAC,KAAK,GAAG;YACf;QACF,GAAG,EAAE;QACL,SAAS,iBAAiB,OAAO;YAC/B,IAAK,IAAI,IAAI,GAAG,IAAI,QAAQ,MAAM,EAAE,IAAK;gBACvC,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,OAAO,CAAC,EAAE;YAC5B;QACF;IACF;IACA,OAAO;AACT;;AAEA,IAAI,gBAAgB,SAAU,OAAO,EAAE,UAAU,EAAE,OAAO,EAAE,GAAG;IAC7D,OAAO,OAAO,CAAC,IAAI;AACrB;AACA,IAAI,yBAAyB,CAAC,KAAK,CAAC,GAAG,EAAE,CAAC,+IAAA,CAAA,2BAAwB,GAAG,MAAM,+IAAA,CAAA,0BAAuB,CAAC,GAAG,SAAU,OAAO,EAAE,UAAU,EAAE,OAAO,EAAE,GAAG;IAC/I,OAAO,OAAO,CAAC,MAAM,WAAW;AAClC,GAAG,EAAE,CAAC,+IAAA,CAAA,2BAAwB,GAAG,MAAM,+IAAA,CAAA,uBAAoB,CAAC,GAAG,SAAU,OAAO,EAAE,UAAU,EAAE,OAAO,EAAE,GAAG,EAAE,GAAG;IAC7G,OAAO;IACP,IAAI,OAAO,OAAO,EAAE;IACpB,IAAI,OAAO;IACX,IAAK,IAAI,IAAI,GAAG,IAAI,KAAK,MAAM,EAAE,IAAK;QACpC,IAAI,MAAM,IAAI,CAAC,EAAE;QACjB,IAAI,CAAC,EAAE,GAAG,MAAM,GAAG,CAAC,IAAI,GAAG;IAC7B;IACA,OAAO;AACT,GAAG,EAAE,CAAC,+IAAA,CAAA,4BAAyB,CAAC,GAAG,eAAe,EAAE,CAAC,+IAAA,CAAA,8BAA2B,CAAC,GAAG,SAAU,OAAO,EAAE,UAAU,EAAE,OAAO,EAAE,GAAG,EAAE,GAAG;IAClI,IAAI,OAAO,OAAO,EAAE;IACpB,IAAK,IAAI,IAAI,GAAG,IAAI,QAAQ,MAAM,EAAE,IAAK;QACvC,IAAI,UAAU,OAAO,CAAC,EAAE,CAAC,IAAI;QAC7B,wCAA2C;YACzC,IAAI,WAAW,MAAM;gBACnB,MAAM,IAAI;YACZ;QACF;QACA,IAAI,MAAM,OAAO,CAAC,QAAQ;QAC1B,IAAI,CAAC,EAAE,GAAG,MAAM,GAAG,CAAC,IAAI,GAAG;IAC7B;IACA,OAAO;AACT,GAAG,EAAE,CAAC,+IAAA,CAAA,yBAAsB,CAAC,GAAG,eAAe,EAAE;AAC1C,SAAS,uBAAuB,YAAY,EAAE,cAAc;IACjE,IAAI,SAAS,sBAAsB,CAAC,gBAAgB,cAAc,gBAAgB;IAClF,wCAA2C;QACzC,CAAA,GAAA,8IAAA,CAAA,SAAM,AAAD,EAAE,QAAQ,iCAAiC,eAAe,SAAS,iBAAiB;IAC3F;IACA,OAAO;AACT;AACA,IAAI,cAAc,SAAU,OAAO,EAAE,UAAU,EAAE,OAAO;IACtD,OAAO,QAAQ,MAAM;AACvB;AACA,IAAI,0BAA0B,CAAC,KAAK,CAAC,GAAG,EAAE,CAAC,+IAAA,CAAA,2BAAwB,GAAG,MAAM,+IAAA,CAAA,0BAAuB,CAAC,GAAG,SAAU,OAAO,EAAE,UAAU,EAAE,OAAO;IAC3I,OAAO,KAAK,GAAG,CAAC,GAAG,QAAQ,MAAM,GAAG;AACtC,GAAG,EAAE,CAAC,+IAAA,CAAA,2BAAwB,GAAG,MAAM,+IAAA,CAAA,uBAAoB,CAAC,GAAG,SAAU,OAAO,EAAE,UAAU,EAAE,OAAO;IACnG,IAAI,MAAM,OAAO,CAAC,EAAE;IACpB,OAAO,MAAM,KAAK,GAAG,CAAC,GAAG,IAAI,MAAM,GAAG,cAAc;AACtD,GAAG,EAAE,CAAC,+IAAA,CAAA,4BAAyB,CAAC,GAAG,aAAa,EAAE,CAAC,+IAAA,CAAA,8BAA2B,CAAC,GAAG,SAAU,OAAO,EAAE,UAAU,EAAE,OAAO;IACtH,IAAI,UAAU,OAAO,CAAC,EAAE,CAAC,IAAI;IAC7B,wCAA2C;QACzC,IAAI,WAAW,MAAM;YACnB,MAAM,IAAI;QACZ;IACF;IACA,IAAI,MAAM,OAAO,CAAC,QAAQ;IAC1B,OAAO,MAAM,IAAI,MAAM,GAAG;AAC5B,GAAG,EAAE,CAAC,+IAAA,CAAA,yBAAsB,CAAC,GAAG,aAAa,EAAE;AACxC,SAAS,wBAAwB,YAAY,EAAE,cAAc;IAClE,IAAI,SAAS,uBAAuB,CAAC,gBAAgB,cAAc,gBAAgB;IACnF,wCAA2C;QACzC,CAAA,GAAA,8IAAA,CAAA,SAAM,AAAD,EAAE,QAAQ,8BAA8B,eAAe,SAAS,iBAAiB;IACxF;IACA,OAAO;AACT;AACA,IAAI,oBAAoB,SAAU,QAAQ,EAAE,QAAQ,EAAE,QAAQ;IAC5D,OAAO,QAAQ,CAAC,SAAS;AAC3B;AACA,IAAI,0BAA0B,CAAC,KAAK,CAAC,GAAG,EAAE,CAAC,+IAAA,CAAA,2BAAwB,CAAC,GAAG,mBAAmB,EAAE,CAAC,+IAAA,CAAA,4BAAyB,CAAC,GAAG,SAAU,QAAQ,EAAE,QAAQ,EAAE,QAAQ;IAC9J,OAAO,QAAQ,CAAC,SAAS;AAC3B,GAAG,EAAE,CAAC,+IAAA,CAAA,8BAA2B,CAAC,GAAG,mBAAmB,EAAE,CAAC,+IAAA,CAAA,yBAAsB,CAAC,GAAG,SAAU,QAAQ,EAAE,QAAQ,EAAE,QAAQ;IACzH,yDAAyD;IACzD,6BAA6B;IAC7B,IAAI,QAAQ,CAAA,GAAA,+IAAA,CAAA,mBAAgB,AAAD,EAAE;IAC7B,OAAO,CAAC,CAAC,iBAAiB,KAAK,IAAI,QAAQ,KAAK,CAAC,SAAS;AAC5D,GAAG,EAAE,CAAC,+IAAA,CAAA,4BAAyB,CAAC,GAAG,mBAAmB,EAAE;AACjD,SAAS,wBAAwB,YAAY;IAClD,IAAI,SAAS,uBAAuB,CAAC,aAAa;IAClD,wCAA2C;QACzC,CAAA,GAAA,8IAAA,CAAA,SAAM,AAAD,EAAE,QAAQ,kCAAkC,eAAe;IAClE;IACA,OAAO;AACT;AACA,SAAS,gBAAgB,YAAY,EAAE,cAAc;IACnD,OAAO,iBAAiB,+IAAA,CAAA,2BAAwB,GAAG,eAAe,MAAM,iBAAiB;AAC3F;AAQO,SAAS,iBAAiB,IAAI,EAAE,SAAS,EAChD,wDAAwD;AACxD,qCAAqC;AACrC,GAAG;IACD,IAAI,CAAC,MAAM;QACT;IACF;IACA,uCAAuC;IACvC,IAAI,WAAW,KAAK,cAAc,CAAC;IACnC,IAAI,YAAY,MAAM;QACpB;IACF;IACA,IAAI,QAAQ,KAAK,QAAQ;IACzB,IAAI,eAAe,MAAM,SAAS,GAAG,YAAY;IACjD,IAAI,OAAO,MAAM;QACf,IAAI,WAAW,KAAK,iBAAiB,CAAC;QACtC,IAAI,WAAW,MAAM,oBAAoB,CAAC;QAC1C,OAAO,wBAAwB,cAAc,UAAU,UAAU;IACnE,OAAO;QACL,IAAI,SAAS;QACb,IAAI,iBAAiB,+IAAA,CAAA,yBAAsB,EAAE;YAC3C,SAAS,CAAA,GAAA,+IAAA,CAAA,mBAAgB,AAAD,EAAE;QAC5B;QACA,OAAO;IACT;AACF;AAaO,SAAS,gBAAgB,IAAI,EAAE,SAAS,EAAE,IAAI;IACnD,IAAI,CAAC,MAAM;QACT;IACF;IACA,IAAI,eAAe,KAAK,QAAQ,GAAG,SAAS,GAAG,YAAY;IAC3D,IAAI,iBAAiB,+IAAA,CAAA,yBAAsB,IAAI,iBAAiB,+IAAA,CAAA,4BAAyB,EAAE;QACzF;IACF;IACA,IAAI,WAAW,KAAK,cAAc,CAAC;IACnC,IAAI,iBAAiB,+IAAA,CAAA,yBAAsB,IAAI,CAAC,CAAA,GAAA,8IAAA,CAAA,WAAQ,AAAD,EAAE,WAAW;QAClE,WAAW;IACb;IACA,IAAI,UAAU;QACZ,OAAO,QAAQ,CAAC,KAAK;IACvB;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 965, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/tindahan/sari-sari-admin/node_modules/echarts/lib/data/helper/dataValueHelper.js"], "sourcesContent": ["\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\r\n* Licensed to the Apache Software Foundation (ASF) under one\r\n* or more contributor license agreements.  See the NOTICE file\r\n* distributed with this work for additional information\r\n* regarding copyright ownership.  The ASF licenses this file\r\n* to you under the Apache License, Version 2.0 (the\r\n* \"License\"); you may not use this file except in compliance\r\n* with the License.  You may obtain a copy of the License at\r\n*\r\n*   http://www.apache.org/licenses/LICENSE-2.0\r\n*\r\n* Unless required by applicable law or agreed to in writing,\r\n* software distributed under the License is distributed on an\r\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\r\n* KIND, either express or implied.  See the License for the\r\n* specific language governing permissions and limitations\r\n* under the License.\r\n*/\nimport { parseDate, numericToNumber } from '../../util/number.js';\nimport { createHashMap, trim, hasOwn, isString, isNumber } from 'zrender/lib/core/util.js';\nimport { throwError } from '../../util/log.js';\n/**\r\n * Convert raw the value in to inner value in List.\r\n *\r\n * [Performance sensitive]\r\n *\r\n * [Caution]: this is the key logic of user value parser.\r\n * For backward compatibility, do not modify it until you have to!\r\n */\nexport function parseDataValue(value,\n// For high performance, do not omit the second param.\nopt) {\n  // Performance sensitive.\n  var dimType = opt && opt.type;\n  if (dimType === 'ordinal') {\n    // If given value is a category string\n    return value;\n  }\n  if (dimType === 'time'\n  // spead up when using timestamp\n  && !isNumber(value) && value != null && value !== '-') {\n    value = +parseDate(value);\n  }\n  // dimType defaults 'number'.\n  // If dimType is not ordinal and value is null or undefined or NaN or '-',\n  // parse to NaN.\n  // number-like string (like ' 123 ') can be converted to a number.\n  // where null/undefined or other string will be converted to NaN.\n  return value == null || value === '' ? NaN\n  // If string (like '-'), using '+' parse to NaN\n  // If object, also parse to NaN\n  : Number(value);\n}\n;\nvar valueParserMap = createHashMap({\n  'number': function (val) {\n    // Do not use `numericToNumber` here. We have `numericToNumber` by default.\n    // Here the number parser can have loose rule:\n    // enable to cut suffix: \"120px\" => 120, \"14%\" => 14.\n    return parseFloat(val);\n  },\n  'time': function (val) {\n    // return timestamp.\n    return +parseDate(val);\n  },\n  'trim': function (val) {\n    return isString(val) ? trim(val) : val;\n  }\n});\nexport function getRawValueParser(type) {\n  return valueParserMap.get(type);\n}\nvar ORDER_COMPARISON_OP_MAP = {\n  lt: function (lval, rval) {\n    return lval < rval;\n  },\n  lte: function (lval, rval) {\n    return lval <= rval;\n  },\n  gt: function (lval, rval) {\n    return lval > rval;\n  },\n  gte: function (lval, rval) {\n    return lval >= rval;\n  }\n};\nvar FilterOrderComparator = /** @class */function () {\n  function FilterOrderComparator(op, rval) {\n    if (!isNumber(rval)) {\n      var errMsg = '';\n      if (process.env.NODE_ENV !== 'production') {\n        errMsg = 'rvalue of \"<\", \">\", \"<=\", \">=\" can only be number in filter.';\n      }\n      throwError(errMsg);\n    }\n    this._opFn = ORDER_COMPARISON_OP_MAP[op];\n    this._rvalFloat = numericToNumber(rval);\n  }\n  // Performance sensitive.\n  FilterOrderComparator.prototype.evaluate = function (lval) {\n    // Most cases is 'number', and typeof maybe 10 times faseter than parseFloat.\n    return isNumber(lval) ? this._opFn(lval, this._rvalFloat) : this._opFn(numericToNumber(lval), this._rvalFloat);\n  };\n  return FilterOrderComparator;\n}();\nvar SortOrderComparator = /** @class */function () {\n  /**\r\n   * @param order by default: 'asc'\r\n   * @param incomparable by default: Always on the tail.\r\n   *        That is, if 'asc' => 'max', if 'desc' => 'min'\r\n   *        See the definition of \"incomparable\" in [SORT_COMPARISON_RULE].\r\n   */\n  function SortOrderComparator(order, incomparable) {\n    var isDesc = order === 'desc';\n    this._resultLT = isDesc ? 1 : -1;\n    if (incomparable == null) {\n      incomparable = isDesc ? 'min' : 'max';\n    }\n    this._incomparable = incomparable === 'min' ? -Infinity : Infinity;\n  }\n  // See [SORT_COMPARISON_RULE].\n  // Performance sensitive.\n  SortOrderComparator.prototype.evaluate = function (lval, rval) {\n    // Most cases is 'number', and typeof maybe 10 times faseter than parseFloat.\n    var lvalFloat = isNumber(lval) ? lval : numericToNumber(lval);\n    var rvalFloat = isNumber(rval) ? rval : numericToNumber(rval);\n    var lvalNotNumeric = isNaN(lvalFloat);\n    var rvalNotNumeric = isNaN(rvalFloat);\n    if (lvalNotNumeric) {\n      lvalFloat = this._incomparable;\n    }\n    if (rvalNotNumeric) {\n      rvalFloat = this._incomparable;\n    }\n    if (lvalNotNumeric && rvalNotNumeric) {\n      var lvalIsStr = isString(lval);\n      var rvalIsStr = isString(rval);\n      if (lvalIsStr) {\n        lvalFloat = rvalIsStr ? lval : 0;\n      }\n      if (rvalIsStr) {\n        rvalFloat = lvalIsStr ? rval : 0;\n      }\n    }\n    return lvalFloat < rvalFloat ? this._resultLT : lvalFloat > rvalFloat ? -this._resultLT : 0;\n  };\n  return SortOrderComparator;\n}();\nexport { SortOrderComparator };\nvar FilterEqualityComparator = /** @class */function () {\n  function FilterEqualityComparator(isEq, rval) {\n    this._rval = rval;\n    this._isEQ = isEq;\n    this._rvalTypeof = typeof rval;\n    this._rvalFloat = numericToNumber(rval);\n  }\n  // Performance sensitive.\n  FilterEqualityComparator.prototype.evaluate = function (lval) {\n    var eqResult = lval === this._rval;\n    if (!eqResult) {\n      var lvalTypeof = typeof lval;\n      if (lvalTypeof !== this._rvalTypeof && (lvalTypeof === 'number' || this._rvalTypeof === 'number')) {\n        eqResult = numericToNumber(lval) === this._rvalFloat;\n      }\n    }\n    return this._isEQ ? eqResult : !eqResult;\n  };\n  return FilterEqualityComparator;\n}();\n/**\r\n * [FILTER_COMPARISON_RULE]\r\n * `lt`|`lte`|`gt`|`gte`:\r\n * + rval must be a number. And lval will be converted to number (`numericToNumber`) to compare.\r\n * `eq`:\r\n * + If same type, compare with `===`.\r\n * + If there is one number, convert to number (`numericToNumber`) to compare.\r\n * + Else return `false`.\r\n * `ne`:\r\n * + Not `eq`.\r\n *\r\n *\r\n * [SORT_COMPARISON_RULE]\r\n * All the values are grouped into three categories:\r\n * + \"numeric\" (number and numeric string)\r\n * + \"non-numeric-string\" (string that excluding numeric string)\r\n * + \"others\"\r\n * \"numeric\" vs \"numeric\": values are ordered by number order.\r\n * \"non-numeric-string\" vs \"non-numeric-string\": values are ordered by ES spec (#sec-abstract-relational-comparison).\r\n * \"others\" vs \"others\": do not change order (always return 0).\r\n * \"numeric\" vs \"non-numeric-string\": \"non-numeric-string\" is treated as \"incomparable\".\r\n * \"number\" vs \"others\": \"others\" is treated as \"incomparable\".\r\n * \"non-numeric-string\" vs \"others\": \"others\" is treated as \"incomparable\".\r\n * \"incomparable\" will be seen as -Infinity or Infinity (depends on the settings).\r\n * MEMO:\r\n *   Non-numeric string sort makes sense when we need to put the items with the same tag together.\r\n *   But if we support string sort, we still need to avoid the misleading like `'2' > '12'`,\r\n *   So we treat \"numeric-string\" sorted by number order rather than string comparison.\r\n *\r\n *\r\n * [CHECK_LIST_OF_THE_RULE_DESIGN]\r\n * + Do not support string comparison until required. And also need to\r\n *   avoid the misleading of \"2\" > \"12\".\r\n * + Should avoid the misleading case:\r\n *   `\" 22 \" gte \"22\"` is `true` but `\" 22 \" eq \"22\"` is `false`.\r\n * + JS bad case should be avoided: null <= 0, [] <= 0, ' ' <= 0, ...\r\n * + Only \"numeric\" can be converted to comparable number, otherwise converted to NaN.\r\n *   See `util/number.ts#numericToNumber`.\r\n *\r\n * @return If `op` is not `RelationalOperator`, return null;\r\n */\nexport function createFilterComparator(op, rval) {\n  return op === 'eq' || op === 'ne' ? new FilterEqualityComparator(op === 'eq', rval) : hasOwn(ORDER_COMPARISON_OP_MAP, op) ? new FilterOrderComparator(op, rval) : null;\n}"], "names": [], "mappings": "AACA;;;;;;;;;;;;;;;;;AAiBA,GAGA;;CAEC,GAED;;;;;;;;;;;;;;;;;AAiBA;;;;;;AACA;AACA;AACA;;;;AASO,SAAS,eAAe,KAAK,EACpC,sDAAsD;AACtD,GAAG;IACD,yBAAyB;IACzB,IAAI,UAAU,OAAO,IAAI,IAAI;IAC7B,IAAI,YAAY,WAAW;QACzB,sCAAsC;QACtC,OAAO;IACT;IACA,IAAI,YAAY,UAEb,CAAC,CAAA,GAAA,8IAAA,CAAA,WAAQ,AAAD,EAAE,UAAU,SAAS,QAAQ,UAAU,KAAK;QACrD,QAAQ,CAAC,CAAA,GAAA,gJAAA,CAAA,YAAS,AAAD,EAAE;IACrB;IACA,6BAA6B;IAC7B,0EAA0E;IAC1E,gBAAgB;IAChB,kEAAkE;IAClE,iEAAiE;IACjE,OAAO,SAAS,QAAQ,UAAU,KAAK,MAGrC,OAAO;AACX;;AAEA,IAAI,iBAAiB,CAAA,GAAA,8IAAA,CAAA,gBAAa,AAAD,EAAE;IACjC,UAAU,SAAU,GAAG;QACrB,2EAA2E;QAC3E,8CAA8C;QAC9C,qDAAqD;QACrD,OAAO,WAAW;IACpB;IACA,QAAQ,SAAU,GAAG;QACnB,oBAAoB;QACpB,OAAO,CAAC,CAAA,GAAA,gJAAA,CAAA,YAAS,AAAD,EAAE;IACpB;IACA,QAAQ,SAAU,GAAG;QACnB,OAAO,CAAA,GAAA,8IAAA,CAAA,WAAQ,AAAD,EAAE,OAAO,CAAA,GAAA,8IAAA,CAAA,OAAI,AAAD,EAAE,OAAO;IACrC;AACF;AACO,SAAS,kBAAkB,IAAI;IACpC,OAAO,eAAe,GAAG,CAAC;AAC5B;AACA,IAAI,0BAA0B;IAC5B,IAAI,SAAU,IAAI,EAAE,IAAI;QACtB,OAAO,OAAO;IAChB;IACA,KAAK,SAAU,IAAI,EAAE,IAAI;QACvB,OAAO,QAAQ;IACjB;IACA,IAAI,SAAU,IAAI,EAAE,IAAI;QACtB,OAAO,OAAO;IAChB;IACA,KAAK,SAAU,IAAI,EAAE,IAAI;QACvB,OAAO,QAAQ;IACjB;AACF;AACA,IAAI,wBAAwB,WAAW,GAAE;IACvC,SAAS,sBAAsB,EAAE,EAAE,IAAI;QACrC,IAAI,CAAC,CAAA,GAAA,8IAAA,CAAA,WAAQ,AAAD,EAAE,OAAO;YACnB,IAAI,SAAS;YACb,IAAI,oDAAyB,cAAc;gBACzC,SAAS;YACX;YACA,CAAA,GAAA,6IAAA,CAAA,aAAU,AAAD,EAAE;QACb;QACA,IAAI,CAAC,KAAK,GAAG,uBAAuB,CAAC,GAAG;QACxC,IAAI,CAAC,UAAU,GAAG,CAAA,GAAA,gJAAA,CAAA,kBAAe,AAAD,EAAE;IACpC;IACA,yBAAyB;IACzB,sBAAsB,SAAS,CAAC,QAAQ,GAAG,SAAU,IAAI;QACvD,6EAA6E;QAC7E,OAAO,CAAA,GAAA,8IAAA,CAAA,WAAQ,AAAD,EAAE,QAAQ,IAAI,CAAC,KAAK,CAAC,MAAM,IAAI,CAAC,UAAU,IAAI,IAAI,CAAC,KAAK,CAAC,CAAA,GAAA,gJAAA,CAAA,kBAAe,AAAD,EAAE,OAAO,IAAI,CAAC,UAAU;IAC/G;IACA,OAAO;AACT;AACA,IAAI,sBAAsB,WAAW,GAAE;IACrC;;;;;GAKC,GACD,SAAS,oBAAoB,KAAK,EAAE,YAAY;QAC9C,IAAI,SAAS,UAAU;QACvB,IAAI,CAAC,SAAS,GAAG,SAAS,IAAI,CAAC;QAC/B,IAAI,gBAAgB,MAAM;YACxB,eAAe,SAAS,QAAQ;QAClC;QACA,IAAI,CAAC,aAAa,GAAG,iBAAiB,QAAQ,CAAC,WAAW;IAC5D;IACA,8BAA8B;IAC9B,yBAAyB;IACzB,oBAAoB,SAAS,CAAC,QAAQ,GAAG,SAAU,IAAI,EAAE,IAAI;QAC3D,6EAA6E;QAC7E,IAAI,YAAY,CAAA,GAAA,8IAAA,CAAA,WAAQ,AAAD,EAAE,QAAQ,OAAO,CAAA,GAAA,gJAAA,CAAA,kBAAe,AAAD,EAAE;QACxD,IAAI,YAAY,CAAA,GAAA,8IAAA,CAAA,WAAQ,AAAD,EAAE,QAAQ,OAAO,CAAA,GAAA,gJAAA,CAAA,kBAAe,AAAD,EAAE;QACxD,IAAI,iBAAiB,MAAM;QAC3B,IAAI,iBAAiB,MAAM;QAC3B,IAAI,gBAAgB;YAClB,YAAY,IAAI,CAAC,aAAa;QAChC;QACA,IAAI,gBAAgB;YAClB,YAAY,IAAI,CAAC,aAAa;QAChC;QACA,IAAI,kBAAkB,gBAAgB;YACpC,IAAI,YAAY,CAAA,GAAA,8IAAA,CAAA,WAAQ,AAAD,EAAE;YACzB,IAAI,YAAY,CAAA,GAAA,8IAAA,CAAA,WAAQ,AAAD,EAAE;YACzB,IAAI,WAAW;gBACb,YAAY,YAAY,OAAO;YACjC;YACA,IAAI,WAAW;gBACb,YAAY,YAAY,OAAO;YACjC;QACF;QACA,OAAO,YAAY,YAAY,IAAI,CAAC,SAAS,GAAG,YAAY,YAAY,CAAC,IAAI,CAAC,SAAS,GAAG;IAC5F;IACA,OAAO;AACT;;AAEA,IAAI,2BAA2B,WAAW,GAAE;IAC1C,SAAS,yBAAyB,IAAI,EAAE,IAAI;QAC1C,IAAI,CAAC,KAAK,GAAG;QACb,IAAI,CAAC,KAAK,GAAG;QACb,IAAI,CAAC,WAAW,GAAG,OAAO;QAC1B,IAAI,CAAC,UAAU,GAAG,CAAA,GAAA,gJAAA,CAAA,kBAAe,AAAD,EAAE;IACpC;IACA,yBAAyB;IACzB,yBAAyB,SAAS,CAAC,QAAQ,GAAG,SAAU,IAAI;QAC1D,IAAI,WAAW,SAAS,IAAI,CAAC,KAAK;QAClC,IAAI,CAAC,UAAU;YACb,IAAI,aAAa,OAAO;YACxB,IAAI,eAAe,IAAI,CAAC,WAAW,IAAI,CAAC,eAAe,YAAY,IAAI,CAAC,WAAW,KAAK,QAAQ,GAAG;gBACjG,WAAW,CAAA,GAAA,gJAAA,CAAA,kBAAe,AAAD,EAAE,UAAU,IAAI,CAAC,UAAU;YACtD;QACF;QACA,OAAO,IAAI,CAAC,KAAK,GAAG,WAAW,CAAC;IAClC;IACA,OAAO;AACT;AA0CO,SAAS,uBAAuB,EAAE,EAAE,IAAI;IAC7C,OAAO,OAAO,QAAQ,OAAO,OAAO,IAAI,yBAAyB,OAAO,MAAM,QAAQ,CAAA,GAAA,8IAAA,CAAA,SAAM,AAAD,EAAE,yBAAyB,MAAM,IAAI,sBAAsB,IAAI,QAAQ;AACpK", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1155, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/tindahan/sari-sari-admin/node_modules/echarts/lib/data/helper/transform.js"], "sourcesContent": ["\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\r\n* Licensed to the Apache Software Foundation (ASF) under one\r\n* or more contributor license agreements.  See the NOTICE file\r\n* distributed with this work for additional information\r\n* regarding copyright ownership.  The ASF licenses this file\r\n* to you under the Apache License, Version 2.0 (the\r\n* \"License\"); you may not use this file except in compliance\r\n* with the License.  You may obtain a copy of the License at\r\n*\r\n*   http://www.apache.org/licenses/LICENSE-2.0\r\n*\r\n* Unless required by applicable law or agreed to in writing,\r\n* software distributed under the License is distributed on an\r\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\r\n* KIND, either express or implied.  See the License for the\r\n* specific language governing permissions and limitations\r\n* under the License.\r\n*/\nimport { SERIES_LAYOUT_BY_COLUMN, SOURCE_FORMAT_OBJECT_ROWS, SOURCE_FORMAT_ARRAY_ROWS } from '../../util/types.js';\nimport { normalizeToArray } from '../../util/model.js';\nimport { createHashMap, bind, each, hasOwn, map, clone, isObject, extend, isNumber } from 'zrender/lib/core/util.js';\nimport { getRawSourceItemGetter, getRawSourceDataCounter, getRawSourceValueGetter } from './dataProvider.js';\nimport { parseDataValue } from './dataValueHelper.js';\nimport { log, makePrintable, throwError } from '../../util/log.js';\nimport { createSource, detectSourceFormat } from '../Source.js';\n/**\r\n * TODO: disable writable.\r\n * This structure will be exposed to users.\r\n */\nvar ExternalSource = /** @class */function () {\n  function ExternalSource() {}\n  ExternalSource.prototype.getRawData = function () {\n    // Only built-in transform available.\n    throw new Error('not supported');\n  };\n  ExternalSource.prototype.getRawDataItem = function (dataIndex) {\n    // Only built-in transform available.\n    throw new Error('not supported');\n  };\n  ExternalSource.prototype.cloneRawData = function () {\n    return;\n  };\n  /**\r\n   * @return If dimension not found, return null/undefined.\r\n   */\n  ExternalSource.prototype.getDimensionInfo = function (dim) {\n    return;\n  };\n  /**\r\n   * dimensions defined if and only if either:\r\n   * (a) dataset.dimensions are declared.\r\n   * (b) dataset data include dimensions definitions in data (detected or via specified `sourceHeader`).\r\n   * If dimensions are defined, `dimensionInfoAll` is corresponding to\r\n   * the defined dimensions.\r\n   * Otherwise, `dimensionInfoAll` is determined by data columns.\r\n   * @return Always return an array (even empty array).\r\n   */\n  ExternalSource.prototype.cloneAllDimensionInfo = function () {\n    return;\n  };\n  ExternalSource.prototype.count = function () {\n    return;\n  };\n  /**\r\n   * Only support by dimension index.\r\n   * No need to support by dimension name in transform function,\r\n   * because transform function is not case-specific, no need to use name literally.\r\n   */\n  ExternalSource.prototype.retrieveValue = function (dataIndex, dimIndex) {\n    return;\n  };\n  ExternalSource.prototype.retrieveValueFromItem = function (dataItem, dimIndex) {\n    return;\n  };\n  ExternalSource.prototype.convertValue = function (rawVal, dimInfo) {\n    return parseDataValue(rawVal, dimInfo);\n  };\n  return ExternalSource;\n}();\nexport { ExternalSource };\nfunction createExternalSource(internalSource, externalTransform) {\n  var extSource = new ExternalSource();\n  var data = internalSource.data;\n  var sourceFormat = extSource.sourceFormat = internalSource.sourceFormat;\n  var sourceHeaderCount = internalSource.startIndex;\n  var errMsg = '';\n  if (internalSource.seriesLayoutBy !== SERIES_LAYOUT_BY_COLUMN) {\n    // For the logic simplicity in transformer, only 'culumn' is\n    // supported in data transform. Otherwise, the `dimensionsDefine`\n    // might be detected by 'row', which probably confuses users.\n    if (process.env.NODE_ENV !== 'production') {\n      errMsg = '`seriesLayoutBy` of upstream dataset can only be \"column\" in data transform.';\n    }\n    throwError(errMsg);\n  }\n  // [MEMO]\n  // Create a new dimensions structure for exposing.\n  // Do not expose all dimension info to users directly.\n  // Because the dimension is probably auto detected from data and not might reliable.\n  // Should not lead the transformers to think that is reliable and return it.\n  // See [DIMENSION_INHERIT_RULE] in `sourceManager.ts`.\n  var dimensions = [];\n  var dimsByName = {};\n  var dimsDef = internalSource.dimensionsDefine;\n  if (dimsDef) {\n    each(dimsDef, function (dimDef, idx) {\n      var name = dimDef.name;\n      var dimDefExt = {\n        index: idx,\n        name: name,\n        displayName: dimDef.displayName\n      };\n      dimensions.push(dimDefExt);\n      // Users probably do not specify dimension name. For simplicity, data transform\n      // does not generate dimension name.\n      if (name != null) {\n        // Dimension name should not be duplicated.\n        // For simplicity, data transform forbids name duplication, do not generate\n        // new name like module `completeDimensions.ts` did, but just tell users.\n        var errMsg_1 = '';\n        if (hasOwn(dimsByName, name)) {\n          if (process.env.NODE_ENV !== 'production') {\n            errMsg_1 = 'dimension name \"' + name + '\" duplicated.';\n          }\n          throwError(errMsg_1);\n        }\n        dimsByName[name] = dimDefExt;\n      }\n    });\n  }\n  // If dimension definitions are not defined and can not be detected.\n  // e.g., pure data `[[11, 22], ...]`.\n  else {\n    for (var i = 0; i < internalSource.dimensionsDetectedCount || 0; i++) {\n      // Do not generete name or anything others. The consequence process in\n      // `transform` or `series` probably have there own name generation strategry.\n      dimensions.push({\n        index: i\n      });\n    }\n  }\n  // Implement public methods:\n  var rawItemGetter = getRawSourceItemGetter(sourceFormat, SERIES_LAYOUT_BY_COLUMN);\n  if (externalTransform.__isBuiltIn) {\n    extSource.getRawDataItem = function (dataIndex) {\n      return rawItemGetter(data, sourceHeaderCount, dimensions, dataIndex);\n    };\n    extSource.getRawData = bind(getRawData, null, internalSource);\n  }\n  extSource.cloneRawData = bind(cloneRawData, null, internalSource);\n  var rawCounter = getRawSourceDataCounter(sourceFormat, SERIES_LAYOUT_BY_COLUMN);\n  extSource.count = bind(rawCounter, null, data, sourceHeaderCount, dimensions);\n  var rawValueGetter = getRawSourceValueGetter(sourceFormat);\n  extSource.retrieveValue = function (dataIndex, dimIndex) {\n    var rawItem = rawItemGetter(data, sourceHeaderCount, dimensions, dataIndex);\n    return retrieveValueFromItem(rawItem, dimIndex);\n  };\n  var retrieveValueFromItem = extSource.retrieveValueFromItem = function (dataItem, dimIndex) {\n    if (dataItem == null) {\n      return;\n    }\n    var dimDef = dimensions[dimIndex];\n    // When `dimIndex` is `null`, `rawValueGetter` return the whole item.\n    if (dimDef) {\n      return rawValueGetter(dataItem, dimIndex, dimDef.name);\n    }\n  };\n  extSource.getDimensionInfo = bind(getDimensionInfo, null, dimensions, dimsByName);\n  extSource.cloneAllDimensionInfo = bind(cloneAllDimensionInfo, null, dimensions);\n  return extSource;\n}\nfunction getRawData(upstream) {\n  var sourceFormat = upstream.sourceFormat;\n  if (!isSupportedSourceFormat(sourceFormat)) {\n    var errMsg = '';\n    if (process.env.NODE_ENV !== 'production') {\n      errMsg = '`getRawData` is not supported in source format ' + sourceFormat;\n    }\n    throwError(errMsg);\n  }\n  return upstream.data;\n}\nfunction cloneRawData(upstream) {\n  var sourceFormat = upstream.sourceFormat;\n  var data = upstream.data;\n  if (!isSupportedSourceFormat(sourceFormat)) {\n    var errMsg = '';\n    if (process.env.NODE_ENV !== 'production') {\n      errMsg = '`cloneRawData` is not supported in source format ' + sourceFormat;\n    }\n    throwError(errMsg);\n  }\n  if (sourceFormat === SOURCE_FORMAT_ARRAY_ROWS) {\n    var result = [];\n    for (var i = 0, len = data.length; i < len; i++) {\n      // Not strictly clone for performance\n      result.push(data[i].slice());\n    }\n    return result;\n  } else if (sourceFormat === SOURCE_FORMAT_OBJECT_ROWS) {\n    var result = [];\n    for (var i = 0, len = data.length; i < len; i++) {\n      // Not strictly clone for performance\n      result.push(extend({}, data[i]));\n    }\n    return result;\n  }\n}\nfunction getDimensionInfo(dimensions, dimsByName, dim) {\n  if (dim == null) {\n    return;\n  }\n  // Keep the same logic as `List::getDimension` did.\n  if (isNumber(dim)\n  // If being a number-like string but not being defined a dimension name.\n  || !isNaN(dim) && !hasOwn(dimsByName, dim)) {\n    return dimensions[dim];\n  } else if (hasOwn(dimsByName, dim)) {\n    return dimsByName[dim];\n  }\n}\nfunction cloneAllDimensionInfo(dimensions) {\n  return clone(dimensions);\n}\nvar externalTransformMap = createHashMap();\nexport function registerExternalTransform(externalTransform) {\n  externalTransform = clone(externalTransform);\n  var type = externalTransform.type;\n  var errMsg = '';\n  if (!type) {\n    if (process.env.NODE_ENV !== 'production') {\n      errMsg = 'Must have a `type` when `registerTransform`.';\n    }\n    throwError(errMsg);\n  }\n  var typeParsed = type.split(':');\n  if (typeParsed.length !== 2) {\n    if (process.env.NODE_ENV !== 'production') {\n      errMsg = 'Name must include namespace like \"ns:regression\".';\n    }\n    throwError(errMsg);\n  }\n  // Namespace 'echarts:xxx' is official namespace, where the transforms should\n  // be called directly via 'xxx' rather than 'echarts:xxx'.\n  var isBuiltIn = false;\n  if (typeParsed[0] === 'echarts') {\n    type = typeParsed[1];\n    isBuiltIn = true;\n  }\n  externalTransform.__isBuiltIn = isBuiltIn;\n  externalTransformMap.set(type, externalTransform);\n}\nexport function applyDataTransform(rawTransOption, sourceList, infoForPrint) {\n  var pipedTransOption = normalizeToArray(rawTransOption);\n  var pipeLen = pipedTransOption.length;\n  var errMsg = '';\n  if (!pipeLen) {\n    if (process.env.NODE_ENV !== 'production') {\n      errMsg = 'If `transform` declared, it should at least contain one transform.';\n    }\n    throwError(errMsg);\n  }\n  for (var i = 0, len = pipeLen; i < len; i++) {\n    var transOption = pipedTransOption[i];\n    sourceList = applySingleDataTransform(transOption, sourceList, infoForPrint, pipeLen === 1 ? null : i);\n    // piped transform only support single input, except the fist one.\n    // piped transform only support single output, except the last one.\n    if (i !== len - 1) {\n      sourceList.length = Math.max(sourceList.length, 1);\n    }\n  }\n  return sourceList;\n}\nfunction applySingleDataTransform(transOption, upSourceList, infoForPrint,\n// If `pipeIndex` is null/undefined, no piped transform.\npipeIndex) {\n  var errMsg = '';\n  if (!upSourceList.length) {\n    if (process.env.NODE_ENV !== 'production') {\n      errMsg = 'Must have at least one upstream dataset.';\n    }\n    throwError(errMsg);\n  }\n  if (!isObject(transOption)) {\n    if (process.env.NODE_ENV !== 'production') {\n      errMsg = 'transform declaration must be an object rather than ' + typeof transOption + '.';\n    }\n    throwError(errMsg);\n  }\n  var transType = transOption.type;\n  var externalTransform = externalTransformMap.get(transType);\n  if (!externalTransform) {\n    if (process.env.NODE_ENV !== 'production') {\n      errMsg = 'Can not find transform on type \"' + transType + '\".';\n    }\n    throwError(errMsg);\n  }\n  // Prepare source\n  var extUpSourceList = map(upSourceList, function (upSource) {\n    return createExternalSource(upSource, externalTransform);\n  });\n  var resultList = normalizeToArray(externalTransform.transform({\n    upstream: extUpSourceList[0],\n    upstreamList: extUpSourceList,\n    config: clone(transOption.config)\n  }));\n  if (process.env.NODE_ENV !== 'production') {\n    if (transOption.print) {\n      var printStrArr = map(resultList, function (extSource) {\n        var pipeIndexStr = pipeIndex != null ? ' === pipe index: ' + pipeIndex : '';\n        return ['=== dataset index: ' + infoForPrint.datasetIndex + pipeIndexStr + ' ===', '- transform result data:', makePrintable(extSource.data), '- transform result dimensions:', makePrintable(extSource.dimensions)].join('\\n');\n      }).join('\\n');\n      log(printStrArr);\n    }\n  }\n  return map(resultList, function (result, resultIndex) {\n    var errMsg = '';\n    if (!isObject(result)) {\n      if (process.env.NODE_ENV !== 'production') {\n        errMsg = 'A transform should not return some empty results.';\n      }\n      throwError(errMsg);\n    }\n    if (!result.data) {\n      if (process.env.NODE_ENV !== 'production') {\n        errMsg = 'Transform result data should be not be null or undefined';\n      }\n      throwError(errMsg);\n    }\n    var sourceFormat = detectSourceFormat(result.data);\n    if (!isSupportedSourceFormat(sourceFormat)) {\n      if (process.env.NODE_ENV !== 'production') {\n        errMsg = 'Transform result data should be array rows or object rows.';\n      }\n      throwError(errMsg);\n    }\n    var resultMetaRawOption;\n    var firstUpSource = upSourceList[0];\n    /**\r\n     * Intuitively, the end users known the content of the original `dataset.source`,\r\n     * calucating the transform result in mind.\r\n     * Suppose the original `dataset.source` is:\r\n     * ```js\r\n     * [\r\n     *     ['product', '2012', '2013', '2014', '2015'],\r\n     *     ['AAA', 41.1, 30.4, 65.1, 53.3],\r\n     *     ['BBB', 86.5, 92.1, 85.7, 83.1],\r\n     *     ['CCC', 24.1, 67.2, 79.5, 86.4]\r\n     * ]\r\n     * ```\r\n     * The dimension info have to be detected from the source data.\r\n     * Some of the transformers (like filter, sort) will follow the dimension info\r\n     * of upstream, while others use new dimensions (like aggregate).\r\n     * Transformer can output a field `dimensions` to define the its own output dimensions.\r\n     * We also allow transformers to ignore the output `dimensions` field, and\r\n     * inherit the upstream dimensions definition. It can reduce the burden of handling\r\n     * dimensions in transformers.\r\n     *\r\n     * See also [DIMENSION_INHERIT_RULE] in `sourceManager.ts`.\r\n     */\n    if (firstUpSource && resultIndex === 0\n    // If transformer returns `dimensions`, it means that the transformer has different\n    // dimensions definitions. We do not inherit anything from upstream.\n    && !result.dimensions) {\n      var startIndex = firstUpSource.startIndex;\n      // We copy the header of upstream to the result, because:\n      // (1) The returned data always does not contain header line and can not be used\n      // as dimension-detection. In this case we can not use \"detected dimensions\" of\n      // upstream directly, because it might be detected based on different `seriesLayoutBy`.\n      // (2) We should support that the series read the upstream source in `seriesLayoutBy: 'row'`.\n      // So the original detected header should be add to the result, otherwise they can not be read.\n      if (startIndex) {\n        result.data = firstUpSource.data.slice(0, startIndex).concat(result.data);\n      }\n      resultMetaRawOption = {\n        seriesLayoutBy: SERIES_LAYOUT_BY_COLUMN,\n        sourceHeader: startIndex,\n        dimensions: firstUpSource.metaRawOption.dimensions\n      };\n    } else {\n      resultMetaRawOption = {\n        seriesLayoutBy: SERIES_LAYOUT_BY_COLUMN,\n        sourceHeader: 0,\n        dimensions: result.dimensions\n      };\n    }\n    return createSource(result.data, resultMetaRawOption, null);\n  });\n}\nfunction isSupportedSourceFormat(sourceFormat) {\n  return sourceFormat === SOURCE_FORMAT_ARRAY_ROWS || sourceFormat === SOURCE_FORMAT_OBJECT_ROWS;\n}"], "names": [], "mappings": "AACA;;;;;;;;;;;;;;;;;AAiBA,GAGA;;CAEC,GAED;;;;;;;;;;;;;;;;;AAiBA;;;;;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;AACA;;;CAGC,GACD,IAAI,iBAAiB,WAAW,GAAE;IAChC,SAAS,kBAAkB;IAC3B,eAAe,SAAS,CAAC,UAAU,GAAG;QACpC,qCAAqC;QACrC,MAAM,IAAI,MAAM;IAClB;IACA,eAAe,SAAS,CAAC,cAAc,GAAG,SAAU,SAAS;QAC3D,qCAAqC;QACrC,MAAM,IAAI,MAAM;IAClB;IACA,eAAe,SAAS,CAAC,YAAY,GAAG;QACtC;IACF;IACA;;GAEC,GACD,eAAe,SAAS,CAAC,gBAAgB,GAAG,SAAU,GAAG;QACvD;IACF;IACA;;;;;;;;GAQC,GACD,eAAe,SAAS,CAAC,qBAAqB,GAAG;QAC/C;IACF;IACA,eAAe,SAAS,CAAC,KAAK,GAAG;QAC/B;IACF;IACA;;;;GAIC,GACD,eAAe,SAAS,CAAC,aAAa,GAAG,SAAU,SAAS,EAAE,QAAQ;QACpE;IACF;IACA,eAAe,SAAS,CAAC,qBAAqB,GAAG,SAAU,QAAQ,EAAE,QAAQ;QAC3E;IACF;IACA,eAAe,SAAS,CAAC,YAAY,GAAG,SAAU,MAAM,EAAE,OAAO;QAC/D,OAAO,CAAA,GAAA,mKAAA,CAAA,iBAAc,AAAD,EAAE,QAAQ;IAChC;IACA,OAAO;AACT;;AAEA,SAAS,qBAAqB,cAAc,EAAE,iBAAiB;IAC7D,IAAI,YAAY,IAAI;IACpB,IAAI,OAAO,eAAe,IAAI;IAC9B,IAAI,eAAe,UAAU,YAAY,GAAG,eAAe,YAAY;IACvE,IAAI,oBAAoB,eAAe,UAAU;IACjD,IAAI,SAAS;IACb,IAAI,eAAe,cAAc,KAAK,+IAAA,CAAA,0BAAuB,EAAE;QAC7D,4DAA4D;QAC5D,iEAAiE;QACjE,6DAA6D;QAC7D,IAAI,oDAAyB,cAAc;YACzC,SAAS;QACX;QACA,CAAA,GAAA,6IAAA,CAAA,aAAU,AAAD,EAAE;IACb;IACA,SAAS;IACT,kDAAkD;IAClD,sDAAsD;IACtD,oFAAoF;IACpF,4EAA4E;IAC5E,sDAAsD;IACtD,IAAI,aAAa,EAAE;IACnB,IAAI,aAAa,CAAC;IAClB,IAAI,UAAU,eAAe,gBAAgB;IAC7C,IAAI,SAAS;QACX,CAAA,GAAA,8IAAA,CAAA,OAAI,AAAD,EAAE,SAAS,SAAU,MAAM,EAAE,GAAG;YACjC,IAAI,OAAO,OAAO,IAAI;YACtB,IAAI,YAAY;gBACd,OAAO;gBACP,MAAM;gBACN,aAAa,OAAO,WAAW;YACjC;YACA,WAAW,IAAI,CAAC;YAChB,+EAA+E;YAC/E,oCAAoC;YACpC,IAAI,QAAQ,MAAM;gBAChB,2CAA2C;gBAC3C,2EAA2E;gBAC3E,yEAAyE;gBACzE,IAAI,WAAW;gBACf,IAAI,CAAA,GAAA,8IAAA,CAAA,SAAM,AAAD,EAAE,YAAY,OAAO;oBAC5B,IAAI,oDAAyB,cAAc;wBACzC,WAAW,qBAAqB,OAAO;oBACzC;oBACA,CAAA,GAAA,6IAAA,CAAA,aAAU,AAAD,EAAE;gBACb;gBACA,UAAU,CAAC,KAAK,GAAG;YACrB;QACF;IACF,OAGK;QACH,IAAK,IAAI,IAAI,GAAG,IAAI,eAAe,uBAAuB,IAAI,GAAG,IAAK;YACpE,sEAAsE;YACtE,6EAA6E;YAC7E,WAAW,IAAI,CAAC;gBACd,OAAO;YACT;QACF;IACF;IACA,4BAA4B;IAC5B,IAAI,gBAAgB,CAAA,GAAA,gKAAA,CAAA,yBAAsB,AAAD,EAAE,cAAc,+IAAA,CAAA,0BAAuB;IAChF,IAAI,kBAAkB,WAAW,EAAE;QACjC,UAAU,cAAc,GAAG,SAAU,SAAS;YAC5C,OAAO,cAAc,MAAM,mBAAmB,YAAY;QAC5D;QACA,UAAU,UAAU,GAAG,CAAA,GAAA,8IAAA,CAAA,OAAI,AAAD,EAAE,YAAY,MAAM;IAChD;IACA,UAAU,YAAY,GAAG,CAAA,GAAA,8IAAA,CAAA,OAAI,AAAD,EAAE,cAAc,MAAM;IAClD,IAAI,aAAa,CAAA,GAAA,gKAAA,CAAA,0BAAuB,AAAD,EAAE,cAAc,+IAAA,CAAA,0BAAuB;IAC9E,UAAU,KAAK,GAAG,CAAA,GAAA,8IAAA,CAAA,OAAI,AAAD,EAAE,YAAY,MAAM,MAAM,mBAAmB;IAClE,IAAI,iBAAiB,CAAA,GAAA,gKAAA,CAAA,0BAAuB,AAAD,EAAE;IAC7C,UAAU,aAAa,GAAG,SAAU,SAAS,EAAE,QAAQ;QACrD,IAAI,UAAU,cAAc,MAAM,mBAAmB,YAAY;QACjE,OAAO,sBAAsB,SAAS;IACxC;IACA,IAAI,wBAAwB,UAAU,qBAAqB,GAAG,SAAU,QAAQ,EAAE,QAAQ;QACxF,IAAI,YAAY,MAAM;YACpB;QACF;QACA,IAAI,SAAS,UAAU,CAAC,SAAS;QACjC,qEAAqE;QACrE,IAAI,QAAQ;YACV,OAAO,eAAe,UAAU,UAAU,OAAO,IAAI;QACvD;IACF;IACA,UAAU,gBAAgB,GAAG,CAAA,GAAA,8IAAA,CAAA,OAAI,AAAD,EAAE,kBAAkB,MAAM,YAAY;IACtE,UAAU,qBAAqB,GAAG,CAAA,GAAA,8IAAA,CAAA,OAAI,AAAD,EAAE,uBAAuB,MAAM;IACpE,OAAO;AACT;AACA,SAAS,WAAW,QAAQ;IAC1B,IAAI,eAAe,SAAS,YAAY;IACxC,IAAI,CAAC,wBAAwB,eAAe;QAC1C,IAAI,SAAS;QACb,IAAI,oDAAyB,cAAc;YACzC,SAAS,oDAAoD;QAC/D;QACA,CAAA,GAAA,6IAAA,CAAA,aAAU,AAAD,EAAE;IACb;IACA,OAAO,SAAS,IAAI;AACtB;AACA,SAAS,aAAa,QAAQ;IAC5B,IAAI,eAAe,SAAS,YAAY;IACxC,IAAI,OAAO,SAAS,IAAI;IACxB,IAAI,CAAC,wBAAwB,eAAe;QAC1C,IAAI,SAAS;QACb,IAAI,oDAAyB,cAAc;YACzC,SAAS,sDAAsD;QACjE;QACA,CAAA,GAAA,6IAAA,CAAA,aAAU,AAAD,EAAE;IACb;IACA,IAAI,iBAAiB,+IAAA,CAAA,2BAAwB,EAAE;QAC7C,IAAI,SAAS,EAAE;QACf,IAAK,IAAI,IAAI,GAAG,MAAM,KAAK,MAAM,EAAE,IAAI,KAAK,IAAK;YAC/C,qCAAqC;YACrC,OAAO,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC,KAAK;QAC3B;QACA,OAAO;IACT,OAAO,IAAI,iBAAiB,+IAAA,CAAA,4BAAyB,EAAE;QACrD,IAAI,SAAS,EAAE;QACf,IAAK,IAAI,IAAI,GAAG,MAAM,KAAK,MAAM,EAAE,IAAI,KAAK,IAAK;YAC/C,qCAAqC;YACrC,OAAO,IAAI,CAAC,CAAA,GAAA,8IAAA,CAAA,SAAM,AAAD,EAAE,CAAC,GAAG,IAAI,CAAC,EAAE;QAChC;QACA,OAAO;IACT;AACF;AACA,SAAS,iBAAiB,UAAU,EAAE,UAAU,EAAE,GAAG;IACnD,IAAI,OAAO,MAAM;QACf;IACF;IACA,mDAAmD;IACnD,IAAI,CAAA,GAAA,8IAAA,CAAA,WAAQ,AAAD,EAAE,QAEV,CAAC,MAAM,QAAQ,CAAC,CAAA,GAAA,8IAAA,CAAA,SAAM,AAAD,EAAE,YAAY,MAAM;QAC1C,OAAO,UAAU,CAAC,IAAI;IACxB,OAAO,IAAI,CAAA,GAAA,8IAAA,CAAA,SAAM,AAAD,EAAE,YAAY,MAAM;QAClC,OAAO,UAAU,CAAC,IAAI;IACxB;AACF;AACA,SAAS,sBAAsB,UAAU;IACvC,OAAO,CAAA,GAAA,8IAAA,CAAA,QAAK,AAAD,EAAE;AACf;AACA,IAAI,uBAAuB,CAAA,GAAA,8IAAA,CAAA,gBAAa,AAAD;AAChC,SAAS,0BAA0B,iBAAiB;IACzD,oBAAoB,CAAA,GAAA,8IAAA,CAAA,QAAK,AAAD,EAAE;IAC1B,IAAI,OAAO,kBAAkB,IAAI;IACjC,IAAI,SAAS;IACb,IAAI,CAAC,MAAM;QACT,IAAI,oDAAyB,cAAc;YACzC,SAAS;QACX;QACA,CAAA,GAAA,6IAAA,CAAA,aAAU,AAAD,EAAE;IACb;IACA,IAAI,aAAa,KAAK,KAAK,CAAC;IAC5B,IAAI,WAAW,MAAM,KAAK,GAAG;QAC3B,IAAI,oDAAyB,cAAc;YACzC,SAAS;QACX;QACA,CAAA,GAAA,6IAAA,CAAA,aAAU,AAAD,EAAE;IACb;IACA,6EAA6E;IAC7E,0DAA0D;IAC1D,IAAI,YAAY;IAChB,IAAI,UAAU,CAAC,EAAE,KAAK,WAAW;QAC/B,OAAO,UAAU,CAAC,EAAE;QACpB,YAAY;IACd;IACA,kBAAkB,WAAW,GAAG;IAChC,qBAAqB,GAAG,CAAC,MAAM;AACjC;AACO,SAAS,mBAAmB,cAAc,EAAE,UAAU,EAAE,YAAY;IACzE,IAAI,mBAAmB,CAAA,GAAA,+IAAA,CAAA,mBAAgB,AAAD,EAAE;IACxC,IAAI,UAAU,iBAAiB,MAAM;IACrC,IAAI,SAAS;IACb,IAAI,CAAC,SAAS;QACZ,IAAI,oDAAyB,cAAc;YACzC,SAAS;QACX;QACA,CAAA,GAAA,6IAAA,CAAA,aAAU,AAAD,EAAE;IACb;IACA,IAAK,IAAI,IAAI,GAAG,MAAM,SAAS,IAAI,KAAK,IAAK;QAC3C,IAAI,cAAc,gBAAgB,CAAC,EAAE;QACrC,aAAa,yBAAyB,aAAa,YAAY,cAAc,YAAY,IAAI,OAAO;QACpG,kEAAkE;QAClE,mEAAmE;QACnE,IAAI,MAAM,MAAM,GAAG;YACjB,WAAW,MAAM,GAAG,KAAK,GAAG,CAAC,WAAW,MAAM,EAAE;QAClD;IACF;IACA,OAAO;AACT;AACA,SAAS,yBAAyB,WAAW,EAAE,YAAY,EAAE,YAAY,EACzE,wDAAwD;AACxD,SAAS;IACP,IAAI,SAAS;IACb,IAAI,CAAC,aAAa,MAAM,EAAE;QACxB,IAAI,oDAAyB,cAAc;YACzC,SAAS;QACX;QACA,CAAA,GAAA,6IAAA,CAAA,aAAU,AAAD,EAAE;IACb;IACA,IAAI,CAAC,CAAA,GAAA,8IAAA,CAAA,WAAQ,AAAD,EAAE,cAAc;QAC1B,wCAA2C;YACzC,SAAS,yDAAyD,OAAO,cAAc;QACzF;QACA,CAAA,GAAA,6IAAA,CAAA,aAAU,AAAD,EAAE;IACb;IACA,IAAI,YAAY,YAAY,IAAI;IAChC,IAAI,oBAAoB,qBAAqB,GAAG,CAAC;IACjD,IAAI,CAAC,mBAAmB;QACtB,IAAI,oDAAyB,cAAc;YACzC,SAAS,qCAAqC,YAAY;QAC5D;QACA,CAAA,GAAA,6IAAA,CAAA,aAAU,AAAD,EAAE;IACb;IACA,iBAAiB;IACjB,IAAI,kBAAkB,CAAA,GAAA,8IAAA,CAAA,MAAG,AAAD,EAAE,cAAc,SAAU,QAAQ;QACxD,OAAO,qBAAqB,UAAU;IACxC;IACA,IAAI,aAAa,CAAA,GAAA,+IAAA,CAAA,mBAAgB,AAAD,EAAE,kBAAkB,SAAS,CAAC;QAC5D,UAAU,eAAe,CAAC,EAAE;QAC5B,cAAc;QACd,QAAQ,CAAA,GAAA,8IAAA,CAAA,QAAK,AAAD,EAAE,YAAY,MAAM;IAClC;IACA,wCAA2C;QACzC,IAAI,YAAY,KAAK,EAAE;YACrB,IAAI,cAAc,CAAA,GAAA,8IAAA,CAAA,MAAG,AAAD,EAAE,YAAY,SAAU,SAAS;gBACnD,IAAI,eAAe,aAAa,OAAO,sBAAsB,YAAY;gBACzE,OAAO;oBAAC,wBAAwB,aAAa,YAAY,GAAG,eAAe;oBAAQ;oBAA4B,CAAA,GAAA,6IAAA,CAAA,gBAAa,AAAD,EAAE,UAAU,IAAI;oBAAG;oBAAkC,CAAA,GAAA,6IAAA,CAAA,gBAAa,AAAD,EAAE,UAAU,UAAU;iBAAE,CAAC,IAAI,CAAC;YAC5N,GAAG,IAAI,CAAC;YACR,CAAA,GAAA,6IAAA,CAAA,MAAG,AAAD,EAAE;QACN;IACF;IACA,OAAO,CAAA,GAAA,8IAAA,CAAA,MAAG,AAAD,EAAE,YAAY,SAAU,MAAM,EAAE,WAAW;QAClD,IAAI,SAAS;QACb,IAAI,CAAC,CAAA,GAAA,8IAAA,CAAA,WAAQ,AAAD,EAAE,SAAS;YACrB,IAAI,oDAAyB,cAAc;gBACzC,SAAS;YACX;YACA,CAAA,GAAA,6IAAA,CAAA,aAAU,AAAD,EAAE;QACb;QACA,IAAI,CAAC,OAAO,IAAI,EAAE;YAChB,IAAI,oDAAyB,cAAc;gBACzC,SAAS;YACX;YACA,CAAA,GAAA,6IAAA,CAAA,aAAU,AAAD,EAAE;QACb;QACA,IAAI,eAAe,CAAA,GAAA,gJAAA,CAAA,qBAAkB,AAAD,EAAE,OAAO,IAAI;QACjD,IAAI,CAAC,wBAAwB,eAAe;YAC1C,IAAI,oDAAyB,cAAc;gBACzC,SAAS;YACX;YACA,CAAA,GAAA,6IAAA,CAAA,aAAU,AAAD,EAAE;QACb;QACA,IAAI;QACJ,IAAI,gBAAgB,YAAY,CAAC,EAAE;QACnC;;;;;;;;;;;;;;;;;;;;;KAqBC,GACD,IAAI,iBAAiB,gBAAgB,KAGlC,CAAC,OAAO,UAAU,EAAE;YACrB,IAAI,aAAa,cAAc,UAAU;YACzC,yDAAyD;YACzD,gFAAgF;YAChF,+EAA+E;YAC/E,uFAAuF;YACvF,6FAA6F;YAC7F,+FAA+F;YAC/F,IAAI,YAAY;gBACd,OAAO,IAAI,GAAG,cAAc,IAAI,CAAC,KAAK,CAAC,GAAG,YAAY,MAAM,CAAC,OAAO,IAAI;YAC1E;YACA,sBAAsB;gBACpB,gBAAgB,+IAAA,CAAA,0BAAuB;gBACvC,cAAc;gBACd,YAAY,cAAc,aAAa,CAAC,UAAU;YACpD;QACF,OAAO;YACL,sBAAsB;gBACpB,gBAAgB,+IAAA,CAAA,0BAAuB;gBACvC,cAAc;gBACd,YAAY,OAAO,UAAU;YAC/B;QACF;QACA,OAAO,CAAA,GAAA,gJAAA,CAAA,eAAY,AAAD,EAAE,OAAO,IAAI,EAAE,qBAAqB;IACxD;AACF;AACA,SAAS,wBAAwB,YAAY;IAC3C,OAAO,iBAAiB,+IAAA,CAAA,2BAAwB,IAAI,iBAAiB,+IAAA,CAAA,4BAAyB;AAChG", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1575, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/tindahan/sari-sari-admin/node_modules/echarts/lib/data/DataStore.js"], "sourcesContent": ["\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\r\n* Licensed to the Apache Software Foundation (ASF) under one\r\n* or more contributor license agreements.  See the NOTICE file\r\n* distributed with this work for additional information\r\n* regarding copyright ownership.  The ASF licenses this file\r\n* to you under the Apache License, Version 2.0 (the\r\n* \"License\"); you may not use this file except in compliance\r\n* with the License.  You may obtain a copy of the License at\r\n*\r\n*   http://www.apache.org/licenses/LICENSE-2.0\r\n*\r\n* Unless required by applicable law or agreed to in writing,\r\n* software distributed under the License is distributed on an\r\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\r\n* KIND, either express or implied.  See the License for the\r\n* specific language governing permissions and limitations\r\n* under the License.\r\n*/\nimport { assert, clone, createHashMap, isFunction, keys, map, reduce } from 'zrender/lib/core/util.js';\nimport { parseDataValue } from './helper/dataValueHelper.js';\nimport { shouldRetrieveDataByName } from './Source.js';\nvar UNDEFINED = 'undefined';\n/* global Float64Array, Int32Array, Uint32Array, Uint16Array */\n// Caution: MUST not use `new CtorUint32Array(arr, 0, len)`, because the Ctor of array is\n// different from the Ctor of typed array.\nexport var CtorUint32Array = typeof Uint32Array === UNDEFINED ? Array : Uint32Array;\nexport var CtorUint16Array = typeof Uint16Array === UNDEFINED ? Array : Uint16Array;\nexport var CtorInt32Array = typeof Int32Array === UNDEFINED ? Array : Int32Array;\nexport var CtorFloat64Array = typeof Float64Array === UNDEFINED ? Array : Float64Array;\n/**\r\n * Multi dimensional data store\r\n */\nvar dataCtors = {\n  'float': CtorFloat64Array,\n  'int': CtorInt32Array,\n  // Ordinal data type can be string or int\n  'ordinal': Array,\n  'number': Array,\n  'time': CtorFloat64Array\n};\nvar defaultDimValueGetters;\nfunction getIndicesCtor(rawCount) {\n  // The possible max value in this._indicies is always this._rawCount despite of filtering.\n  return rawCount > 65535 ? CtorUint32Array : CtorUint16Array;\n}\n;\nfunction getInitialExtent() {\n  return [Infinity, -Infinity];\n}\n;\nfunction cloneChunk(originalChunk) {\n  var Ctor = originalChunk.constructor;\n  // Only shallow clone is enough when Array.\n  return Ctor === Array ? originalChunk.slice() : new Ctor(originalChunk);\n}\nfunction prepareStore(store, dimIdx, dimType, end, append) {\n  var DataCtor = dataCtors[dimType || 'float'];\n  if (append) {\n    var oldStore = store[dimIdx];\n    var oldLen = oldStore && oldStore.length;\n    if (!(oldLen === end)) {\n      var newStore = new DataCtor(end);\n      // The cost of the copy is probably inconsiderable\n      // within the initial chunkSize.\n      for (var j = 0; j < oldLen; j++) {\n        newStore[j] = oldStore[j];\n      }\n      store[dimIdx] = newStore;\n    }\n  } else {\n    store[dimIdx] = new DataCtor(end);\n  }\n}\n;\n/**\r\n * Basically, DataStore API keep immutable.\r\n */\nvar DataStore = /** @class */function () {\n  function DataStore() {\n    this._chunks = [];\n    // It will not be calculated until needed.\n    this._rawExtent = [];\n    this._extent = [];\n    this._count = 0;\n    this._rawCount = 0;\n    this._calcDimNameToIdx = createHashMap();\n  }\n  /**\r\n   * Initialize from data\r\n   */\n  DataStore.prototype.initData = function (provider, inputDimensions, dimValueGetter) {\n    if (process.env.NODE_ENV !== 'production') {\n      assert(isFunction(provider.getItem) && isFunction(provider.count), 'Invalid data provider.');\n    }\n    this._provider = provider;\n    // Clear\n    this._chunks = [];\n    this._indices = null;\n    this.getRawIndex = this._getRawIdxIdentity;\n    var source = provider.getSource();\n    var defaultGetter = this.defaultDimValueGetter = defaultDimValueGetters[source.sourceFormat];\n    // Default dim value getter\n    this._dimValueGetter = dimValueGetter || defaultGetter;\n    // Reset raw extent.\n    this._rawExtent = [];\n    var willRetrieveDataByName = shouldRetrieveDataByName(source);\n    this._dimensions = map(inputDimensions, function (dim) {\n      if (process.env.NODE_ENV !== 'production') {\n        if (willRetrieveDataByName) {\n          assert(dim.property != null);\n        }\n      }\n      return {\n        // Only pick these two props. Not leak other properties like orderMeta.\n        type: dim.type,\n        property: dim.property\n      };\n    });\n    this._initDataFromProvider(0, provider.count());\n  };\n  DataStore.prototype.getProvider = function () {\n    return this._provider;\n  };\n  /**\r\n   * Caution: even when a `source` instance owned by a series, the created data store\r\n   * may still be shared by different sereis (the source hash does not use all `source`\r\n   * props, see `sourceManager`). In this case, the `source` props that are not used in\r\n   * hash (like `source.dimensionDefine`) probably only belongs to a certain series and\r\n   * thus should not be fetch here.\r\n   */\n  DataStore.prototype.getSource = function () {\n    return this._provider.getSource();\n  };\n  /**\r\n   * @caution Only used in dataStack.\r\n   */\n  DataStore.prototype.ensureCalculationDimension = function (dimName, type) {\n    var calcDimNameToIdx = this._calcDimNameToIdx;\n    var dimensions = this._dimensions;\n    var calcDimIdx = calcDimNameToIdx.get(dimName);\n    if (calcDimIdx != null) {\n      if (dimensions[calcDimIdx].type === type) {\n        return calcDimIdx;\n      }\n    } else {\n      calcDimIdx = dimensions.length;\n    }\n    dimensions[calcDimIdx] = {\n      type: type\n    };\n    calcDimNameToIdx.set(dimName, calcDimIdx);\n    this._chunks[calcDimIdx] = new dataCtors[type || 'float'](this._rawCount);\n    this._rawExtent[calcDimIdx] = getInitialExtent();\n    return calcDimIdx;\n  };\n  DataStore.prototype.collectOrdinalMeta = function (dimIdx, ordinalMeta) {\n    var chunk = this._chunks[dimIdx];\n    var dim = this._dimensions[dimIdx];\n    var rawExtents = this._rawExtent;\n    var offset = dim.ordinalOffset || 0;\n    var len = chunk.length;\n    if (offset === 0) {\n      // We need to reset the rawExtent if collect is from start.\n      // Because this dimension may be guessed as number and calcuating a wrong extent.\n      rawExtents[dimIdx] = getInitialExtent();\n    }\n    var dimRawExtent = rawExtents[dimIdx];\n    // Parse from previous data offset. len may be changed after appendData\n    for (var i = offset; i < len; i++) {\n      var val = chunk[i] = ordinalMeta.parseAndCollect(chunk[i]);\n      if (!isNaN(val)) {\n        dimRawExtent[0] = Math.min(val, dimRawExtent[0]);\n        dimRawExtent[1] = Math.max(val, dimRawExtent[1]);\n      }\n    }\n    dim.ordinalMeta = ordinalMeta;\n    dim.ordinalOffset = len;\n    dim.type = 'ordinal'; // Force to be ordinal\n  };\n  DataStore.prototype.getOrdinalMeta = function (dimIdx) {\n    var dimInfo = this._dimensions[dimIdx];\n    var ordinalMeta = dimInfo.ordinalMeta;\n    return ordinalMeta;\n  };\n  DataStore.prototype.getDimensionProperty = function (dimIndex) {\n    var item = this._dimensions[dimIndex];\n    return item && item.property;\n  };\n  /**\r\n   * Caution: Can be only called on raw data (before `this._indices` created).\r\n   */\n  DataStore.prototype.appendData = function (data) {\n    if (process.env.NODE_ENV !== 'production') {\n      assert(!this._indices, 'appendData can only be called on raw data.');\n    }\n    var provider = this._provider;\n    var start = this.count();\n    provider.appendData(data);\n    var end = provider.count();\n    if (!provider.persistent) {\n      end += start;\n    }\n    if (start < end) {\n      this._initDataFromProvider(start, end, true);\n    }\n    return [start, end];\n  };\n  DataStore.prototype.appendValues = function (values, minFillLen) {\n    var chunks = this._chunks;\n    var dimensions = this._dimensions;\n    var dimLen = dimensions.length;\n    var rawExtent = this._rawExtent;\n    var start = this.count();\n    var end = start + Math.max(values.length, minFillLen || 0);\n    for (var i = 0; i < dimLen; i++) {\n      var dim = dimensions[i];\n      prepareStore(chunks, i, dim.type, end, true);\n    }\n    var emptyDataItem = [];\n    for (var idx = start; idx < end; idx++) {\n      var sourceIdx = idx - start;\n      // Store the data by dimensions\n      for (var dimIdx = 0; dimIdx < dimLen; dimIdx++) {\n        var dim = dimensions[dimIdx];\n        var val = defaultDimValueGetters.arrayRows.call(this, values[sourceIdx] || emptyDataItem, dim.property, sourceIdx, dimIdx);\n        chunks[dimIdx][idx] = val;\n        var dimRawExtent = rawExtent[dimIdx];\n        val < dimRawExtent[0] && (dimRawExtent[0] = val);\n        val > dimRawExtent[1] && (dimRawExtent[1] = val);\n      }\n    }\n    this._rawCount = this._count = end;\n    return {\n      start: start,\n      end: end\n    };\n  };\n  DataStore.prototype._initDataFromProvider = function (start, end, append) {\n    var provider = this._provider;\n    var chunks = this._chunks;\n    var dimensions = this._dimensions;\n    var dimLen = dimensions.length;\n    var rawExtent = this._rawExtent;\n    var dimNames = map(dimensions, function (dim) {\n      return dim.property;\n    });\n    for (var i = 0; i < dimLen; i++) {\n      var dim = dimensions[i];\n      if (!rawExtent[i]) {\n        rawExtent[i] = getInitialExtent();\n      }\n      prepareStore(chunks, i, dim.type, end, append);\n    }\n    if (provider.fillStorage) {\n      provider.fillStorage(start, end, chunks, rawExtent);\n    } else {\n      var dataItem = [];\n      for (var idx = start; idx < end; idx++) {\n        // NOTICE: Try not to write things into dataItem\n        dataItem = provider.getItem(idx, dataItem);\n        // Each data item is value\n        // [1, 2]\n        // 2\n        // Bar chart, line chart which uses category axis\n        // only gives the 'y' value. 'x' value is the indices of category\n        // Use a tempValue to normalize the value to be a (x, y) value\n        // Store the data by dimensions\n        for (var dimIdx = 0; dimIdx < dimLen; dimIdx++) {\n          var dimStorage = chunks[dimIdx];\n          // PENDING NULL is empty or zero\n          var val = this._dimValueGetter(dataItem, dimNames[dimIdx], idx, dimIdx);\n          dimStorage[idx] = val;\n          var dimRawExtent = rawExtent[dimIdx];\n          val < dimRawExtent[0] && (dimRawExtent[0] = val);\n          val > dimRawExtent[1] && (dimRawExtent[1] = val);\n        }\n      }\n    }\n    if (!provider.persistent && provider.clean) {\n      // Clean unused data if data source is typed array.\n      provider.clean();\n    }\n    this._rawCount = this._count = end;\n    // Reset data extent\n    this._extent = [];\n  };\n  DataStore.prototype.count = function () {\n    return this._count;\n  };\n  /**\r\n   * Get value. Return NaN if idx is out of range.\r\n   */\n  DataStore.prototype.get = function (dim, idx) {\n    if (!(idx >= 0 && idx < this._count)) {\n      return NaN;\n    }\n    var dimStore = this._chunks[dim];\n    return dimStore ? dimStore[this.getRawIndex(idx)] : NaN;\n  };\n  DataStore.prototype.getValues = function (dimensions, idx) {\n    var values = [];\n    var dimArr = [];\n    if (idx == null) {\n      idx = dimensions;\n      // TODO get all from store?\n      dimensions = [];\n      // All dimensions\n      for (var i = 0; i < this._dimensions.length; i++) {\n        dimArr.push(i);\n      }\n    } else {\n      dimArr = dimensions;\n    }\n    for (var i = 0, len = dimArr.length; i < len; i++) {\n      values.push(this.get(dimArr[i], idx));\n    }\n    return values;\n  };\n  /**\r\n   * @param dim concrete dim\r\n   */\n  DataStore.prototype.getByRawIndex = function (dim, rawIdx) {\n    if (!(rawIdx >= 0 && rawIdx < this._rawCount)) {\n      return NaN;\n    }\n    var dimStore = this._chunks[dim];\n    return dimStore ? dimStore[rawIdx] : NaN;\n  };\n  /**\r\n   * Get sum of data in one dimension\r\n   */\n  DataStore.prototype.getSum = function (dim) {\n    var dimData = this._chunks[dim];\n    var sum = 0;\n    if (dimData) {\n      for (var i = 0, len = this.count(); i < len; i++) {\n        var value = this.get(dim, i);\n        if (!isNaN(value)) {\n          sum += value;\n        }\n      }\n    }\n    return sum;\n  };\n  /**\r\n   * Get median of data in one dimension\r\n   */\n  DataStore.prototype.getMedian = function (dim) {\n    var dimDataArray = [];\n    // map all data of one dimension\n    this.each([dim], function (val) {\n      if (!isNaN(val)) {\n        dimDataArray.push(val);\n      }\n    });\n    // TODO\n    // Use quick select?\n    var sortedDimDataArray = dimDataArray.sort(function (a, b) {\n      return a - b;\n    });\n    var len = this.count();\n    // calculate median\n    return len === 0 ? 0 : len % 2 === 1 ? sortedDimDataArray[(len - 1) / 2] : (sortedDimDataArray[len / 2] + sortedDimDataArray[len / 2 - 1]) / 2;\n  };\n  /**\r\n   * Retrieve the index with given raw data index.\r\n   */\n  DataStore.prototype.indexOfRawIndex = function (rawIndex) {\n    if (rawIndex >= this._rawCount || rawIndex < 0) {\n      return -1;\n    }\n    if (!this._indices) {\n      return rawIndex;\n    }\n    // Indices are ascending\n    var indices = this._indices;\n    // If rawIndex === dataIndex\n    var rawDataIndex = indices[rawIndex];\n    if (rawDataIndex != null && rawDataIndex < this._count && rawDataIndex === rawIndex) {\n      return rawIndex;\n    }\n    var left = 0;\n    var right = this._count - 1;\n    while (left <= right) {\n      var mid = (left + right) / 2 | 0;\n      if (indices[mid] < rawIndex) {\n        left = mid + 1;\n      } else if (indices[mid] > rawIndex) {\n        right = mid - 1;\n      } else {\n        return mid;\n      }\n    }\n    return -1;\n  };\n  /**\r\n   * Retrieve the index of nearest value.\r\n   * @param dim\r\n   * @param value\r\n   * @param [maxDistance=Infinity]\r\n   * @return If and only if multiple indices have\r\n   *         the same value, they are put to the result.\r\n   */\n  DataStore.prototype.indicesOfNearest = function (dim, value, maxDistance) {\n    var chunks = this._chunks;\n    var dimData = chunks[dim];\n    var nearestIndices = [];\n    if (!dimData) {\n      return nearestIndices;\n    }\n    if (maxDistance == null) {\n      maxDistance = Infinity;\n    }\n    var minDist = Infinity;\n    var minDiff = -1;\n    var nearestIndicesLen = 0;\n    // Check the test case of `test/ut/spec/data/SeriesData.js`.\n    for (var i = 0, len = this.count(); i < len; i++) {\n      var dataIndex = this.getRawIndex(i);\n      var diff = value - dimData[dataIndex];\n      var dist = Math.abs(diff);\n      if (dist <= maxDistance) {\n        // When the `value` is at the middle of `this.get(dim, i)` and `this.get(dim, i+1)`,\n        // we'd better not push both of them to `nearestIndices`, otherwise it is easy to\n        // get more than one item in `nearestIndices` (more specifically, in `tooltip`).\n        // So we choose the one that `diff >= 0` in this case.\n        // But if `this.get(dim, i)` and `this.get(dim, j)` get the same value, both of them\n        // should be push to `nearestIndices`.\n        if (dist < minDist || dist === minDist && diff >= 0 && minDiff < 0) {\n          minDist = dist;\n          minDiff = diff;\n          nearestIndicesLen = 0;\n        }\n        if (diff === minDiff) {\n          nearestIndices[nearestIndicesLen++] = i;\n        }\n      }\n    }\n    nearestIndices.length = nearestIndicesLen;\n    return nearestIndices;\n  };\n  DataStore.prototype.getIndices = function () {\n    var newIndices;\n    var indices = this._indices;\n    if (indices) {\n      var Ctor = indices.constructor;\n      var thisCount = this._count;\n      // `new Array(a, b, c)` is different from `new Uint32Array(a, b, c)`.\n      if (Ctor === Array) {\n        newIndices = new Ctor(thisCount);\n        for (var i = 0; i < thisCount; i++) {\n          newIndices[i] = indices[i];\n        }\n      } else {\n        newIndices = new Ctor(indices.buffer, 0, thisCount);\n      }\n    } else {\n      var Ctor = getIndicesCtor(this._rawCount);\n      newIndices = new Ctor(this.count());\n      for (var i = 0; i < newIndices.length; i++) {\n        newIndices[i] = i;\n      }\n    }\n    return newIndices;\n  };\n  /**\r\n   * Data filter.\r\n   */\n  DataStore.prototype.filter = function (dims, cb) {\n    if (!this._count) {\n      return this;\n    }\n    var newStore = this.clone();\n    var count = newStore.count();\n    var Ctor = getIndicesCtor(newStore._rawCount);\n    var newIndices = new Ctor(count);\n    var value = [];\n    var dimSize = dims.length;\n    var offset = 0;\n    var dim0 = dims[0];\n    var chunks = newStore._chunks;\n    for (var i = 0; i < count; i++) {\n      var keep = void 0;\n      var rawIdx = newStore.getRawIndex(i);\n      // Simple optimization\n      if (dimSize === 0) {\n        keep = cb(i);\n      } else if (dimSize === 1) {\n        var val = chunks[dim0][rawIdx];\n        keep = cb(val, i);\n      } else {\n        var k = 0;\n        for (; k < dimSize; k++) {\n          value[k] = chunks[dims[k]][rawIdx];\n        }\n        value[k] = i;\n        keep = cb.apply(null, value);\n      }\n      if (keep) {\n        newIndices[offset++] = rawIdx;\n      }\n    }\n    // Set indices after filtered.\n    if (offset < count) {\n      newStore._indices = newIndices;\n    }\n    newStore._count = offset;\n    // Reset data extent\n    newStore._extent = [];\n    newStore._updateGetRawIdx();\n    return newStore;\n  };\n  /**\r\n   * Select data in range. (For optimization of filter)\r\n   * (Manually inline code, support 5 million data filtering in data zoom.)\r\n   */\n  DataStore.prototype.selectRange = function (range) {\n    var newStore = this.clone();\n    var len = newStore._count;\n    if (!len) {\n      return this;\n    }\n    var dims = keys(range);\n    var dimSize = dims.length;\n    if (!dimSize) {\n      return this;\n    }\n    var originalCount = newStore.count();\n    var Ctor = getIndicesCtor(newStore._rawCount);\n    var newIndices = new Ctor(originalCount);\n    var offset = 0;\n    var dim0 = dims[0];\n    var min = range[dim0][0];\n    var max = range[dim0][1];\n    var storeArr = newStore._chunks;\n    var quickFinished = false;\n    if (!newStore._indices) {\n      // Extreme optimization for common case. About 2x faster in chrome.\n      var idx = 0;\n      if (dimSize === 1) {\n        var dimStorage = storeArr[dims[0]];\n        for (var i = 0; i < len; i++) {\n          var val = dimStorage[i];\n          // NaN will not be filtered. Consider the case, in line chart, empty\n          // value indicates the line should be broken. But for the case like\n          // scatter plot, a data item with empty value will not be rendered,\n          // but the axis extent may be effected if some other dim of the data\n          // item has value. Fortunately it is not a significant negative effect.\n          if (val >= min && val <= max || isNaN(val)) {\n            newIndices[offset++] = idx;\n          }\n          idx++;\n        }\n        quickFinished = true;\n      } else if (dimSize === 2) {\n        var dimStorage = storeArr[dims[0]];\n        var dimStorage2 = storeArr[dims[1]];\n        var min2 = range[dims[1]][0];\n        var max2 = range[dims[1]][1];\n        for (var i = 0; i < len; i++) {\n          var val = dimStorage[i];\n          var val2 = dimStorage2[i];\n          // Do not filter NaN, see comment above.\n          if ((val >= min && val <= max || isNaN(val)) && (val2 >= min2 && val2 <= max2 || isNaN(val2))) {\n            newIndices[offset++] = idx;\n          }\n          idx++;\n        }\n        quickFinished = true;\n      }\n    }\n    if (!quickFinished) {\n      if (dimSize === 1) {\n        for (var i = 0; i < originalCount; i++) {\n          var rawIndex = newStore.getRawIndex(i);\n          var val = storeArr[dims[0]][rawIndex];\n          // Do not filter NaN, see comment above.\n          if (val >= min && val <= max || isNaN(val)) {\n            newIndices[offset++] = rawIndex;\n          }\n        }\n      } else {\n        for (var i = 0; i < originalCount; i++) {\n          var keep = true;\n          var rawIndex = newStore.getRawIndex(i);\n          for (var k = 0; k < dimSize; k++) {\n            var dimk = dims[k];\n            var val = storeArr[dimk][rawIndex];\n            // Do not filter NaN, see comment above.\n            if (val < range[dimk][0] || val > range[dimk][1]) {\n              keep = false;\n            }\n          }\n          if (keep) {\n            newIndices[offset++] = newStore.getRawIndex(i);\n          }\n        }\n      }\n    }\n    // Set indices after filtered.\n    if (offset < originalCount) {\n      newStore._indices = newIndices;\n    }\n    newStore._count = offset;\n    // Reset data extent\n    newStore._extent = [];\n    newStore._updateGetRawIdx();\n    return newStore;\n  };\n  // /**\n  //  * Data mapping to a plain array\n  //  */\n  // mapArray(dims: DimensionIndex[], cb: MapArrayCb): any[] {\n  //     const result: any[] = [];\n  //     this.each(dims, function () {\n  //         result.push(cb && (cb as MapArrayCb).apply(null, arguments));\n  //     });\n  //     return result;\n  // }\n  /**\r\n   * Data mapping to a new List with given dimensions\r\n   */\n  DataStore.prototype.map = function (dims, cb) {\n    // TODO only clone picked chunks.\n    var target = this.clone(dims);\n    this._updateDims(target, dims, cb);\n    return target;\n  };\n  /**\r\n   * @caution Danger!! Only used in dataStack.\r\n   */\n  DataStore.prototype.modify = function (dims, cb) {\n    this._updateDims(this, dims, cb);\n  };\n  DataStore.prototype._updateDims = function (target, dims, cb) {\n    var targetChunks = target._chunks;\n    var tmpRetValue = [];\n    var dimSize = dims.length;\n    var dataCount = target.count();\n    var values = [];\n    var rawExtent = target._rawExtent;\n    for (var i = 0; i < dims.length; i++) {\n      rawExtent[dims[i]] = getInitialExtent();\n    }\n    for (var dataIndex = 0; dataIndex < dataCount; dataIndex++) {\n      var rawIndex = target.getRawIndex(dataIndex);\n      for (var k = 0; k < dimSize; k++) {\n        values[k] = targetChunks[dims[k]][rawIndex];\n      }\n      values[dimSize] = dataIndex;\n      var retValue = cb && cb.apply(null, values);\n      if (retValue != null) {\n        // a number or string (in oridinal dimension)?\n        if (typeof retValue !== 'object') {\n          tmpRetValue[0] = retValue;\n          retValue = tmpRetValue;\n        }\n        for (var i = 0; i < retValue.length; i++) {\n          var dim = dims[i];\n          var val = retValue[i];\n          var rawExtentOnDim = rawExtent[dim];\n          var dimStore = targetChunks[dim];\n          if (dimStore) {\n            dimStore[rawIndex] = val;\n          }\n          if (val < rawExtentOnDim[0]) {\n            rawExtentOnDim[0] = val;\n          }\n          if (val > rawExtentOnDim[1]) {\n            rawExtentOnDim[1] = val;\n          }\n        }\n      }\n    }\n  };\n  /**\r\n   * Large data down sampling using largest-triangle-three-buckets\r\n   * @param {string} valueDimension\r\n   * @param {number} targetCount\r\n   */\n  DataStore.prototype.lttbDownSample = function (valueDimension, rate) {\n    var target = this.clone([valueDimension], true);\n    var targetStorage = target._chunks;\n    var dimStore = targetStorage[valueDimension];\n    var len = this.count();\n    var sampledIndex = 0;\n    var frameSize = Math.floor(1 / rate);\n    var currentRawIndex = this.getRawIndex(0);\n    var maxArea;\n    var area;\n    var nextRawIndex;\n    var newIndices = new (getIndicesCtor(this._rawCount))(Math.min((Math.ceil(len / frameSize) + 2) * 2, len));\n    // First frame use the first data.\n    newIndices[sampledIndex++] = currentRawIndex;\n    for (var i = 1; i < len - 1; i += frameSize) {\n      var nextFrameStart = Math.min(i + frameSize, len - 1);\n      var nextFrameEnd = Math.min(i + frameSize * 2, len);\n      var avgX = (nextFrameEnd + nextFrameStart) / 2;\n      var avgY = 0;\n      for (var idx = nextFrameStart; idx < nextFrameEnd; idx++) {\n        var rawIndex = this.getRawIndex(idx);\n        var y = dimStore[rawIndex];\n        if (isNaN(y)) {\n          continue;\n        }\n        avgY += y;\n      }\n      avgY /= nextFrameEnd - nextFrameStart;\n      var frameStart = i;\n      var frameEnd = Math.min(i + frameSize, len);\n      var pointAX = i - 1;\n      var pointAY = dimStore[currentRawIndex];\n      maxArea = -1;\n      nextRawIndex = frameStart;\n      var firstNaNIndex = -1;\n      var countNaN = 0;\n      // Find a point from current frame that construct a triangle with largest area with previous selected point\n      // And the average of next frame.\n      for (var idx = frameStart; idx < frameEnd; idx++) {\n        var rawIndex = this.getRawIndex(idx);\n        var y = dimStore[rawIndex];\n        if (isNaN(y)) {\n          countNaN++;\n          if (firstNaNIndex < 0) {\n            firstNaNIndex = rawIndex;\n          }\n          continue;\n        }\n        // Calculate triangle area over three buckets\n        area = Math.abs((pointAX - avgX) * (y - pointAY) - (pointAX - idx) * (avgY - pointAY));\n        if (area > maxArea) {\n          maxArea = area;\n          nextRawIndex = rawIndex; // Next a is this b\n        }\n      }\n      if (countNaN > 0 && countNaN < frameEnd - frameStart) {\n        // Append first NaN point in every bucket.\n        // It is necessary to ensure the correct order of indices.\n        newIndices[sampledIndex++] = Math.min(firstNaNIndex, nextRawIndex);\n        nextRawIndex = Math.max(firstNaNIndex, nextRawIndex);\n      }\n      newIndices[sampledIndex++] = nextRawIndex;\n      currentRawIndex = nextRawIndex; // This a is the next a (chosen b)\n    }\n    // First frame use the last data.\n    newIndices[sampledIndex++] = this.getRawIndex(len - 1);\n    target._count = sampledIndex;\n    target._indices = newIndices;\n    target.getRawIndex = this._getRawIdx;\n    return target;\n  };\n  /**\r\n   * Large data down sampling using min-max\r\n   * @param {string} valueDimension\r\n   * @param {number} rate\r\n   */\n  DataStore.prototype.minmaxDownSample = function (valueDimension, rate) {\n    var target = this.clone([valueDimension], true);\n    var targetStorage = target._chunks;\n    var frameSize = Math.floor(1 / rate);\n    var dimStore = targetStorage[valueDimension];\n    var len = this.count();\n    // Each frame results in 2 data points, one for min and one for max\n    var newIndices = new (getIndicesCtor(this._rawCount))(Math.ceil(len / frameSize) * 2);\n    var offset = 0;\n    for (var i = 0; i < len; i += frameSize) {\n      var minIndex = i;\n      var minValue = dimStore[this.getRawIndex(minIndex)];\n      var maxIndex = i;\n      var maxValue = dimStore[this.getRawIndex(maxIndex)];\n      var thisFrameSize = frameSize;\n      // Handle final smaller frame\n      if (i + frameSize > len) {\n        thisFrameSize = len - i;\n      }\n      // Determine min and max within the current frame\n      for (var k = 0; k < thisFrameSize; k++) {\n        var rawIndex = this.getRawIndex(i + k);\n        var value = dimStore[rawIndex];\n        if (value < minValue) {\n          minValue = value;\n          minIndex = i + k;\n        }\n        if (value > maxValue) {\n          maxValue = value;\n          maxIndex = i + k;\n        }\n      }\n      var rawMinIndex = this.getRawIndex(minIndex);\n      var rawMaxIndex = this.getRawIndex(maxIndex);\n      // Set the order of the min and max values, based on their ordering in the frame\n      if (minIndex < maxIndex) {\n        newIndices[offset++] = rawMinIndex;\n        newIndices[offset++] = rawMaxIndex;\n      } else {\n        newIndices[offset++] = rawMaxIndex;\n        newIndices[offset++] = rawMinIndex;\n      }\n    }\n    target._count = offset;\n    target._indices = newIndices;\n    target._updateGetRawIdx();\n    return target;\n  };\n  /**\r\n   * Large data down sampling on given dimension\r\n   * @param sampleIndex Sample index for name and id\r\n   */\n  DataStore.prototype.downSample = function (dimension, rate, sampleValue, sampleIndex) {\n    var target = this.clone([dimension], true);\n    var targetStorage = target._chunks;\n    var frameValues = [];\n    var frameSize = Math.floor(1 / rate);\n    var dimStore = targetStorage[dimension];\n    var len = this.count();\n    var rawExtentOnDim = target._rawExtent[dimension] = getInitialExtent();\n    var newIndices = new (getIndicesCtor(this._rawCount))(Math.ceil(len / frameSize));\n    var offset = 0;\n    for (var i = 0; i < len; i += frameSize) {\n      // Last frame\n      if (frameSize > len - i) {\n        frameSize = len - i;\n        frameValues.length = frameSize;\n      }\n      for (var k = 0; k < frameSize; k++) {\n        var dataIdx = this.getRawIndex(i + k);\n        frameValues[k] = dimStore[dataIdx];\n      }\n      var value = sampleValue(frameValues);\n      var sampleFrameIdx = this.getRawIndex(Math.min(i + sampleIndex(frameValues, value) || 0, len - 1));\n      // Only write value on the filtered data\n      dimStore[sampleFrameIdx] = value;\n      if (value < rawExtentOnDim[0]) {\n        rawExtentOnDim[0] = value;\n      }\n      if (value > rawExtentOnDim[1]) {\n        rawExtentOnDim[1] = value;\n      }\n      newIndices[offset++] = sampleFrameIdx;\n    }\n    target._count = offset;\n    target._indices = newIndices;\n    target._updateGetRawIdx();\n    return target;\n  };\n  /**\r\n   * Data iteration\r\n   * @param ctx default this\r\n   * @example\r\n   *  list.each('x', function (x, idx) {});\r\n   *  list.each(['x', 'y'], function (x, y, idx) {});\r\n   *  list.each(function (idx) {})\r\n   */\n  DataStore.prototype.each = function (dims, cb) {\n    if (!this._count) {\n      return;\n    }\n    var dimSize = dims.length;\n    var chunks = this._chunks;\n    for (var i = 0, len = this.count(); i < len; i++) {\n      var rawIdx = this.getRawIndex(i);\n      // Simple optimization\n      switch (dimSize) {\n        case 0:\n          cb(i);\n          break;\n        case 1:\n          cb(chunks[dims[0]][rawIdx], i);\n          break;\n        case 2:\n          cb(chunks[dims[0]][rawIdx], chunks[dims[1]][rawIdx], i);\n          break;\n        default:\n          var k = 0;\n          var value = [];\n          for (; k < dimSize; k++) {\n            value[k] = chunks[dims[k]][rawIdx];\n          }\n          // Index\n          value[k] = i;\n          cb.apply(null, value);\n      }\n    }\n  };\n  /**\r\n   * Get extent of data in one dimension\r\n   */\n  DataStore.prototype.getDataExtent = function (dim) {\n    // Make sure use concrete dim as cache name.\n    var dimData = this._chunks[dim];\n    var initialExtent = getInitialExtent();\n    if (!dimData) {\n      return initialExtent;\n    }\n    // Make more strict checkings to ensure hitting cache.\n    var currEnd = this.count();\n    // Consider the most cases when using data zoom, `getDataExtent`\n    // happened before filtering. We cache raw extent, which is not\n    // necessary to be cleared and recalculated when restore data.\n    var useRaw = !this._indices;\n    var dimExtent;\n    if (useRaw) {\n      return this._rawExtent[dim].slice();\n    }\n    dimExtent = this._extent[dim];\n    if (dimExtent) {\n      return dimExtent.slice();\n    }\n    dimExtent = initialExtent;\n    var min = dimExtent[0];\n    var max = dimExtent[1];\n    for (var i = 0; i < currEnd; i++) {\n      var rawIdx = this.getRawIndex(i);\n      var value = dimData[rawIdx];\n      value < min && (min = value);\n      value > max && (max = value);\n    }\n    dimExtent = [min, max];\n    this._extent[dim] = dimExtent;\n    return dimExtent;\n  };\n  /**\r\n   * Get raw data item\r\n   */\n  DataStore.prototype.getRawDataItem = function (idx) {\n    var rawIdx = this.getRawIndex(idx);\n    if (!this._provider.persistent) {\n      var val = [];\n      var chunks = this._chunks;\n      for (var i = 0; i < chunks.length; i++) {\n        val.push(chunks[i][rawIdx]);\n      }\n      return val;\n    } else {\n      return this._provider.getItem(rawIdx);\n    }\n  };\n  /**\r\n   * Clone shallow.\r\n   *\r\n   * @param clonedDims Determine which dims to clone. Will share the data if not specified.\r\n   */\n  DataStore.prototype.clone = function (clonedDims, ignoreIndices) {\n    var target = new DataStore();\n    var chunks = this._chunks;\n    var clonedDimsMap = clonedDims && reduce(clonedDims, function (obj, dimIdx) {\n      obj[dimIdx] = true;\n      return obj;\n    }, {});\n    if (clonedDimsMap) {\n      for (var i = 0; i < chunks.length; i++) {\n        // Not clone if dim is not picked.\n        target._chunks[i] = !clonedDimsMap[i] ? chunks[i] : cloneChunk(chunks[i]);\n      }\n    } else {\n      target._chunks = chunks;\n    }\n    this._copyCommonProps(target);\n    if (!ignoreIndices) {\n      target._indices = this._cloneIndices();\n    }\n    target._updateGetRawIdx();\n    return target;\n  };\n  DataStore.prototype._copyCommonProps = function (target) {\n    target._count = this._count;\n    target._rawCount = this._rawCount;\n    target._provider = this._provider;\n    target._dimensions = this._dimensions;\n    target._extent = clone(this._extent);\n    target._rawExtent = clone(this._rawExtent);\n  };\n  DataStore.prototype._cloneIndices = function () {\n    if (this._indices) {\n      var Ctor = this._indices.constructor;\n      var indices = void 0;\n      if (Ctor === Array) {\n        var thisCount = this._indices.length;\n        indices = new Ctor(thisCount);\n        for (var i = 0; i < thisCount; i++) {\n          indices[i] = this._indices[i];\n        }\n      } else {\n        indices = new Ctor(this._indices);\n      }\n      return indices;\n    }\n    return null;\n  };\n  DataStore.prototype._getRawIdxIdentity = function (idx) {\n    return idx;\n  };\n  DataStore.prototype._getRawIdx = function (idx) {\n    if (idx < this._count && idx >= 0) {\n      return this._indices[idx];\n    }\n    return -1;\n  };\n  DataStore.prototype._updateGetRawIdx = function () {\n    this.getRawIndex = this._indices ? this._getRawIdx : this._getRawIdxIdentity;\n  };\n  DataStore.internalField = function () {\n    function getDimValueSimply(dataItem, property, dataIndex, dimIndex) {\n      return parseDataValue(dataItem[dimIndex], this._dimensions[dimIndex]);\n    }\n    defaultDimValueGetters = {\n      arrayRows: getDimValueSimply,\n      objectRows: function (dataItem, property, dataIndex, dimIndex) {\n        return parseDataValue(dataItem[property], this._dimensions[dimIndex]);\n      },\n      keyedColumns: getDimValueSimply,\n      original: function (dataItem, property, dataIndex, dimIndex) {\n        // Performance sensitive, do not use modelUtil.getDataItemValue.\n        // If dataItem is an plain object with no value field, the let `value`\n        // will be assigned with the object, but it will be tread correctly\n        // in the `convertValue`.\n        var value = dataItem && (dataItem.value == null ? dataItem : dataItem.value);\n        return parseDataValue(value instanceof Array ? value[dimIndex]\n        // If value is a single number or something else not array.\n        : value, this._dimensions[dimIndex]);\n      },\n      typedArray: function (dataItem, property, dataIndex, dimIndex) {\n        return dataItem[dimIndex];\n      }\n    };\n  }();\n  return DataStore;\n}();\nexport default DataStore;"], "names": [], "mappings": "AACA;;;;;;;;;;;;;;;;;AAiBA,GAGA;;CAEC,GAED;;;;;;;;;;;;;;;;;AAiBA;;;;;;;AACA;AACA;AACA;;;;AACA,IAAI,YAAY;AAIT,IAAI,kBAAkB,OAAO,gBAAgB,YAAY,QAAQ;AACjE,IAAI,kBAAkB,OAAO,gBAAgB,YAAY,QAAQ;AACjE,IAAI,iBAAiB,OAAO,eAAe,YAAY,QAAQ;AAC/D,IAAI,mBAAmB,OAAO,iBAAiB,YAAY,QAAQ;AAC1E;;CAEC,GACD,IAAI,YAAY;IACd,SAAS;IACT,OAAO;IACP,yCAAyC;IACzC,WAAW;IACX,UAAU;IACV,QAAQ;AACV;AACA,IAAI;AACJ,SAAS,eAAe,QAAQ;IAC9B,0FAA0F;IAC1F,OAAO,WAAW,QAAQ,kBAAkB;AAC9C;;AAEA,SAAS;IACP,OAAO;QAAC;QAAU,CAAC;KAAS;AAC9B;;AAEA,SAAS,WAAW,aAAa;IAC/B,IAAI,OAAO,cAAc,WAAW;IACpC,2CAA2C;IAC3C,OAAO,SAAS,QAAQ,cAAc,KAAK,KAAK,IAAI,KAAK;AAC3D;AACA,SAAS,aAAa,KAAK,EAAE,MAAM,EAAE,OAAO,EAAE,GAAG,EAAE,MAAM;IACvD,IAAI,WAAW,SAAS,CAAC,WAAW,QAAQ;IAC5C,IAAI,QAAQ;QACV,IAAI,WAAW,KAAK,CAAC,OAAO;QAC5B,IAAI,SAAS,YAAY,SAAS,MAAM;QACxC,IAAI,CAAC,CAAC,WAAW,GAAG,GAAG;YACrB,IAAI,WAAW,IAAI,SAAS;YAC5B,kDAAkD;YAClD,gCAAgC;YAChC,IAAK,IAAI,IAAI,GAAG,IAAI,QAAQ,IAAK;gBAC/B,QAAQ,CAAC,EAAE,GAAG,QAAQ,CAAC,EAAE;YAC3B;YACA,KAAK,CAAC,OAAO,GAAG;QAClB;IACF,OAAO;QACL,KAAK,CAAC,OAAO,GAAG,IAAI,SAAS;IAC/B;AACF;;AAEA;;CAEC,GACD,IAAI,YAAY,WAAW,GAAE;IAC3B,SAAS;QACP,IAAI,CAAC,OAAO,GAAG,EAAE;QACjB,0CAA0C;QAC1C,IAAI,CAAC,UAAU,GAAG,EAAE;QACpB,IAAI,CAAC,OAAO,GAAG,EAAE;QACjB,IAAI,CAAC,MAAM,GAAG;QACd,IAAI,CAAC,SAAS,GAAG;QACjB,IAAI,CAAC,iBAAiB,GAAG,CAAA,GAAA,8IAAA,CAAA,gBAAa,AAAD;IACvC;IACA;;GAEC,GACD,UAAU,SAAS,CAAC,QAAQ,GAAG,SAAU,QAAQ,EAAE,eAAe,EAAE,cAAc;QAChF,wCAA2C;YACzC,CAAA,GAAA,8IAAA,CAAA,SAAM,AAAD,EAAE,CAAA,GAAA,8IAAA,CAAA,aAAU,AAAD,EAAE,SAAS,OAAO,KAAK,CAAA,GAAA,8IAAA,CAAA,aAAU,AAAD,EAAE,SAAS,KAAK,GAAG;QACrE;QACA,IAAI,CAAC,SAAS,GAAG;QACjB,QAAQ;QACR,IAAI,CAAC,OAAO,GAAG,EAAE;QACjB,IAAI,CAAC,QAAQ,GAAG;QAChB,IAAI,CAAC,WAAW,GAAG,IAAI,CAAC,kBAAkB;QAC1C,IAAI,SAAS,SAAS,SAAS;QAC/B,IAAI,gBAAgB,IAAI,CAAC,qBAAqB,GAAG,sBAAsB,CAAC,OAAO,YAAY,CAAC;QAC5F,2BAA2B;QAC3B,IAAI,CAAC,eAAe,GAAG,kBAAkB;QACzC,oBAAoB;QACpB,IAAI,CAAC,UAAU,GAAG,EAAE;QACpB,IAAI,yBAAyB,CAAA,GAAA,gJAAA,CAAA,2BAAwB,AAAD,EAAE;QACtD,IAAI,CAAC,WAAW,GAAG,CAAA,GAAA,8IAAA,CAAA,MAAG,AAAD,EAAE,iBAAiB,SAAU,GAAG;YACnD,wCAA2C;gBACzC,IAAI,wBAAwB;oBAC1B,CAAA,GAAA,8IAAA,CAAA,SAAM,AAAD,EAAE,IAAI,QAAQ,IAAI;gBACzB;YACF;YACA,OAAO;gBACL,uEAAuE;gBACvE,MAAM,IAAI,IAAI;gBACd,UAAU,IAAI,QAAQ;YACxB;QACF;QACA,IAAI,CAAC,qBAAqB,CAAC,GAAG,SAAS,KAAK;IAC9C;IACA,UAAU,SAAS,CAAC,WAAW,GAAG;QAChC,OAAO,IAAI,CAAC,SAAS;IACvB;IACA;;;;;;GAMC,GACD,UAAU,SAAS,CAAC,SAAS,GAAG;QAC9B,OAAO,IAAI,CAAC,SAAS,CAAC,SAAS;IACjC;IACA;;GAEC,GACD,UAAU,SAAS,CAAC,0BAA0B,GAAG,SAAU,OAAO,EAAE,IAAI;QACtE,IAAI,mBAAmB,IAAI,CAAC,iBAAiB;QAC7C,IAAI,aAAa,IAAI,CAAC,WAAW;QACjC,IAAI,aAAa,iBAAiB,GAAG,CAAC;QACtC,IAAI,cAAc,MAAM;YACtB,IAAI,UAAU,CAAC,WAAW,CAAC,IAAI,KAAK,MAAM;gBACxC,OAAO;YACT;QACF,OAAO;YACL,aAAa,WAAW,MAAM;QAChC;QACA,UAAU,CAAC,WAAW,GAAG;YACvB,MAAM;QACR;QACA,iBAAiB,GAAG,CAAC,SAAS;QAC9B,IAAI,CAAC,OAAO,CAAC,WAAW,GAAG,IAAI,SAAS,CAAC,QAAQ,QAAQ,CAAC,IAAI,CAAC,SAAS;QACxE,IAAI,CAAC,UAAU,CAAC,WAAW,GAAG;QAC9B,OAAO;IACT;IACA,UAAU,SAAS,CAAC,kBAAkB,GAAG,SAAU,MAAM,EAAE,WAAW;QACpE,IAAI,QAAQ,IAAI,CAAC,OAAO,CAAC,OAAO;QAChC,IAAI,MAAM,IAAI,CAAC,WAAW,CAAC,OAAO;QAClC,IAAI,aAAa,IAAI,CAAC,UAAU;QAChC,IAAI,SAAS,IAAI,aAAa,IAAI;QAClC,IAAI,MAAM,MAAM,MAAM;QACtB,IAAI,WAAW,GAAG;YAChB,2DAA2D;YAC3D,iFAAiF;YACjF,UAAU,CAAC,OAAO,GAAG;QACvB;QACA,IAAI,eAAe,UAAU,CAAC,OAAO;QACrC,uEAAuE;QACvE,IAAK,IAAI,IAAI,QAAQ,IAAI,KAAK,IAAK;YACjC,IAAI,MAAM,KAAK,CAAC,EAAE,GAAG,YAAY,eAAe,CAAC,KAAK,CAAC,EAAE;YACzD,IAAI,CAAC,MAAM,MAAM;gBACf,YAAY,CAAC,EAAE,GAAG,KAAK,GAAG,CAAC,KAAK,YAAY,CAAC,EAAE;gBAC/C,YAAY,CAAC,EAAE,GAAG,KAAK,GAAG,CAAC,KAAK,YAAY,CAAC,EAAE;YACjD;QACF;QACA,IAAI,WAAW,GAAG;QAClB,IAAI,aAAa,GAAG;QACpB,IAAI,IAAI,GAAG,WAAW,sBAAsB;IAC9C;IACA,UAAU,SAAS,CAAC,cAAc,GAAG,SAAU,MAAM;QACnD,IAAI,UAAU,IAAI,CAAC,WAAW,CAAC,OAAO;QACtC,IAAI,cAAc,QAAQ,WAAW;QACrC,OAAO;IACT;IACA,UAAU,SAAS,CAAC,oBAAoB,GAAG,SAAU,QAAQ;QAC3D,IAAI,OAAO,IAAI,CAAC,WAAW,CAAC,SAAS;QACrC,OAAO,QAAQ,KAAK,QAAQ;IAC9B;IACA;;GAEC,GACD,UAAU,SAAS,CAAC,UAAU,GAAG,SAAU,IAAI;QAC7C,wCAA2C;YACzC,CAAA,GAAA,8IAAA,CAAA,SAAM,AAAD,EAAE,CAAC,IAAI,CAAC,QAAQ,EAAE;QACzB;QACA,IAAI,WAAW,IAAI,CAAC,SAAS;QAC7B,IAAI,QAAQ,IAAI,CAAC,KAAK;QACtB,SAAS,UAAU,CAAC;QACpB,IAAI,MAAM,SAAS,KAAK;QACxB,IAAI,CAAC,SAAS,UAAU,EAAE;YACxB,OAAO;QACT;QACA,IAAI,QAAQ,KAAK;YACf,IAAI,CAAC,qBAAqB,CAAC,OAAO,KAAK;QACzC;QACA,OAAO;YAAC;YAAO;SAAI;IACrB;IACA,UAAU,SAAS,CAAC,YAAY,GAAG,SAAU,MAAM,EAAE,UAAU;QAC7D,IAAI,SAAS,IAAI,CAAC,OAAO;QACzB,IAAI,aAAa,IAAI,CAAC,WAAW;QACjC,IAAI,SAAS,WAAW,MAAM;QAC9B,IAAI,YAAY,IAAI,CAAC,UAAU;QAC/B,IAAI,QAAQ,IAAI,CAAC,KAAK;QACtB,IAAI,MAAM,QAAQ,KAAK,GAAG,CAAC,OAAO,MAAM,EAAE,cAAc;QACxD,IAAK,IAAI,IAAI,GAAG,IAAI,QAAQ,IAAK;YAC/B,IAAI,MAAM,UAAU,CAAC,EAAE;YACvB,aAAa,QAAQ,GAAG,IAAI,IAAI,EAAE,KAAK;QACzC;QACA,IAAI,gBAAgB,EAAE;QACtB,IAAK,IAAI,MAAM,OAAO,MAAM,KAAK,MAAO;YACtC,IAAI,YAAY,MAAM;YACtB,+BAA+B;YAC/B,IAAK,IAAI,SAAS,GAAG,SAAS,QAAQ,SAAU;gBAC9C,IAAI,MAAM,UAAU,CAAC,OAAO;gBAC5B,IAAI,MAAM,uBAAuB,SAAS,CAAC,IAAI,CAAC,IAAI,EAAE,MAAM,CAAC,UAAU,IAAI,eAAe,IAAI,QAAQ,EAAE,WAAW;gBACnH,MAAM,CAAC,OAAO,CAAC,IAAI,GAAG;gBACtB,IAAI,eAAe,SAAS,CAAC,OAAO;gBACpC,MAAM,YAAY,CAAC,EAAE,IAAI,CAAC,YAAY,CAAC,EAAE,GAAG,GAAG;gBAC/C,MAAM,YAAY,CAAC,EAAE,IAAI,CAAC,YAAY,CAAC,EAAE,GAAG,GAAG;YACjD;QACF;QACA,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC,MAAM,GAAG;QAC/B,OAAO;YACL,OAAO;YACP,KAAK;QACP;IACF;IACA,UAAU,SAAS,CAAC,qBAAqB,GAAG,SAAU,KAAK,EAAE,GAAG,EAAE,MAAM;QACtE,IAAI,WAAW,IAAI,CAAC,SAAS;QAC7B,IAAI,SAAS,IAAI,CAAC,OAAO;QACzB,IAAI,aAAa,IAAI,CAAC,WAAW;QACjC,IAAI,SAAS,WAAW,MAAM;QAC9B,IAAI,YAAY,IAAI,CAAC,UAAU;QAC/B,IAAI,WAAW,CAAA,GAAA,8IAAA,CAAA,MAAG,AAAD,EAAE,YAAY,SAAU,GAAG;YAC1C,OAAO,IAAI,QAAQ;QACrB;QACA,IAAK,IAAI,IAAI,GAAG,IAAI,QAAQ,IAAK;YAC/B,IAAI,MAAM,UAAU,CAAC,EAAE;YACvB,IAAI,CAAC,SAAS,CAAC,EAAE,EAAE;gBACjB,SAAS,CAAC,EAAE,GAAG;YACjB;YACA,aAAa,QAAQ,GAAG,IAAI,IAAI,EAAE,KAAK;QACzC;QACA,IAAI,SAAS,WAAW,EAAE;YACxB,SAAS,WAAW,CAAC,OAAO,KAAK,QAAQ;QAC3C,OAAO;YACL,IAAI,WAAW,EAAE;YACjB,IAAK,IAAI,MAAM,OAAO,MAAM,KAAK,MAAO;gBACtC,gDAAgD;gBAChD,WAAW,SAAS,OAAO,CAAC,KAAK;gBACjC,0BAA0B;gBAC1B,SAAS;gBACT,IAAI;gBACJ,iDAAiD;gBACjD,iEAAiE;gBACjE,8DAA8D;gBAC9D,+BAA+B;gBAC/B,IAAK,IAAI,SAAS,GAAG,SAAS,QAAQ,SAAU;oBAC9C,IAAI,aAAa,MAAM,CAAC,OAAO;oBAC/B,gCAAgC;oBAChC,IAAI,MAAM,IAAI,CAAC,eAAe,CAAC,UAAU,QAAQ,CAAC,OAAO,EAAE,KAAK;oBAChE,UAAU,CAAC,IAAI,GAAG;oBAClB,IAAI,eAAe,SAAS,CAAC,OAAO;oBACpC,MAAM,YAAY,CAAC,EAAE,IAAI,CAAC,YAAY,CAAC,EAAE,GAAG,GAAG;oBAC/C,MAAM,YAAY,CAAC,EAAE,IAAI,CAAC,YAAY,CAAC,EAAE,GAAG,GAAG;gBACjD;YACF;QACF;QACA,IAAI,CAAC,SAAS,UAAU,IAAI,SAAS,KAAK,EAAE;YAC1C,mDAAmD;YACnD,SAAS,KAAK;QAChB;QACA,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC,MAAM,GAAG;QAC/B,oBAAoB;QACpB,IAAI,CAAC,OAAO,GAAG,EAAE;IACnB;IACA,UAAU,SAAS,CAAC,KAAK,GAAG;QAC1B,OAAO,IAAI,CAAC,MAAM;IACpB;IACA;;GAEC,GACD,UAAU,SAAS,CAAC,GAAG,GAAG,SAAU,GAAG,EAAE,GAAG;QAC1C,IAAI,CAAC,CAAC,OAAO,KAAK,MAAM,IAAI,CAAC,MAAM,GAAG;YACpC,OAAO;QACT;QACA,IAAI,WAAW,IAAI,CAAC,OAAO,CAAC,IAAI;QAChC,OAAO,WAAW,QAAQ,CAAC,IAAI,CAAC,WAAW,CAAC,KAAK,GAAG;IACtD;IACA,UAAU,SAAS,CAAC,SAAS,GAAG,SAAU,UAAU,EAAE,GAAG;QACvD,IAAI,SAAS,EAAE;QACf,IAAI,SAAS,EAAE;QACf,IAAI,OAAO,MAAM;YACf,MAAM;YACN,2BAA2B;YAC3B,aAAa,EAAE;YACf,iBAAiB;YACjB,IAAK,IAAI,IAAI,GAAG,IAAI,IAAI,CAAC,WAAW,CAAC,MAAM,EAAE,IAAK;gBAChD,OAAO,IAAI,CAAC;YACd;QACF,OAAO;YACL,SAAS;QACX;QACA,IAAK,IAAI,IAAI,GAAG,MAAM,OAAO,MAAM,EAAE,IAAI,KAAK,IAAK;YACjD,OAAO,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC,MAAM,CAAC,EAAE,EAAE;QAClC;QACA,OAAO;IACT;IACA;;GAEC,GACD,UAAU,SAAS,CAAC,aAAa,GAAG,SAAU,GAAG,EAAE,MAAM;QACvD,IAAI,CAAC,CAAC,UAAU,KAAK,SAAS,IAAI,CAAC,SAAS,GAAG;YAC7C,OAAO;QACT;QACA,IAAI,WAAW,IAAI,CAAC,OAAO,CAAC,IAAI;QAChC,OAAO,WAAW,QAAQ,CAAC,OAAO,GAAG;IACvC;IACA;;GAEC,GACD,UAAU,SAAS,CAAC,MAAM,GAAG,SAAU,GAAG;QACxC,IAAI,UAAU,IAAI,CAAC,OAAO,CAAC,IAAI;QAC/B,IAAI,MAAM;QACV,IAAI,SAAS;YACX,IAAK,IAAI,IAAI,GAAG,MAAM,IAAI,CAAC,KAAK,IAAI,IAAI,KAAK,IAAK;gBAChD,IAAI,QAAQ,IAAI,CAAC,GAAG,CAAC,KAAK;gBAC1B,IAAI,CAAC,MAAM,QAAQ;oBACjB,OAAO;gBACT;YACF;QACF;QACA,OAAO;IACT;IACA;;GAEC,GACD,UAAU,SAAS,CAAC,SAAS,GAAG,SAAU,GAAG;QAC3C,IAAI,eAAe,EAAE;QACrB,gCAAgC;QAChC,IAAI,CAAC,IAAI,CAAC;YAAC;SAAI,EAAE,SAAU,GAAG;YAC5B,IAAI,CAAC,MAAM,MAAM;gBACf,aAAa,IAAI,CAAC;YACpB;QACF;QACA,OAAO;QACP,oBAAoB;QACpB,IAAI,qBAAqB,aAAa,IAAI,CAAC,SAAU,CAAC,EAAE,CAAC;YACvD,OAAO,IAAI;QACb;QACA,IAAI,MAAM,IAAI,CAAC,KAAK;QACpB,mBAAmB;QACnB,OAAO,QAAQ,IAAI,IAAI,MAAM,MAAM,IAAI,kBAAkB,CAAC,CAAC,MAAM,CAAC,IAAI,EAAE,GAAG,CAAC,kBAAkB,CAAC,MAAM,EAAE,GAAG,kBAAkB,CAAC,MAAM,IAAI,EAAE,IAAI;IAC/I;IACA;;GAEC,GACD,UAAU,SAAS,CAAC,eAAe,GAAG,SAAU,QAAQ;QACtD,IAAI,YAAY,IAAI,CAAC,SAAS,IAAI,WAAW,GAAG;YAC9C,OAAO,CAAC;QACV;QACA,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE;YAClB,OAAO;QACT;QACA,wBAAwB;QACxB,IAAI,UAAU,IAAI,CAAC,QAAQ;QAC3B,4BAA4B;QAC5B,IAAI,eAAe,OAAO,CAAC,SAAS;QACpC,IAAI,gBAAgB,QAAQ,eAAe,IAAI,CAAC,MAAM,IAAI,iBAAiB,UAAU;YACnF,OAAO;QACT;QACA,IAAI,OAAO;QACX,IAAI,QAAQ,IAAI,CAAC,MAAM,GAAG;QAC1B,MAAO,QAAQ,MAAO;YACpB,IAAI,MAAM,CAAC,OAAO,KAAK,IAAI,IAAI;YAC/B,IAAI,OAAO,CAAC,IAAI,GAAG,UAAU;gBAC3B,OAAO,MAAM;YACf,OAAO,IAAI,OAAO,CAAC,IAAI,GAAG,UAAU;gBAClC,QAAQ,MAAM;YAChB,OAAO;gBACL,OAAO;YACT;QACF;QACA,OAAO,CAAC;IACV;IACA;;;;;;;GAOC,GACD,UAAU,SAAS,CAAC,gBAAgB,GAAG,SAAU,GAAG,EAAE,KAAK,EAAE,WAAW;QACtE,IAAI,SAAS,IAAI,CAAC,OAAO;QACzB,IAAI,UAAU,MAAM,CAAC,IAAI;QACzB,IAAI,iBAAiB,EAAE;QACvB,IAAI,CAAC,SAAS;YACZ,OAAO;QACT;QACA,IAAI,eAAe,MAAM;YACvB,cAAc;QAChB;QACA,IAAI,UAAU;QACd,IAAI,UAAU,CAAC;QACf,IAAI,oBAAoB;QACxB,4DAA4D;QAC5D,IAAK,IAAI,IAAI,GAAG,MAAM,IAAI,CAAC,KAAK,IAAI,IAAI,KAAK,IAAK;YAChD,IAAI,YAAY,IAAI,CAAC,WAAW,CAAC;YACjC,IAAI,OAAO,QAAQ,OAAO,CAAC,UAAU;YACrC,IAAI,OAAO,KAAK,GAAG,CAAC;YACpB,IAAI,QAAQ,aAAa;gBACvB,oFAAoF;gBACpF,iFAAiF;gBACjF,gFAAgF;gBAChF,sDAAsD;gBACtD,oFAAoF;gBACpF,sCAAsC;gBACtC,IAAI,OAAO,WAAW,SAAS,WAAW,QAAQ,KAAK,UAAU,GAAG;oBAClE,UAAU;oBACV,UAAU;oBACV,oBAAoB;gBACtB;gBACA,IAAI,SAAS,SAAS;oBACpB,cAAc,CAAC,oBAAoB,GAAG;gBACxC;YACF;QACF;QACA,eAAe,MAAM,GAAG;QACxB,OAAO;IACT;IACA,UAAU,SAAS,CAAC,UAAU,GAAG;QAC/B,IAAI;QACJ,IAAI,UAAU,IAAI,CAAC,QAAQ;QAC3B,IAAI,SAAS;YACX,IAAI,OAAO,QAAQ,WAAW;YAC9B,IAAI,YAAY,IAAI,CAAC,MAAM;YAC3B,qEAAqE;YACrE,IAAI,SAAS,OAAO;gBAClB,aAAa,IAAI,KAAK;gBACtB,IAAK,IAAI,IAAI,GAAG,IAAI,WAAW,IAAK;oBAClC,UAAU,CAAC,EAAE,GAAG,OAAO,CAAC,EAAE;gBAC5B;YACF,OAAO;gBACL,aAAa,IAAI,KAAK,QAAQ,MAAM,EAAE,GAAG;YAC3C;QACF,OAAO;YACL,IAAI,OAAO,eAAe,IAAI,CAAC,SAAS;YACxC,aAAa,IAAI,KAAK,IAAI,CAAC,KAAK;YAChC,IAAK,IAAI,IAAI,GAAG,IAAI,WAAW,MAAM,EAAE,IAAK;gBAC1C,UAAU,CAAC,EAAE,GAAG;YAClB;QACF;QACA,OAAO;IACT;IACA;;GAEC,GACD,UAAU,SAAS,CAAC,MAAM,GAAG,SAAU,IAAI,EAAE,EAAE;QAC7C,IAAI,CAAC,IAAI,CAAC,MAAM,EAAE;YAChB,OAAO,IAAI;QACb;QACA,IAAI,WAAW,IAAI,CAAC,KAAK;QACzB,IAAI,QAAQ,SAAS,KAAK;QAC1B,IAAI,OAAO,eAAe,SAAS,SAAS;QAC5C,IAAI,aAAa,IAAI,KAAK;QAC1B,IAAI,QAAQ,EAAE;QACd,IAAI,UAAU,KAAK,MAAM;QACzB,IAAI,SAAS;QACb,IAAI,OAAO,IAAI,CAAC,EAAE;QAClB,IAAI,SAAS,SAAS,OAAO;QAC7B,IAAK,IAAI,IAAI,GAAG,IAAI,OAAO,IAAK;YAC9B,IAAI,OAAO,KAAK;YAChB,IAAI,SAAS,SAAS,WAAW,CAAC;YAClC,sBAAsB;YACtB,IAAI,YAAY,GAAG;gBACjB,OAAO,GAAG;YACZ,OAAO,IAAI,YAAY,GAAG;gBACxB,IAAI,MAAM,MAAM,CAAC,KAAK,CAAC,OAAO;gBAC9B,OAAO,GAAG,KAAK;YACjB,OAAO;gBACL,IAAI,IAAI;gBACR,MAAO,IAAI,SAAS,IAAK;oBACvB,KAAK,CAAC,EAAE,GAAG,MAAM,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC,OAAO;gBACpC;gBACA,KAAK,CAAC,EAAE,GAAG;gBACX,OAAO,GAAG,KAAK,CAAC,MAAM;YACxB;YACA,IAAI,MAAM;gBACR,UAAU,CAAC,SAAS,GAAG;YACzB;QACF;QACA,8BAA8B;QAC9B,IAAI,SAAS,OAAO;YAClB,SAAS,QAAQ,GAAG;QACtB;QACA,SAAS,MAAM,GAAG;QAClB,oBAAoB;QACpB,SAAS,OAAO,GAAG,EAAE;QACrB,SAAS,gBAAgB;QACzB,OAAO;IACT;IACA;;;GAGC,GACD,UAAU,SAAS,CAAC,WAAW,GAAG,SAAU,KAAK;QAC/C,IAAI,WAAW,IAAI,CAAC,KAAK;QACzB,IAAI,MAAM,SAAS,MAAM;QACzB,IAAI,CAAC,KAAK;YACR,OAAO,IAAI;QACb;QACA,IAAI,OAAO,CAAA,GAAA,8IAAA,CAAA,OAAI,AAAD,EAAE;QAChB,IAAI,UAAU,KAAK,MAAM;QACzB,IAAI,CAAC,SAAS;YACZ,OAAO,IAAI;QACb;QACA,IAAI,gBAAgB,SAAS,KAAK;QAClC,IAAI,OAAO,eAAe,SAAS,SAAS;QAC5C,IAAI,aAAa,IAAI,KAAK;QAC1B,IAAI,SAAS;QACb,IAAI,OAAO,IAAI,CAAC,EAAE;QAClB,IAAI,MAAM,KAAK,CAAC,KAAK,CAAC,EAAE;QACxB,IAAI,MAAM,KAAK,CAAC,KAAK,CAAC,EAAE;QACxB,IAAI,WAAW,SAAS,OAAO;QAC/B,IAAI,gBAAgB;QACpB,IAAI,CAAC,SAAS,QAAQ,EAAE;YACtB,mEAAmE;YACnE,IAAI,MAAM;YACV,IAAI,YAAY,GAAG;gBACjB,IAAI,aAAa,QAAQ,CAAC,IAAI,CAAC,EAAE,CAAC;gBAClC,IAAK,IAAI,IAAI,GAAG,IAAI,KAAK,IAAK;oBAC5B,IAAI,MAAM,UAAU,CAAC,EAAE;oBACvB,oEAAoE;oBACpE,mEAAmE;oBACnE,mEAAmE;oBACnE,oEAAoE;oBACpE,uEAAuE;oBACvE,IAAI,OAAO,OAAO,OAAO,OAAO,MAAM,MAAM;wBAC1C,UAAU,CAAC,SAAS,GAAG;oBACzB;oBACA;gBACF;gBACA,gBAAgB;YAClB,OAAO,IAAI,YAAY,GAAG;gBACxB,IAAI,aAAa,QAAQ,CAAC,IAAI,CAAC,EAAE,CAAC;gBAClC,IAAI,cAAc,QAAQ,CAAC,IAAI,CAAC,EAAE,CAAC;gBACnC,IAAI,OAAO,KAAK,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC,EAAE;gBAC5B,IAAI,OAAO,KAAK,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC,EAAE;gBAC5B,IAAK,IAAI,IAAI,GAAG,IAAI,KAAK,IAAK;oBAC5B,IAAI,MAAM,UAAU,CAAC,EAAE;oBACvB,IAAI,OAAO,WAAW,CAAC,EAAE;oBACzB,wCAAwC;oBACxC,IAAI,CAAC,OAAO,OAAO,OAAO,OAAO,MAAM,IAAI,KAAK,CAAC,QAAQ,QAAQ,QAAQ,QAAQ,MAAM,KAAK,GAAG;wBAC7F,UAAU,CAAC,SAAS,GAAG;oBACzB;oBACA;gBACF;gBACA,gBAAgB;YAClB;QACF;QACA,IAAI,CAAC,eAAe;YAClB,IAAI,YAAY,GAAG;gBACjB,IAAK,IAAI,IAAI,GAAG,IAAI,eAAe,IAAK;oBACtC,IAAI,WAAW,SAAS,WAAW,CAAC;oBACpC,IAAI,MAAM,QAAQ,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC,SAAS;oBACrC,wCAAwC;oBACxC,IAAI,OAAO,OAAO,OAAO,OAAO,MAAM,MAAM;wBAC1C,UAAU,CAAC,SAAS,GAAG;oBACzB;gBACF;YACF,OAAO;gBACL,IAAK,IAAI,IAAI,GAAG,IAAI,eAAe,IAAK;oBACtC,IAAI,OAAO;oBACX,IAAI,WAAW,SAAS,WAAW,CAAC;oBACpC,IAAK,IAAI,IAAI,GAAG,IAAI,SAAS,IAAK;wBAChC,IAAI,OAAO,IAAI,CAAC,EAAE;wBAClB,IAAI,MAAM,QAAQ,CAAC,KAAK,CAAC,SAAS;wBAClC,wCAAwC;wBACxC,IAAI,MAAM,KAAK,CAAC,KAAK,CAAC,EAAE,IAAI,MAAM,KAAK,CAAC,KAAK,CAAC,EAAE,EAAE;4BAChD,OAAO;wBACT;oBACF;oBACA,IAAI,MAAM;wBACR,UAAU,CAAC,SAAS,GAAG,SAAS,WAAW,CAAC;oBAC9C;gBACF;YACF;QACF;QACA,8BAA8B;QAC9B,IAAI,SAAS,eAAe;YAC1B,SAAS,QAAQ,GAAG;QACtB;QACA,SAAS,MAAM,GAAG;QAClB,oBAAoB;QACpB,SAAS,OAAO,GAAG,EAAE;QACrB,SAAS,gBAAgB;QACzB,OAAO;IACT;IACA,MAAM;IACN,mCAAmC;IACnC,MAAM;IACN,4DAA4D;IAC5D,gCAAgC;IAChC,oCAAoC;IACpC,wEAAwE;IACxE,UAAU;IACV,qBAAqB;IACrB,IAAI;IACJ;;GAEC,GACD,UAAU,SAAS,CAAC,GAAG,GAAG,SAAU,IAAI,EAAE,EAAE;QAC1C,iCAAiC;QACjC,IAAI,SAAS,IAAI,CAAC,KAAK,CAAC;QACxB,IAAI,CAAC,WAAW,CAAC,QAAQ,MAAM;QAC/B,OAAO;IACT;IACA;;GAEC,GACD,UAAU,SAAS,CAAC,MAAM,GAAG,SAAU,IAAI,EAAE,EAAE;QAC7C,IAAI,CAAC,WAAW,CAAC,IAAI,EAAE,MAAM;IAC/B;IACA,UAAU,SAAS,CAAC,WAAW,GAAG,SAAU,MAAM,EAAE,IAAI,EAAE,EAAE;QAC1D,IAAI,eAAe,OAAO,OAAO;QACjC,IAAI,cAAc,EAAE;QACpB,IAAI,UAAU,KAAK,MAAM;QACzB,IAAI,YAAY,OAAO,KAAK;QAC5B,IAAI,SAAS,EAAE;QACf,IAAI,YAAY,OAAO,UAAU;QACjC,IAAK,IAAI,IAAI,GAAG,IAAI,KAAK,MAAM,EAAE,IAAK;YACpC,SAAS,CAAC,IAAI,CAAC,EAAE,CAAC,GAAG;QACvB;QACA,IAAK,IAAI,YAAY,GAAG,YAAY,WAAW,YAAa;YAC1D,IAAI,WAAW,OAAO,WAAW,CAAC;YAClC,IAAK,IAAI,IAAI,GAAG,IAAI,SAAS,IAAK;gBAChC,MAAM,CAAC,EAAE,GAAG,YAAY,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC,SAAS;YAC7C;YACA,MAAM,CAAC,QAAQ,GAAG;YAClB,IAAI,WAAW,MAAM,GAAG,KAAK,CAAC,MAAM;YACpC,IAAI,YAAY,MAAM;gBACpB,8CAA8C;gBAC9C,IAAI,OAAO,aAAa,UAAU;oBAChC,WAAW,CAAC,EAAE,GAAG;oBACjB,WAAW;gBACb;gBACA,IAAK,IAAI,IAAI,GAAG,IAAI,SAAS,MAAM,EAAE,IAAK;oBACxC,IAAI,MAAM,IAAI,CAAC,EAAE;oBACjB,IAAI,MAAM,QAAQ,CAAC,EAAE;oBACrB,IAAI,iBAAiB,SAAS,CAAC,IAAI;oBACnC,IAAI,WAAW,YAAY,CAAC,IAAI;oBAChC,IAAI,UAAU;wBACZ,QAAQ,CAAC,SAAS,GAAG;oBACvB;oBACA,IAAI,MAAM,cAAc,CAAC,EAAE,EAAE;wBAC3B,cAAc,CAAC,EAAE,GAAG;oBACtB;oBACA,IAAI,MAAM,cAAc,CAAC,EAAE,EAAE;wBAC3B,cAAc,CAAC,EAAE,GAAG;oBACtB;gBACF;YACF;QACF;IACF;IACA;;;;GAIC,GACD,UAAU,SAAS,CAAC,cAAc,GAAG,SAAU,cAAc,EAAE,IAAI;QACjE,IAAI,SAAS,IAAI,CAAC,KAAK,CAAC;YAAC;SAAe,EAAE;QAC1C,IAAI,gBAAgB,OAAO,OAAO;QAClC,IAAI,WAAW,aAAa,CAAC,eAAe;QAC5C,IAAI,MAAM,IAAI,CAAC,KAAK;QACpB,IAAI,eAAe;QACnB,IAAI,YAAY,KAAK,KAAK,CAAC,IAAI;QAC/B,IAAI,kBAAkB,IAAI,CAAC,WAAW,CAAC;QACvC,IAAI;QACJ,IAAI;QACJ,IAAI;QACJ,IAAI,aAAa,IAAI,CAAC,eAAe,IAAI,CAAC,SAAS,CAAC,EAAE,KAAK,GAAG,CAAC,CAAC,KAAK,IAAI,CAAC,MAAM,aAAa,CAAC,IAAI,GAAG;QACrG,kCAAkC;QAClC,UAAU,CAAC,eAAe,GAAG;QAC7B,IAAK,IAAI,IAAI,GAAG,IAAI,MAAM,GAAG,KAAK,UAAW;YAC3C,IAAI,iBAAiB,KAAK,GAAG,CAAC,IAAI,WAAW,MAAM;YACnD,IAAI,eAAe,KAAK,GAAG,CAAC,IAAI,YAAY,GAAG;YAC/C,IAAI,OAAO,CAAC,eAAe,cAAc,IAAI;YAC7C,IAAI,OAAO;YACX,IAAK,IAAI,MAAM,gBAAgB,MAAM,cAAc,MAAO;gBACxD,IAAI,WAAW,IAAI,CAAC,WAAW,CAAC;gBAChC,IAAI,IAAI,QAAQ,CAAC,SAAS;gBAC1B,IAAI,MAAM,IAAI;oBACZ;gBACF;gBACA,QAAQ;YACV;YACA,QAAQ,eAAe;YACvB,IAAI,aAAa;YACjB,IAAI,WAAW,KAAK,GAAG,CAAC,IAAI,WAAW;YACvC,IAAI,UAAU,IAAI;YAClB,IAAI,UAAU,QAAQ,CAAC,gBAAgB;YACvC,UAAU,CAAC;YACX,eAAe;YACf,IAAI,gBAAgB,CAAC;YACrB,IAAI,WAAW;YACf,2GAA2G;YAC3G,iCAAiC;YACjC,IAAK,IAAI,MAAM,YAAY,MAAM,UAAU,MAAO;gBAChD,IAAI,WAAW,IAAI,CAAC,WAAW,CAAC;gBAChC,IAAI,IAAI,QAAQ,CAAC,SAAS;gBAC1B,IAAI,MAAM,IAAI;oBACZ;oBACA,IAAI,gBAAgB,GAAG;wBACrB,gBAAgB;oBAClB;oBACA;gBACF;gBACA,6CAA6C;gBAC7C,OAAO,KAAK,GAAG,CAAC,CAAC,UAAU,IAAI,IAAI,CAAC,IAAI,OAAO,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC,OAAO,OAAO;gBACpF,IAAI,OAAO,SAAS;oBAClB,UAAU;oBACV,eAAe,UAAU,mBAAmB;gBAC9C;YACF;YACA,IAAI,WAAW,KAAK,WAAW,WAAW,YAAY;gBACpD,0CAA0C;gBAC1C,0DAA0D;gBAC1D,UAAU,CAAC,eAAe,GAAG,KAAK,GAAG,CAAC,eAAe;gBACrD,eAAe,KAAK,GAAG,CAAC,eAAe;YACzC;YACA,UAAU,CAAC,eAAe,GAAG;YAC7B,kBAAkB,cAAc,kCAAkC;QACpE;QACA,iCAAiC;QACjC,UAAU,CAAC,eAAe,GAAG,IAAI,CAAC,WAAW,CAAC,MAAM;QACpD,OAAO,MAAM,GAAG;QAChB,OAAO,QAAQ,GAAG;QAClB,OAAO,WAAW,GAAG,IAAI,CAAC,UAAU;QACpC,OAAO;IACT;IACA;;;;GAIC,GACD,UAAU,SAAS,CAAC,gBAAgB,GAAG,SAAU,cAAc,EAAE,IAAI;QACnE,IAAI,SAAS,IAAI,CAAC,KAAK,CAAC;YAAC;SAAe,EAAE;QAC1C,IAAI,gBAAgB,OAAO,OAAO;QAClC,IAAI,YAAY,KAAK,KAAK,CAAC,IAAI;QAC/B,IAAI,WAAW,aAAa,CAAC,eAAe;QAC5C,IAAI,MAAM,IAAI,CAAC,KAAK;QACpB,mEAAmE;QACnE,IAAI,aAAa,IAAI,CAAC,eAAe,IAAI,CAAC,SAAS,CAAC,EAAE,KAAK,IAAI,CAAC,MAAM,aAAa;QACnF,IAAI,SAAS;QACb,IAAK,IAAI,IAAI,GAAG,IAAI,KAAK,KAAK,UAAW;YACvC,IAAI,WAAW;YACf,IAAI,WAAW,QAAQ,CAAC,IAAI,CAAC,WAAW,CAAC,UAAU;YACnD,IAAI,WAAW;YACf,IAAI,WAAW,QAAQ,CAAC,IAAI,CAAC,WAAW,CAAC,UAAU;YACnD,IAAI,gBAAgB;YACpB,6BAA6B;YAC7B,IAAI,IAAI,YAAY,KAAK;gBACvB,gBAAgB,MAAM;YACxB;YACA,iDAAiD;YACjD,IAAK,IAAI,IAAI,GAAG,IAAI,eAAe,IAAK;gBACtC,IAAI,WAAW,IAAI,CAAC,WAAW,CAAC,IAAI;gBACpC,IAAI,QAAQ,QAAQ,CAAC,SAAS;gBAC9B,IAAI,QAAQ,UAAU;oBACpB,WAAW;oBACX,WAAW,IAAI;gBACjB;gBACA,IAAI,QAAQ,UAAU;oBACpB,WAAW;oBACX,WAAW,IAAI;gBACjB;YACF;YACA,IAAI,cAAc,IAAI,CAAC,WAAW,CAAC;YACnC,IAAI,cAAc,IAAI,CAAC,WAAW,CAAC;YACnC,gFAAgF;YAChF,IAAI,WAAW,UAAU;gBACvB,UAAU,CAAC,SAAS,GAAG;gBACvB,UAAU,CAAC,SAAS,GAAG;YACzB,OAAO;gBACL,UAAU,CAAC,SAAS,GAAG;gBACvB,UAAU,CAAC,SAAS,GAAG;YACzB;QACF;QACA,OAAO,MAAM,GAAG;QAChB,OAAO,QAAQ,GAAG;QAClB,OAAO,gBAAgB;QACvB,OAAO;IACT;IACA;;;GAGC,GACD,UAAU,SAAS,CAAC,UAAU,GAAG,SAAU,SAAS,EAAE,IAAI,EAAE,WAAW,EAAE,WAAW;QAClF,IAAI,SAAS,IAAI,CAAC,KAAK,CAAC;YAAC;SAAU,EAAE;QACrC,IAAI,gBAAgB,OAAO,OAAO;QAClC,IAAI,cAAc,EAAE;QACpB,IAAI,YAAY,KAAK,KAAK,CAAC,IAAI;QAC/B,IAAI,WAAW,aAAa,CAAC,UAAU;QACvC,IAAI,MAAM,IAAI,CAAC,KAAK;QACpB,IAAI,iBAAiB,OAAO,UAAU,CAAC,UAAU,GAAG;QACpD,IAAI,aAAa,IAAI,CAAC,eAAe,IAAI,CAAC,SAAS,CAAC,EAAE,KAAK,IAAI,CAAC,MAAM;QACtE,IAAI,SAAS;QACb,IAAK,IAAI,IAAI,GAAG,IAAI,KAAK,KAAK,UAAW;YACvC,aAAa;YACb,IAAI,YAAY,MAAM,GAAG;gBACvB,YAAY,MAAM;gBAClB,YAAY,MAAM,GAAG;YACvB;YACA,IAAK,IAAI,IAAI,GAAG,IAAI,WAAW,IAAK;gBAClC,IAAI,UAAU,IAAI,CAAC,WAAW,CAAC,IAAI;gBACnC,WAAW,CAAC,EAAE,GAAG,QAAQ,CAAC,QAAQ;YACpC;YACA,IAAI,QAAQ,YAAY;YACxB,IAAI,iBAAiB,IAAI,CAAC,WAAW,CAAC,KAAK,GAAG,CAAC,IAAI,YAAY,aAAa,UAAU,GAAG,MAAM;YAC/F,wCAAwC;YACxC,QAAQ,CAAC,eAAe,GAAG;YAC3B,IAAI,QAAQ,cAAc,CAAC,EAAE,EAAE;gBAC7B,cAAc,CAAC,EAAE,GAAG;YACtB;YACA,IAAI,QAAQ,cAAc,CAAC,EAAE,EAAE;gBAC7B,cAAc,CAAC,EAAE,GAAG;YACtB;YACA,UAAU,CAAC,SAAS,GAAG;QACzB;QACA,OAAO,MAAM,GAAG;QAChB,OAAO,QAAQ,GAAG;QAClB,OAAO,gBAAgB;QACvB,OAAO;IACT;IACA;;;;;;;GAOC,GACD,UAAU,SAAS,CAAC,IAAI,GAAG,SAAU,IAAI,EAAE,EAAE;QAC3C,IAAI,CAAC,IAAI,CAAC,MAAM,EAAE;YAChB;QACF;QACA,IAAI,UAAU,KAAK,MAAM;QACzB,IAAI,SAAS,IAAI,CAAC,OAAO;QACzB,IAAK,IAAI,IAAI,GAAG,MAAM,IAAI,CAAC,KAAK,IAAI,IAAI,KAAK,IAAK;YAChD,IAAI,SAAS,IAAI,CAAC,WAAW,CAAC;YAC9B,sBAAsB;YACtB,OAAQ;gBACN,KAAK;oBACH,GAAG;oBACH;gBACF,KAAK;oBACH,GAAG,MAAM,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC,OAAO,EAAE;oBAC5B;gBACF,KAAK;oBACH,GAAG,MAAM,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC,OAAO,EAAE,MAAM,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC,OAAO,EAAE;oBACrD;gBACF;oBACE,IAAI,IAAI;oBACR,IAAI,QAAQ,EAAE;oBACd,MAAO,IAAI,SAAS,IAAK;wBACvB,KAAK,CAAC,EAAE,GAAG,MAAM,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC,OAAO;oBACpC;oBACA,QAAQ;oBACR,KAAK,CAAC,EAAE,GAAG;oBACX,GAAG,KAAK,CAAC,MAAM;YACnB;QACF;IACF;IACA;;GAEC,GACD,UAAU,SAAS,CAAC,aAAa,GAAG,SAAU,GAAG;QAC/C,4CAA4C;QAC5C,IAAI,UAAU,IAAI,CAAC,OAAO,CAAC,IAAI;QAC/B,IAAI,gBAAgB;QACpB,IAAI,CAAC,SAAS;YACZ,OAAO;QACT;QACA,sDAAsD;QACtD,IAAI,UAAU,IAAI,CAAC,KAAK;QACxB,gEAAgE;QAChE,+DAA+D;QAC/D,8DAA8D;QAC9D,IAAI,SAAS,CAAC,IAAI,CAAC,QAAQ;QAC3B,IAAI;QACJ,IAAI,QAAQ;YACV,OAAO,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,KAAK;QACnC;QACA,YAAY,IAAI,CAAC,OAAO,CAAC,IAAI;QAC7B,IAAI,WAAW;YACb,OAAO,UAAU,KAAK;QACxB;QACA,YAAY;QACZ,IAAI,MAAM,SAAS,CAAC,EAAE;QACtB,IAAI,MAAM,SAAS,CAAC,EAAE;QACtB,IAAK,IAAI,IAAI,GAAG,IAAI,SAAS,IAAK;YAChC,IAAI,SAAS,IAAI,CAAC,WAAW,CAAC;YAC9B,IAAI,QAAQ,OAAO,CAAC,OAAO;YAC3B,QAAQ,OAAO,CAAC,MAAM,KAAK;YAC3B,QAAQ,OAAO,CAAC,MAAM,KAAK;QAC7B;QACA,YAAY;YAAC;YAAK;SAAI;QACtB,IAAI,CAAC,OAAO,CAAC,IAAI,GAAG;QACpB,OAAO;IACT;IACA;;GAEC,GACD,UAAU,SAAS,CAAC,cAAc,GAAG,SAAU,GAAG;QAChD,IAAI,SAAS,IAAI,CAAC,WAAW,CAAC;QAC9B,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC,UAAU,EAAE;YAC9B,IAAI,MAAM,EAAE;YACZ,IAAI,SAAS,IAAI,CAAC,OAAO;YACzB,IAAK,IAAI,IAAI,GAAG,IAAI,OAAO,MAAM,EAAE,IAAK;gBACtC,IAAI,IAAI,CAAC,MAAM,CAAC,EAAE,CAAC,OAAO;YAC5B;YACA,OAAO;QACT,OAAO;YACL,OAAO,IAAI,CAAC,SAAS,CAAC,OAAO,CAAC;QAChC;IACF;IACA;;;;GAIC,GACD,UAAU,SAAS,CAAC,KAAK,GAAG,SAAU,UAAU,EAAE,aAAa;QAC7D,IAAI,SAAS,IAAI;QACjB,IAAI,SAAS,IAAI,CAAC,OAAO;QACzB,IAAI,gBAAgB,cAAc,CAAA,GAAA,8IAAA,CAAA,SAAM,AAAD,EAAE,YAAY,SAAU,GAAG,EAAE,MAAM;YACxE,GAAG,CAAC,OAAO,GAAG;YACd,OAAO;QACT,GAAG,CAAC;QACJ,IAAI,eAAe;YACjB,IAAK,IAAI,IAAI,GAAG,IAAI,OAAO,MAAM,EAAE,IAAK;gBACtC,kCAAkC;gBAClC,OAAO,OAAO,CAAC,EAAE,GAAG,CAAC,aAAa,CAAC,EAAE,GAAG,MAAM,CAAC,EAAE,GAAG,WAAW,MAAM,CAAC,EAAE;YAC1E;QACF,OAAO;YACL,OAAO,OAAO,GAAG;QACnB;QACA,IAAI,CAAC,gBAAgB,CAAC;QACtB,IAAI,CAAC,eAAe;YAClB,OAAO,QAAQ,GAAG,IAAI,CAAC,aAAa;QACtC;QACA,OAAO,gBAAgB;QACvB,OAAO;IACT;IACA,UAAU,SAAS,CAAC,gBAAgB,GAAG,SAAU,MAAM;QACrD,OAAO,MAAM,GAAG,IAAI,CAAC,MAAM;QAC3B,OAAO,SAAS,GAAG,IAAI,CAAC,SAAS;QACjC,OAAO,SAAS,GAAG,IAAI,CAAC,SAAS;QACjC,OAAO,WAAW,GAAG,IAAI,CAAC,WAAW;QACrC,OAAO,OAAO,GAAG,CAAA,GAAA,8IAAA,CAAA,QAAK,AAAD,EAAE,IAAI,CAAC,OAAO;QACnC,OAAO,UAAU,GAAG,CAAA,GAAA,8IAAA,CAAA,QAAK,AAAD,EAAE,IAAI,CAAC,UAAU;IAC3C;IACA,UAAU,SAAS,CAAC,aAAa,GAAG;QAClC,IAAI,IAAI,CAAC,QAAQ,EAAE;YACjB,IAAI,OAAO,IAAI,CAAC,QAAQ,CAAC,WAAW;YACpC,IAAI,UAAU,KAAK;YACnB,IAAI,SAAS,OAAO;gBAClB,IAAI,YAAY,IAAI,CAAC,QAAQ,CAAC,MAAM;gBACpC,UAAU,IAAI,KAAK;gBACnB,IAAK,IAAI,IAAI,GAAG,IAAI,WAAW,IAAK;oBAClC,OAAO,CAAC,EAAE,GAAG,IAAI,CAAC,QAAQ,CAAC,EAAE;gBAC/B;YACF,OAAO;gBACL,UAAU,IAAI,KAAK,IAAI,CAAC,QAAQ;YAClC;YACA,OAAO;QACT;QACA,OAAO;IACT;IACA,UAAU,SAAS,CAAC,kBAAkB,GAAG,SAAU,GAAG;QACpD,OAAO;IACT;IACA,UAAU,SAAS,CAAC,UAAU,GAAG,SAAU,GAAG;QAC5C,IAAI,MAAM,IAAI,CAAC,MAAM,IAAI,OAAO,GAAG;YACjC,OAAO,IAAI,CAAC,QAAQ,CAAC,IAAI;QAC3B;QACA,OAAO,CAAC;IACV;IACA,UAAU,SAAS,CAAC,gBAAgB,GAAG;QACrC,IAAI,CAAC,WAAW,GAAG,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC,kBAAkB;IAC9E;IACA,UAAU,aAAa,GAAG;QACxB,SAAS,kBAAkB,QAAQ,EAAE,QAAQ,EAAE,SAAS,EAAE,QAAQ;YAChE,OAAO,CAAA,GAAA,mKAAA,CAAA,iBAAc,AAAD,EAAE,QAAQ,CAAC,SAAS,EAAE,IAAI,CAAC,WAAW,CAAC,SAAS;QACtE;QACA,yBAAyB;YACvB,WAAW;YACX,YAAY,SAAU,QAAQ,EAAE,QAAQ,EAAE,SAAS,EAAE,QAAQ;gBAC3D,OAAO,CAAA,GAAA,mKAAA,CAAA,iBAAc,AAAD,EAAE,QAAQ,CAAC,SAAS,EAAE,IAAI,CAAC,WAAW,CAAC,SAAS;YACtE;YACA,cAAc;YACd,UAAU,SAAU,QAAQ,EAAE,QAAQ,EAAE,SAAS,EAAE,QAAQ;gBACzD,gEAAgE;gBAChE,sEAAsE;gBACtE,mEAAmE;gBACnE,yBAAyB;gBACzB,IAAI,QAAQ,YAAY,CAAC,SAAS,KAAK,IAAI,OAAO,WAAW,SAAS,KAAK;gBAC3E,OAAO,CAAA,GAAA,mKAAA,CAAA,iBAAc,AAAD,EAAE,iBAAiB,QAAQ,KAAK,CAAC,SAAS,GAE5D,OAAO,IAAI,CAAC,WAAW,CAAC,SAAS;YACrC;YACA,YAAY,SAAU,QAAQ,EAAE,QAAQ,EAAE,SAAS,EAAE,QAAQ;gBAC3D,OAAO,QAAQ,CAAC,SAAS;YAC3B;QACF;IACF;IACA,OAAO;AACT;uCACe", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2627, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/tindahan/sari-sari-admin/node_modules/echarts/lib/data/helper/sourceManager.js"], "sourcesContent": ["\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\r\n* Licensed to the Apache Software Foundation (ASF) under one\r\n* or more contributor license agreements.  See the NOTICE file\r\n* distributed with this work for additional information\r\n* regarding copyright ownership.  The ASF licenses this file\r\n* to you under the Apache License, Version 2.0 (the\r\n* \"License\"); you may not use this file except in compliance\r\n* with the License.  You may obtain a copy of the License at\r\n*\r\n*   http://www.apache.org/licenses/LICENSE-2.0\r\n*\r\n* Unless required by applicable law or agreed to in writing,\r\n* software distributed under the License is distributed on an\r\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\r\n* KIND, either express or implied.  See the License for the\r\n* specific language governing permissions and limitations\r\n* under the License.\r\n*/\nimport { setAsPrimitive, map, isTypedArray, assert, each, retrieve2 } from 'zrender/lib/core/util.js';\nimport { createSource, cloneSourceShallow } from '../Source.js';\nimport { SOURCE_FORMAT_TYPED_ARRAY, SOURCE_FORMAT_ORIGINAL } from '../../util/types.js';\nimport { querySeriesUpstreamDatasetModel, queryDatasetUpstreamDatasetModels } from './sourceHelper.js';\nimport { applyDataTransform } from './transform.js';\nimport DataStore from '../DataStore.js';\nimport { DefaultDataProvider } from './dataProvider.js';\n/**\r\n * [REQUIREMENT_MEMO]:\r\n * (0) `metaRawOption` means `dimensions`/`sourceHeader`/`seriesLayoutBy` in raw option.\r\n * (1) Keep support the feature: `metaRawOption` can be specified both on `series` and\r\n * `root-dataset`. Them on `series` has higher priority.\r\n * (2) Do not support to set `metaRawOption` on a `non-root-dataset`, because it might\r\n * confuse users: whether those props indicate how to visit the upstream source or visit\r\n * the transform result source, and some transforms has nothing to do with these props,\r\n * and some transforms might have multiple upstream.\r\n * (3) Transforms should specify `metaRawOption` in each output, just like they can be\r\n * declared in `root-dataset`.\r\n * (4) At present only support visit source in `SERIES_LAYOUT_BY_COLUMN` in transforms.\r\n * That is for reducing complexity in transforms.\r\n * PENDING: Whether to provide transposition transform?\r\n *\r\n * [IMPLEMENTAION_MEMO]:\r\n * \"sourceVisitConfig\" are calculated from `metaRawOption` and `data`.\r\n * They will not be calculated until `source` is about to be visited (to prevent from\r\n * duplicate calcuation). `source` is visited only in series and input to transforms.\r\n *\r\n * [DIMENSION_INHERIT_RULE]:\r\n * By default the dimensions are inherited from ancestors, unless a transform return\r\n * a new dimensions definition.\r\n * Consider the case:\r\n * ```js\r\n * dataset: [{\r\n *     source: [ ['Product', 'Sales', 'Prise'], ['Cookies', 321, 44.21], ...]\r\n * }, {\r\n *     transform: { type: 'filter', ... }\r\n * }]\r\n * dataset: [{\r\n *     dimension: ['Product', 'Sales', 'Prise'],\r\n *     source: [ ['Cookies', 321, 44.21], ...]\r\n * }, {\r\n *     transform: { type: 'filter', ... }\r\n * }]\r\n * ```\r\n * The two types of option should have the same behavior after transform.\r\n *\r\n *\r\n * [SCENARIO]:\r\n * (1) Provide source data directly:\r\n * ```js\r\n * series: {\r\n *     encode: {...},\r\n *     dimensions: [...]\r\n *     seriesLayoutBy: 'row',\r\n *     data: [[...]]\r\n * }\r\n * ```\r\n * (2) Series refer to dataset.\r\n * ```js\r\n * series: [{\r\n *     encode: {...}\r\n *     // Ignore datasetIndex means `datasetIndex: 0`\r\n *     // and the dimensions defination in dataset is used\r\n * }, {\r\n *     encode: {...},\r\n *     seriesLayoutBy: 'column',\r\n *     datasetIndex: 1\r\n * }]\r\n * ```\r\n * (3) dataset transform\r\n * ```js\r\n * dataset: [{\r\n *     source: [...]\r\n * }, {\r\n *     source: [...]\r\n * }, {\r\n *     // By default from 0.\r\n *     transform: { type: 'filter', config: {...} }\r\n * }, {\r\n *     // Piped.\r\n *     transform: [\r\n *         { type: 'filter', config: {...} },\r\n *         { type: 'sort', config: {...} }\r\n *     ]\r\n * }, {\r\n *     id: 'regressionData',\r\n *     fromDatasetIndex: 1,\r\n *     // Third-party transform\r\n *     transform: { type: 'ecStat:regression', config: {...} }\r\n * }, {\r\n *     // retrieve the extra result.\r\n *     id: 'regressionFormula',\r\n *     fromDatasetId: 'regressionData',\r\n *     fromTransformResult: 1\r\n * }]\r\n * ```\r\n */\nvar SourceManager = /** @class */function () {\n  function SourceManager(sourceHost) {\n    // Cached source. Do not repeat calculating if not dirty.\n    this._sourceList = [];\n    this._storeList = [];\n    // version sign of each upstream source manager.\n    this._upstreamSignList = [];\n    this._versionSignBase = 0;\n    this._dirty = true;\n    this._sourceHost = sourceHost;\n  }\n  /**\r\n   * Mark dirty.\r\n   */\n  SourceManager.prototype.dirty = function () {\n    this._setLocalSource([], []);\n    this._storeList = [];\n    this._dirty = true;\n  };\n  SourceManager.prototype._setLocalSource = function (sourceList, upstreamSignList) {\n    this._sourceList = sourceList;\n    this._upstreamSignList = upstreamSignList;\n    this._versionSignBase++;\n    if (this._versionSignBase > 9e10) {\n      this._versionSignBase = 0;\n    }\n  };\n  /**\r\n   * For detecting whether the upstream source is dirty, so that\r\n   * the local cached source (in `_sourceList`) should be discarded.\r\n   */\n  SourceManager.prototype._getVersionSign = function () {\n    return this._sourceHost.uid + '_' + this._versionSignBase;\n  };\n  /**\r\n   * Always return a source instance. Otherwise throw error.\r\n   */\n  SourceManager.prototype.prepareSource = function () {\n    // For the case that call `setOption` multiple time but no data changed,\n    // cache the result source to prevent from repeating transform.\n    if (this._isDirty()) {\n      this._createSource();\n      this._dirty = false;\n    }\n  };\n  SourceManager.prototype._createSource = function () {\n    this._setLocalSource([], []);\n    var sourceHost = this._sourceHost;\n    var upSourceMgrList = this._getUpstreamSourceManagers();\n    var hasUpstream = !!upSourceMgrList.length;\n    var resultSourceList;\n    var upstreamSignList;\n    if (isSeries(sourceHost)) {\n      var seriesModel = sourceHost;\n      var data = void 0;\n      var sourceFormat = void 0;\n      var upSource = void 0;\n      // Has upstream dataset\n      if (hasUpstream) {\n        var upSourceMgr = upSourceMgrList[0];\n        upSourceMgr.prepareSource();\n        upSource = upSourceMgr.getSource();\n        data = upSource.data;\n        sourceFormat = upSource.sourceFormat;\n        upstreamSignList = [upSourceMgr._getVersionSign()];\n      }\n      // Series data is from own.\n      else {\n        data = seriesModel.get('data', true);\n        sourceFormat = isTypedArray(data) ? SOURCE_FORMAT_TYPED_ARRAY : SOURCE_FORMAT_ORIGINAL;\n        upstreamSignList = [];\n      }\n      // See [REQUIREMENT_MEMO], merge settings on series and parent dataset if it is root.\n      var newMetaRawOption = this._getSourceMetaRawOption() || {};\n      var upMetaRawOption = upSource && upSource.metaRawOption || {};\n      var seriesLayoutBy = retrieve2(newMetaRawOption.seriesLayoutBy, upMetaRawOption.seriesLayoutBy) || null;\n      var sourceHeader = retrieve2(newMetaRawOption.sourceHeader, upMetaRawOption.sourceHeader);\n      // Note here we should not use `upSource.dimensionsDefine`. Consider the case:\n      // `upSource.dimensionsDefine` is detected by `seriesLayoutBy: 'column'`,\n      // but series need `seriesLayoutBy: 'row'`.\n      var dimensions = retrieve2(newMetaRawOption.dimensions, upMetaRawOption.dimensions);\n      // We share source with dataset as much as possible\n      // to avoid extra memory cost of high dimensional data.\n      var needsCreateSource = seriesLayoutBy !== upMetaRawOption.seriesLayoutBy || !!sourceHeader !== !!upMetaRawOption.sourceHeader || dimensions;\n      resultSourceList = needsCreateSource ? [createSource(data, {\n        seriesLayoutBy: seriesLayoutBy,\n        sourceHeader: sourceHeader,\n        dimensions: dimensions\n      }, sourceFormat)] : [];\n    } else {\n      var datasetModel = sourceHost;\n      // Has upstream dataset.\n      if (hasUpstream) {\n        var result = this._applyTransform(upSourceMgrList);\n        resultSourceList = result.sourceList;\n        upstreamSignList = result.upstreamSignList;\n      }\n      // Is root dataset.\n      else {\n        var sourceData = datasetModel.get('source', true);\n        resultSourceList = [createSource(sourceData, this._getSourceMetaRawOption(), null)];\n        upstreamSignList = [];\n      }\n    }\n    if (process.env.NODE_ENV !== 'production') {\n      assert(resultSourceList && upstreamSignList);\n    }\n    this._setLocalSource(resultSourceList, upstreamSignList);\n  };\n  SourceManager.prototype._applyTransform = function (upMgrList) {\n    var datasetModel = this._sourceHost;\n    var transformOption = datasetModel.get('transform', true);\n    var fromTransformResult = datasetModel.get('fromTransformResult', true);\n    if (process.env.NODE_ENV !== 'production') {\n      assert(fromTransformResult != null || transformOption != null);\n    }\n    if (fromTransformResult != null) {\n      var errMsg = '';\n      if (upMgrList.length !== 1) {\n        if (process.env.NODE_ENV !== 'production') {\n          errMsg = 'When using `fromTransformResult`, there should be only one upstream dataset';\n        }\n        doThrow(errMsg);\n      }\n    }\n    var sourceList;\n    var upSourceList = [];\n    var upstreamSignList = [];\n    each(upMgrList, function (upMgr) {\n      upMgr.prepareSource();\n      var upSource = upMgr.getSource(fromTransformResult || 0);\n      var errMsg = '';\n      if (fromTransformResult != null && !upSource) {\n        if (process.env.NODE_ENV !== 'production') {\n          errMsg = 'Can not retrieve result by `fromTransformResult`: ' + fromTransformResult;\n        }\n        doThrow(errMsg);\n      }\n      upSourceList.push(upSource);\n      upstreamSignList.push(upMgr._getVersionSign());\n    });\n    if (transformOption) {\n      sourceList = applyDataTransform(transformOption, upSourceList, {\n        datasetIndex: datasetModel.componentIndex\n      });\n    } else if (fromTransformResult != null) {\n      sourceList = [cloneSourceShallow(upSourceList[0])];\n    }\n    return {\n      sourceList: sourceList,\n      upstreamSignList: upstreamSignList\n    };\n  };\n  SourceManager.prototype._isDirty = function () {\n    if (this._dirty) {\n      return true;\n    }\n    // All sourceList is from the some upstream.\n    var upSourceMgrList = this._getUpstreamSourceManagers();\n    for (var i = 0; i < upSourceMgrList.length; i++) {\n      var upSrcMgr = upSourceMgrList[i];\n      if (\n      // Consider the case that there is ancestor diry, call it recursively.\n      // The performance is probably not an issue because usually the chain is not long.\n      upSrcMgr._isDirty() || this._upstreamSignList[i] !== upSrcMgr._getVersionSign()) {\n        return true;\n      }\n    }\n  };\n  /**\r\n   * @param sourceIndex By default 0, means \"main source\".\r\n   *                    In most cases there is only one source.\r\n   */\n  SourceManager.prototype.getSource = function (sourceIndex) {\n    sourceIndex = sourceIndex || 0;\n    var source = this._sourceList[sourceIndex];\n    if (!source) {\n      // Series may share source instance with dataset.\n      var upSourceMgrList = this._getUpstreamSourceManagers();\n      return upSourceMgrList[0] && upSourceMgrList[0].getSource(sourceIndex);\n    }\n    return source;\n  };\n  /**\r\n   *\r\n   * Get a data store which can be shared across series.\r\n   * Only available for series.\r\n   *\r\n   * @param seriesDimRequest Dimensions that are generated in series.\r\n   *        Should have been sorted by `storeDimIndex` asc.\r\n   */\n  SourceManager.prototype.getSharedDataStore = function (seriesDimRequest) {\n    if (process.env.NODE_ENV !== 'production') {\n      assert(isSeries(this._sourceHost), 'Can only call getDataStore on series source manager.');\n    }\n    var schema = seriesDimRequest.makeStoreSchema();\n    return this._innerGetDataStore(schema.dimensions, seriesDimRequest.source, schema.hash);\n  };\n  SourceManager.prototype._innerGetDataStore = function (storeDims, seriesSource, sourceReadKey) {\n    // TODO Can use other sourceIndex?\n    var sourceIndex = 0;\n    var storeList = this._storeList;\n    var cachedStoreMap = storeList[sourceIndex];\n    if (!cachedStoreMap) {\n      cachedStoreMap = storeList[sourceIndex] = {};\n    }\n    var cachedStore = cachedStoreMap[sourceReadKey];\n    if (!cachedStore) {\n      var upSourceMgr = this._getUpstreamSourceManagers()[0];\n      if (isSeries(this._sourceHost) && upSourceMgr) {\n        cachedStore = upSourceMgr._innerGetDataStore(storeDims, seriesSource, sourceReadKey);\n      } else {\n        cachedStore = new DataStore();\n        // Always create store from source of series.\n        cachedStore.initData(new DefaultDataProvider(seriesSource, storeDims.length), storeDims);\n      }\n      cachedStoreMap[sourceReadKey] = cachedStore;\n    }\n    return cachedStore;\n  };\n  /**\r\n   * PENDING: Is it fast enough?\r\n   * If no upstream, return empty array.\r\n   */\n  SourceManager.prototype._getUpstreamSourceManagers = function () {\n    // Always get the relationship from the raw option.\n    // Do not cache the link of the dependency graph, so that\n    // there is no need to update them when change happens.\n    var sourceHost = this._sourceHost;\n    if (isSeries(sourceHost)) {\n      var datasetModel = querySeriesUpstreamDatasetModel(sourceHost);\n      return !datasetModel ? [] : [datasetModel.getSourceManager()];\n    } else {\n      return map(queryDatasetUpstreamDatasetModels(sourceHost), function (datasetModel) {\n        return datasetModel.getSourceManager();\n      });\n    }\n  };\n  SourceManager.prototype._getSourceMetaRawOption = function () {\n    var sourceHost = this._sourceHost;\n    var seriesLayoutBy;\n    var sourceHeader;\n    var dimensions;\n    if (isSeries(sourceHost)) {\n      seriesLayoutBy = sourceHost.get('seriesLayoutBy', true);\n      sourceHeader = sourceHost.get('sourceHeader', true);\n      dimensions = sourceHost.get('dimensions', true);\n    }\n    // See [REQUIREMENT_MEMO], `non-root-dataset` do not support them.\n    else if (!this._getUpstreamSourceManagers().length) {\n      var model = sourceHost;\n      seriesLayoutBy = model.get('seriesLayoutBy', true);\n      sourceHeader = model.get('sourceHeader', true);\n      dimensions = model.get('dimensions', true);\n    }\n    return {\n      seriesLayoutBy: seriesLayoutBy,\n      sourceHeader: sourceHeader,\n      dimensions: dimensions\n    };\n  };\n  return SourceManager;\n}();\nexport { SourceManager };\n// Call this method after `super.init` and `super.mergeOption` to\n// disable the transform merge, but do not disable transform clone from rawOption.\nexport function disableTransformOptionMerge(datasetModel) {\n  var transformOption = datasetModel.option.transform;\n  transformOption && setAsPrimitive(datasetModel.option.transform);\n}\nfunction isSeries(sourceHost) {\n  // Avoid circular dependency with Series.ts\n  return sourceHost.mainType === 'series';\n}\nfunction doThrow(errMsg) {\n  throw new Error(errMsg);\n}"], "names": [], "mappings": "AACA;;;;;;;;;;;;;;;;;AAiBA,GAGA;;CAEC,GAED;;;;;;;;;;;;;;;;;AAiBA;;;;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;AACA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;CAyFC,GACD,IAAI,gBAAgB,WAAW,GAAE;IAC/B,SAAS,cAAc,UAAU;QAC/B,yDAAyD;QACzD,IAAI,CAAC,WAAW,GAAG,EAAE;QACrB,IAAI,CAAC,UAAU,GAAG,EAAE;QACpB,gDAAgD;QAChD,IAAI,CAAC,iBAAiB,GAAG,EAAE;QAC3B,IAAI,CAAC,gBAAgB,GAAG;QACxB,IAAI,CAAC,MAAM,GAAG;QACd,IAAI,CAAC,WAAW,GAAG;IACrB;IACA;;GAEC,GACD,cAAc,SAAS,CAAC,KAAK,GAAG;QAC9B,IAAI,CAAC,eAAe,CAAC,EAAE,EAAE,EAAE;QAC3B,IAAI,CAAC,UAAU,GAAG,EAAE;QACpB,IAAI,CAAC,MAAM,GAAG;IAChB;IACA,cAAc,SAAS,CAAC,eAAe,GAAG,SAAU,UAAU,EAAE,gBAAgB;QAC9E,IAAI,CAAC,WAAW,GAAG;QACnB,IAAI,CAAC,iBAAiB,GAAG;QACzB,IAAI,CAAC,gBAAgB;QACrB,IAAI,IAAI,CAAC,gBAAgB,GAAG,MAAM;YAChC,IAAI,CAAC,gBAAgB,GAAG;QAC1B;IACF;IACA;;;GAGC,GACD,cAAc,SAAS,CAAC,eAAe,GAAG;QACxC,OAAO,IAAI,CAAC,WAAW,CAAC,GAAG,GAAG,MAAM,IAAI,CAAC,gBAAgB;IAC3D;IACA;;GAEC,GACD,cAAc,SAAS,CAAC,aAAa,GAAG;QACtC,wEAAwE;QACxE,+DAA+D;QAC/D,IAAI,IAAI,CAAC,QAAQ,IAAI;YACnB,IAAI,CAAC,aAAa;YAClB,IAAI,CAAC,MAAM,GAAG;QAChB;IACF;IACA,cAAc,SAAS,CAAC,aAAa,GAAG;QACtC,IAAI,CAAC,eAAe,CAAC,EAAE,EAAE,EAAE;QAC3B,IAAI,aAAa,IAAI,CAAC,WAAW;QACjC,IAAI,kBAAkB,IAAI,CAAC,0BAA0B;QACrD,IAAI,cAAc,CAAC,CAAC,gBAAgB,MAAM;QAC1C,IAAI;QACJ,IAAI;QACJ,IAAI,SAAS,aAAa;YACxB,IAAI,cAAc;YAClB,IAAI,OAAO,KAAK;YAChB,IAAI,eAAe,KAAK;YACxB,IAAI,WAAW,KAAK;YACpB,uBAAuB;YACvB,IAAI,aAAa;gBACf,IAAI,cAAc,eAAe,CAAC,EAAE;gBACpC,YAAY,aAAa;gBACzB,WAAW,YAAY,SAAS;gBAChC,OAAO,SAAS,IAAI;gBACpB,eAAe,SAAS,YAAY;gBACpC,mBAAmB;oBAAC,YAAY,eAAe;iBAAG;YACpD,OAEK;gBACH,OAAO,YAAY,GAAG,CAAC,QAAQ;gBAC/B,eAAe,CAAA,GAAA,8IAAA,CAAA,eAAY,AAAD,EAAE,QAAQ,+IAAA,CAAA,4BAAyB,GAAG,+IAAA,CAAA,yBAAsB;gBACtF,mBAAmB,EAAE;YACvB;YACA,qFAAqF;YACrF,IAAI,mBAAmB,IAAI,CAAC,uBAAuB,MAAM,CAAC;YAC1D,IAAI,kBAAkB,YAAY,SAAS,aAAa,IAAI,CAAC;YAC7D,IAAI,iBAAiB,CAAA,GAAA,8IAAA,CAAA,YAAS,AAAD,EAAE,iBAAiB,cAAc,EAAE,gBAAgB,cAAc,KAAK;YACnG,IAAI,eAAe,CAAA,GAAA,8IAAA,CAAA,YAAS,AAAD,EAAE,iBAAiB,YAAY,EAAE,gBAAgB,YAAY;YACxF,8EAA8E;YAC9E,yEAAyE;YACzE,2CAA2C;YAC3C,IAAI,aAAa,CAAA,GAAA,8IAAA,CAAA,YAAS,AAAD,EAAE,iBAAiB,UAAU,EAAE,gBAAgB,UAAU;YAClF,mDAAmD;YACnD,uDAAuD;YACvD,IAAI,oBAAoB,mBAAmB,gBAAgB,cAAc,IAAI,CAAC,CAAC,iBAAiB,CAAC,CAAC,gBAAgB,YAAY,IAAI;YAClI,mBAAmB,oBAAoB;gBAAC,CAAA,GAAA,gJAAA,CAAA,eAAY,AAAD,EAAE,MAAM;oBACzD,gBAAgB;oBAChB,cAAc;oBACd,YAAY;gBACd,GAAG;aAAc,GAAG,EAAE;QACxB,OAAO;YACL,IAAI,eAAe;YACnB,wBAAwB;YACxB,IAAI,aAAa;gBACf,IAAI,SAAS,IAAI,CAAC,eAAe,CAAC;gBAClC,mBAAmB,OAAO,UAAU;gBACpC,mBAAmB,OAAO,gBAAgB;YAC5C,OAEK;gBACH,IAAI,aAAa,aAAa,GAAG,CAAC,UAAU;gBAC5C,mBAAmB;oBAAC,CAAA,GAAA,gJAAA,CAAA,eAAY,AAAD,EAAE,YAAY,IAAI,CAAC,uBAAuB,IAAI;iBAAM;gBACnF,mBAAmB,EAAE;YACvB;QACF;QACA,wCAA2C;YACzC,CAAA,GAAA,8IAAA,CAAA,SAAM,AAAD,EAAE,oBAAoB;QAC7B;QACA,IAAI,CAAC,eAAe,CAAC,kBAAkB;IACzC;IACA,cAAc,SAAS,CAAC,eAAe,GAAG,SAAU,SAAS;QAC3D,IAAI,eAAe,IAAI,CAAC,WAAW;QACnC,IAAI,kBAAkB,aAAa,GAAG,CAAC,aAAa;QACpD,IAAI,sBAAsB,aAAa,GAAG,CAAC,uBAAuB;QAClE,wCAA2C;YACzC,CAAA,GAAA,8IAAA,CAAA,SAAM,AAAD,EAAE,uBAAuB,QAAQ,mBAAmB;QAC3D;QACA,IAAI,uBAAuB,MAAM;YAC/B,IAAI,SAAS;YACb,IAAI,UAAU,MAAM,KAAK,GAAG;gBAC1B,IAAI,oDAAyB,cAAc;oBACzC,SAAS;gBACX;gBACA,QAAQ;YACV;QACF;QACA,IAAI;QACJ,IAAI,eAAe,EAAE;QACrB,IAAI,mBAAmB,EAAE;QACzB,CAAA,GAAA,8IAAA,CAAA,OAAI,AAAD,EAAE,WAAW,SAAU,KAAK;YAC7B,MAAM,aAAa;YACnB,IAAI,WAAW,MAAM,SAAS,CAAC,uBAAuB;YACtD,IAAI,SAAS;YACb,IAAI,uBAAuB,QAAQ,CAAC,UAAU;gBAC5C,IAAI,oDAAyB,cAAc;oBACzC,SAAS,uDAAuD;gBAClE;gBACA,QAAQ;YACV;YACA,aAAa,IAAI,CAAC;YAClB,iBAAiB,IAAI,CAAC,MAAM,eAAe;QAC7C;QACA,IAAI,iBAAiB;YACnB,aAAa,CAAA,GAAA,6JAAA,CAAA,qBAAkB,AAAD,EAAE,iBAAiB,cAAc;gBAC7D,cAAc,aAAa,cAAc;YAC3C;QACF,OAAO,IAAI,uBAAuB,MAAM;YACtC,aAAa;gBAAC,CAAA,GAAA,gJAAA,CAAA,qBAAkB,AAAD,EAAE,YAAY,CAAC,EAAE;aAAE;QACpD;QACA,OAAO;YACL,YAAY;YACZ,kBAAkB;QACpB;IACF;IACA,cAAc,SAAS,CAAC,QAAQ,GAAG;QACjC,IAAI,IAAI,CAAC,MAAM,EAAE;YACf,OAAO;QACT;QACA,4CAA4C;QAC5C,IAAI,kBAAkB,IAAI,CAAC,0BAA0B;QACrD,IAAK,IAAI,IAAI,GAAG,IAAI,gBAAgB,MAAM,EAAE,IAAK;YAC/C,IAAI,WAAW,eAAe,CAAC,EAAE;YACjC,IACA,sEAAsE;YACtE,kFAAkF;YAClF,SAAS,QAAQ,MAAM,IAAI,CAAC,iBAAiB,CAAC,EAAE,KAAK,SAAS,eAAe,IAAI;gBAC/E,OAAO;YACT;QACF;IACF;IACA;;;GAGC,GACD,cAAc,SAAS,CAAC,SAAS,GAAG,SAAU,WAAW;QACvD,cAAc,eAAe;QAC7B,IAAI,SAAS,IAAI,CAAC,WAAW,CAAC,YAAY;QAC1C,IAAI,CAAC,QAAQ;YACX,iDAAiD;YACjD,IAAI,kBAAkB,IAAI,CAAC,0BAA0B;YACrD,OAAO,eAAe,CAAC,EAAE,IAAI,eAAe,CAAC,EAAE,CAAC,SAAS,CAAC;QAC5D;QACA,OAAO;IACT;IACA;;;;;;;GAOC,GACD,cAAc,SAAS,CAAC,kBAAkB,GAAG,SAAU,gBAAgB;QACrE,wCAA2C;YACzC,CAAA,GAAA,8IAAA,CAAA,SAAM,AAAD,EAAE,SAAS,IAAI,CAAC,WAAW,GAAG;QACrC;QACA,IAAI,SAAS,iBAAiB,eAAe;QAC7C,OAAO,IAAI,CAAC,kBAAkB,CAAC,OAAO,UAAU,EAAE,iBAAiB,MAAM,EAAE,OAAO,IAAI;IACxF;IACA,cAAc,SAAS,CAAC,kBAAkB,GAAG,SAAU,SAAS,EAAE,YAAY,EAAE,aAAa;QAC3F,kCAAkC;QAClC,IAAI,cAAc;QAClB,IAAI,YAAY,IAAI,CAAC,UAAU;QAC/B,IAAI,iBAAiB,SAAS,CAAC,YAAY;QAC3C,IAAI,CAAC,gBAAgB;YACnB,iBAAiB,SAAS,CAAC,YAAY,GAAG,CAAC;QAC7C;QACA,IAAI,cAAc,cAAc,CAAC,cAAc;QAC/C,IAAI,CAAC,aAAa;YAChB,IAAI,cAAc,IAAI,CAAC,0BAA0B,EAAE,CAAC,EAAE;YACtD,IAAI,SAAS,IAAI,CAAC,WAAW,KAAK,aAAa;gBAC7C,cAAc,YAAY,kBAAkB,CAAC,WAAW,cAAc;YACxE,OAAO;gBACL,cAAc,IAAI,mJAAA,CAAA,UAAS;gBAC3B,6CAA6C;gBAC7C,YAAY,QAAQ,CAAC,IAAI,gKAAA,CAAA,sBAAmB,CAAC,cAAc,UAAU,MAAM,GAAG;YAChF;YACA,cAAc,CAAC,cAAc,GAAG;QAClC;QACA,OAAO;IACT;IACA;;;GAGC,GACD,cAAc,SAAS,CAAC,0BAA0B,GAAG;QACnD,mDAAmD;QACnD,yDAAyD;QACzD,uDAAuD;QACvD,IAAI,aAAa,IAAI,CAAC,WAAW;QACjC,IAAI,SAAS,aAAa;YACxB,IAAI,eAAe,CAAA,GAAA,gKAAA,CAAA,kCAA+B,AAAD,EAAE;YACnD,OAAO,CAAC,eAAe,EAAE,GAAG;gBAAC,aAAa,gBAAgB;aAAG;QAC/D,OAAO;YACL,OAAO,CAAA,GAAA,8IAAA,CAAA,MAAG,AAAD,EAAE,CAAA,GAAA,gKAAA,CAAA,oCAAiC,AAAD,EAAE,aAAa,SAAU,YAAY;gBAC9E,OAAO,aAAa,gBAAgB;YACtC;QACF;IACF;IACA,cAAc,SAAS,CAAC,uBAAuB,GAAG;QAChD,IAAI,aAAa,IAAI,CAAC,WAAW;QACjC,IAAI;QACJ,IAAI;QACJ,IAAI;QACJ,IAAI,SAAS,aAAa;YACxB,iBAAiB,WAAW,GAAG,CAAC,kBAAkB;YAClD,eAAe,WAAW,GAAG,CAAC,gBAAgB;YAC9C,aAAa,WAAW,GAAG,CAAC,cAAc;QAC5C,OAEK,IAAI,CAAC,IAAI,CAAC,0BAA0B,GAAG,MAAM,EAAE;YAClD,IAAI,QAAQ;YACZ,iBAAiB,MAAM,GAAG,CAAC,kBAAkB;YAC7C,eAAe,MAAM,GAAG,CAAC,gBAAgB;YACzC,aAAa,MAAM,GAAG,CAAC,cAAc;QACvC;QACA,OAAO;YACL,gBAAgB;YAChB,cAAc;YACd,YAAY;QACd;IACF;IACA,OAAO;AACT;;AAIO,SAAS,4BAA4B,YAAY;IACtD,IAAI,kBAAkB,aAAa,MAAM,CAAC,SAAS;IACnD,mBAAmB,CAAA,GAAA,8IAAA,CAAA,iBAAc,AAAD,EAAE,aAAa,MAAM,CAAC,SAAS;AACjE;AACA,SAAS,SAAS,UAAU;IAC1B,2CAA2C;IAC3C,OAAO,WAAW,QAAQ,KAAK;AACjC;AACA,SAAS,QAAQ,MAAM;IACrB,MAAM,IAAI,MAAM;AAClB", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 3048, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/tindahan/sari-sari-admin/node_modules/echarts/lib/data/DataDiffer.js"], "sourcesContent": ["\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\r\n* Licensed to the Apache Software Foundation (ASF) under one\r\n* or more contributor license agreements.  See the NOTICE file\r\n* distributed with this work for additional information\r\n* regarding copyright ownership.  The ASF licenses this file\r\n* to you under the Apache License, Version 2.0 (the\r\n* \"License\"); you may not use this file except in compliance\r\n* with the License.  You may obtain a copy of the License at\r\n*\r\n*   http://www.apache.org/licenses/LICENSE-2.0\r\n*\r\n* Unless required by applicable law or agreed to in writing,\r\n* software distributed under the License is distributed on an\r\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\r\n* KIND, either express or implied.  See the License for the\r\n* specific language governing permissions and limitations\r\n* under the License.\r\n*/\nfunction dataIndexMapValueLength(valNumOrArrLengthMoreThan2) {\n  return valNumOrArrLengthMoreThan2 == null ? 0 : valNumOrArrLengthMoreThan2.length || 1;\n}\nfunction defaultKeyGetter(item) {\n  return item;\n}\nvar DataDiffer = /** @class */function () {\n  /**\r\n   * @param context Can be visited by this.context in callback.\r\n   */\n  function DataDiffer(oldArr, newArr, oldKeyGetter, newKeyGetter, context,\n  // By default: 'oneToOne'.\n  diffMode) {\n    this._old = oldArr;\n    this._new = newArr;\n    this._oldKeyGetter = oldKeyGetter || defaultKeyGetter;\n    this._newKeyGetter = newKeyGetter || defaultKeyGetter;\n    // Visible in callback via `this.context`;\n    this.context = context;\n    this._diffModeMultiple = diffMode === 'multiple';\n  }\n  /**\r\n   * Callback function when add a data\r\n   */\n  DataDiffer.prototype.add = function (func) {\n    this._add = func;\n    return this;\n  };\n  /**\r\n   * Callback function when update a data\r\n   */\n  DataDiffer.prototype.update = function (func) {\n    this._update = func;\n    return this;\n  };\n  /**\r\n   * Callback function when update a data and only work in `cbMode: 'byKey'`.\r\n   */\n  DataDiffer.prototype.updateManyToOne = function (func) {\n    this._updateManyToOne = func;\n    return this;\n  };\n  /**\r\n   * Callback function when update a data and only work in `cbMode: 'byKey'`.\r\n   */\n  DataDiffer.prototype.updateOneToMany = function (func) {\n    this._updateOneToMany = func;\n    return this;\n  };\n  /**\r\n   * Callback function when update a data and only work in `cbMode: 'byKey'`.\r\n   */\n  DataDiffer.prototype.updateManyToMany = function (func) {\n    this._updateManyToMany = func;\n    return this;\n  };\n  /**\r\n   * Callback function when remove a data\r\n   */\n  DataDiffer.prototype.remove = function (func) {\n    this._remove = func;\n    return this;\n  };\n  DataDiffer.prototype.execute = function () {\n    this[this._diffModeMultiple ? '_executeMultiple' : '_executeOneToOne']();\n  };\n  DataDiffer.prototype._executeOneToOne = function () {\n    var oldArr = this._old;\n    var newArr = this._new;\n    var newDataIndexMap = {};\n    var oldDataKeyArr = new Array(oldArr.length);\n    var newDataKeyArr = new Array(newArr.length);\n    this._initIndexMap(oldArr, null, oldDataKeyArr, '_oldKeyGetter');\n    this._initIndexMap(newArr, newDataIndexMap, newDataKeyArr, '_newKeyGetter');\n    for (var i = 0; i < oldArr.length; i++) {\n      var oldKey = oldDataKeyArr[i];\n      var newIdxMapVal = newDataIndexMap[oldKey];\n      var newIdxMapValLen = dataIndexMapValueLength(newIdxMapVal);\n      // idx can never be empty array here. see 'set null' logic below.\n      if (newIdxMapValLen > 1) {\n        // Consider there is duplicate key (for example, use dataItem.name as key).\n        // We should make sure every item in newArr and oldArr can be visited.\n        var newIdx = newIdxMapVal.shift();\n        if (newIdxMapVal.length === 1) {\n          newDataIndexMap[oldKey] = newIdxMapVal[0];\n        }\n        this._update && this._update(newIdx, i);\n      } else if (newIdxMapValLen === 1) {\n        newDataIndexMap[oldKey] = null;\n        this._update && this._update(newIdxMapVal, i);\n      } else {\n        this._remove && this._remove(i);\n      }\n    }\n    this._performRestAdd(newDataKeyArr, newDataIndexMap);\n  };\n  /**\r\n   * For example, consider the case:\r\n   * oldData: [o0, o1, o2, o3, o4, o5, o6, o7],\r\n   * newData: [n0, n1, n2, n3, n4, n5, n6, n7, n8],\r\n   * Where:\r\n   *     o0, o1, n0 has key 'a' (many to one)\r\n   *     o5, n4, n5, n6 has key 'b' (one to many)\r\n   *     o2, n1 has key 'c' (one to one)\r\n   *     n2, n3 has key 'd' (add)\r\n   *     o3, o4 has key 'e' (remove)\r\n   *     o6, o7, n7, n8 has key 'f' (many to many, treated as add and remove)\r\n   * Then:\r\n   *     (The order of the following directives are not ensured.)\r\n   *     this._updateManyToOne(n0, [o0, o1]);\r\n   *     this._updateOneToMany([n4, n5, n6], o5);\r\n   *     this._update(n1, o2);\r\n   *     this._remove(o3);\r\n   *     this._remove(o4);\r\n   *     this._remove(o6);\r\n   *     this._remove(o7);\r\n   *     this._add(n2);\r\n   *     this._add(n3);\r\n   *     this._add(n7);\r\n   *     this._add(n8);\r\n   */\n  DataDiffer.prototype._executeMultiple = function () {\n    var oldArr = this._old;\n    var newArr = this._new;\n    var oldDataIndexMap = {};\n    var newDataIndexMap = {};\n    var oldDataKeyArr = [];\n    var newDataKeyArr = [];\n    this._initIndexMap(oldArr, oldDataIndexMap, oldDataKeyArr, '_oldKeyGetter');\n    this._initIndexMap(newArr, newDataIndexMap, newDataKeyArr, '_newKeyGetter');\n    for (var i = 0; i < oldDataKeyArr.length; i++) {\n      var oldKey = oldDataKeyArr[i];\n      var oldIdxMapVal = oldDataIndexMap[oldKey];\n      var newIdxMapVal = newDataIndexMap[oldKey];\n      var oldIdxMapValLen = dataIndexMapValueLength(oldIdxMapVal);\n      var newIdxMapValLen = dataIndexMapValueLength(newIdxMapVal);\n      if (oldIdxMapValLen > 1 && newIdxMapValLen === 1) {\n        this._updateManyToOne && this._updateManyToOne(newIdxMapVal, oldIdxMapVal);\n        newDataIndexMap[oldKey] = null;\n      } else if (oldIdxMapValLen === 1 && newIdxMapValLen > 1) {\n        this._updateOneToMany && this._updateOneToMany(newIdxMapVal, oldIdxMapVal);\n        newDataIndexMap[oldKey] = null;\n      } else if (oldIdxMapValLen === 1 && newIdxMapValLen === 1) {\n        this._update && this._update(newIdxMapVal, oldIdxMapVal);\n        newDataIndexMap[oldKey] = null;\n      } else if (oldIdxMapValLen > 1 && newIdxMapValLen > 1) {\n        this._updateManyToMany && this._updateManyToMany(newIdxMapVal, oldIdxMapVal);\n        newDataIndexMap[oldKey] = null;\n      } else if (oldIdxMapValLen > 1) {\n        for (var i_1 = 0; i_1 < oldIdxMapValLen; i_1++) {\n          this._remove && this._remove(oldIdxMapVal[i_1]);\n        }\n      } else {\n        this._remove && this._remove(oldIdxMapVal);\n      }\n    }\n    this._performRestAdd(newDataKeyArr, newDataIndexMap);\n  };\n  DataDiffer.prototype._performRestAdd = function (newDataKeyArr, newDataIndexMap) {\n    for (var i = 0; i < newDataKeyArr.length; i++) {\n      var newKey = newDataKeyArr[i];\n      var newIdxMapVal = newDataIndexMap[newKey];\n      var idxMapValLen = dataIndexMapValueLength(newIdxMapVal);\n      if (idxMapValLen > 1) {\n        for (var j = 0; j < idxMapValLen; j++) {\n          this._add && this._add(newIdxMapVal[j]);\n        }\n      } else if (idxMapValLen === 1) {\n        this._add && this._add(newIdxMapVal);\n      }\n      // Support both `newDataKeyArr` are duplication removed or not removed.\n      newDataIndexMap[newKey] = null;\n    }\n  };\n  DataDiffer.prototype._initIndexMap = function (arr,\n  // Can be null.\n  map,\n  // In 'byKey', the output `keyArr` is duplication removed.\n  // In 'byIndex', the output `keyArr` is not duplication removed and\n  //     its indices are accurately corresponding to `arr`.\n  keyArr, keyGetterName) {\n    var cbModeMultiple = this._diffModeMultiple;\n    for (var i = 0; i < arr.length; i++) {\n      // Add prefix to avoid conflict with Object.prototype.\n      var key = '_ec_' + this[keyGetterName](arr[i], i);\n      if (!cbModeMultiple) {\n        keyArr[i] = key;\n      }\n      if (!map) {\n        continue;\n      }\n      var idxMapVal = map[key];\n      var idxMapValLen = dataIndexMapValueLength(idxMapVal);\n      if (idxMapValLen === 0) {\n        // Simple optimize: in most cases, one index has one key,\n        // do not need array.\n        map[key] = i;\n        if (cbModeMultiple) {\n          keyArr.push(key);\n        }\n      } else if (idxMapValLen === 1) {\n        map[key] = [idxMapVal, i];\n      } else {\n        idxMapVal.push(i);\n      }\n    }\n  };\n  return DataDiffer;\n}();\nexport default DataDiffer;"], "names": [], "mappings": "AACA;;;;;;;;;;;;;;;;;AAiBA,GAGA;;CAEC,GAED;;;;;;;;;;;;;;;;;AAiBA;;;AACA,SAAS,wBAAwB,0BAA0B;IACzD,OAAO,8BAA8B,OAAO,IAAI,2BAA2B,MAAM,IAAI;AACvF;AACA,SAAS,iBAAiB,IAAI;IAC5B,OAAO;AACT;AACA,IAAI,aAAa,WAAW,GAAE;IAC5B;;GAEC,GACD,SAAS,WAAW,MAAM,EAAE,MAAM,EAAE,YAAY,EAAE,YAAY,EAAE,OAAO,EACvE,0BAA0B;IAC1B,QAAQ;QACN,IAAI,CAAC,IAAI,GAAG;QACZ,IAAI,CAAC,IAAI,GAAG;QACZ,IAAI,CAAC,aAAa,GAAG,gBAAgB;QACrC,IAAI,CAAC,aAAa,GAAG,gBAAgB;QACrC,0CAA0C;QAC1C,IAAI,CAAC,OAAO,GAAG;QACf,IAAI,CAAC,iBAAiB,GAAG,aAAa;IACxC;IACA;;GAEC,GACD,WAAW,SAAS,CAAC,GAAG,GAAG,SAAU,IAAI;QACvC,IAAI,CAAC,IAAI,GAAG;QACZ,OAAO,IAAI;IACb;IACA;;GAEC,GACD,WAAW,SAAS,CAAC,MAAM,GAAG,SAAU,IAAI;QAC1C,IAAI,CAAC,OAAO,GAAG;QACf,OAAO,IAAI;IACb;IACA;;GAEC,GACD,WAAW,SAAS,CAAC,eAAe,GAAG,SAAU,IAAI;QACnD,IAAI,CAAC,gBAAgB,GAAG;QACxB,OAAO,IAAI;IACb;IACA;;GAEC,GACD,WAAW,SAAS,CAAC,eAAe,GAAG,SAAU,IAAI;QACnD,IAAI,CAAC,gBAAgB,GAAG;QACxB,OAAO,IAAI;IACb;IACA;;GAEC,GACD,WAAW,SAAS,CAAC,gBAAgB,GAAG,SAAU,IAAI;QACpD,IAAI,CAAC,iBAAiB,GAAG;QACzB,OAAO,IAAI;IACb;IACA;;GAEC,GACD,WAAW,SAAS,CAAC,MAAM,GAAG,SAAU,IAAI;QAC1C,IAAI,CAAC,OAAO,GAAG;QACf,OAAO,IAAI;IACb;IACA,WAAW,SAAS,CAAC,OAAO,GAAG;QAC7B,IAAI,CAAC,IAAI,CAAC,iBAAiB,GAAG,qBAAqB,mBAAmB;IACxE;IACA,WAAW,SAAS,CAAC,gBAAgB,GAAG;QACtC,IAAI,SAAS,IAAI,CAAC,IAAI;QACtB,IAAI,SAAS,IAAI,CAAC,IAAI;QACtB,IAAI,kBAAkB,CAAC;QACvB,IAAI,gBAAgB,IAAI,MAAM,OAAO,MAAM;QAC3C,IAAI,gBAAgB,IAAI,MAAM,OAAO,MAAM;QAC3C,IAAI,CAAC,aAAa,CAAC,QAAQ,MAAM,eAAe;QAChD,IAAI,CAAC,aAAa,CAAC,QAAQ,iBAAiB,eAAe;QAC3D,IAAK,IAAI,IAAI,GAAG,IAAI,OAAO,MAAM,EAAE,IAAK;YACtC,IAAI,SAAS,aAAa,CAAC,EAAE;YAC7B,IAAI,eAAe,eAAe,CAAC,OAAO;YAC1C,IAAI,kBAAkB,wBAAwB;YAC9C,iEAAiE;YACjE,IAAI,kBAAkB,GAAG;gBACvB,2EAA2E;gBAC3E,sEAAsE;gBACtE,IAAI,SAAS,aAAa,KAAK;gBAC/B,IAAI,aAAa,MAAM,KAAK,GAAG;oBAC7B,eAAe,CAAC,OAAO,GAAG,YAAY,CAAC,EAAE;gBAC3C;gBACA,IAAI,CAAC,OAAO,IAAI,IAAI,CAAC,OAAO,CAAC,QAAQ;YACvC,OAAO,IAAI,oBAAoB,GAAG;gBAChC,eAAe,CAAC,OAAO,GAAG;gBAC1B,IAAI,CAAC,OAAO,IAAI,IAAI,CAAC,OAAO,CAAC,cAAc;YAC7C,OAAO;gBACL,IAAI,CAAC,OAAO,IAAI,IAAI,CAAC,OAAO,CAAC;YAC/B;QACF;QACA,IAAI,CAAC,eAAe,CAAC,eAAe;IACtC;IACA;;;;;;;;;;;;;;;;;;;;;;;;GAwBC,GACD,WAAW,SAAS,CAAC,gBAAgB,GAAG;QACtC,IAAI,SAAS,IAAI,CAAC,IAAI;QACtB,IAAI,SAAS,IAAI,CAAC,IAAI;QACtB,IAAI,kBAAkB,CAAC;QACvB,IAAI,kBAAkB,CAAC;QACvB,IAAI,gBAAgB,EAAE;QACtB,IAAI,gBAAgB,EAAE;QACtB,IAAI,CAAC,aAAa,CAAC,QAAQ,iBAAiB,eAAe;QAC3D,IAAI,CAAC,aAAa,CAAC,QAAQ,iBAAiB,eAAe;QAC3D,IAAK,IAAI,IAAI,GAAG,IAAI,cAAc,MAAM,EAAE,IAAK;YAC7C,IAAI,SAAS,aAAa,CAAC,EAAE;YAC7B,IAAI,eAAe,eAAe,CAAC,OAAO;YAC1C,IAAI,eAAe,eAAe,CAAC,OAAO;YAC1C,IAAI,kBAAkB,wBAAwB;YAC9C,IAAI,kBAAkB,wBAAwB;YAC9C,IAAI,kBAAkB,KAAK,oBAAoB,GAAG;gBAChD,IAAI,CAAC,gBAAgB,IAAI,IAAI,CAAC,gBAAgB,CAAC,cAAc;gBAC7D,eAAe,CAAC,OAAO,GAAG;YAC5B,OAAO,IAAI,oBAAoB,KAAK,kBAAkB,GAAG;gBACvD,IAAI,CAAC,gBAAgB,IAAI,IAAI,CAAC,gBAAgB,CAAC,cAAc;gBAC7D,eAAe,CAAC,OAAO,GAAG;YAC5B,OAAO,IAAI,oBAAoB,KAAK,oBAAoB,GAAG;gBACzD,IAAI,CAAC,OAAO,IAAI,IAAI,CAAC,OAAO,CAAC,cAAc;gBAC3C,eAAe,CAAC,OAAO,GAAG;YAC5B,OAAO,IAAI,kBAAkB,KAAK,kBAAkB,GAAG;gBACrD,IAAI,CAAC,iBAAiB,IAAI,IAAI,CAAC,iBAAiB,CAAC,cAAc;gBAC/D,eAAe,CAAC,OAAO,GAAG;YAC5B,OAAO,IAAI,kBAAkB,GAAG;gBAC9B,IAAK,IAAI,MAAM,GAAG,MAAM,iBAAiB,MAAO;oBAC9C,IAAI,CAAC,OAAO,IAAI,IAAI,CAAC,OAAO,CAAC,YAAY,CAAC,IAAI;gBAChD;YACF,OAAO;gBACL,IAAI,CAAC,OAAO,IAAI,IAAI,CAAC,OAAO,CAAC;YAC/B;QACF;QACA,IAAI,CAAC,eAAe,CAAC,eAAe;IACtC;IACA,WAAW,SAAS,CAAC,eAAe,GAAG,SAAU,aAAa,EAAE,eAAe;QAC7E,IAAK,IAAI,IAAI,GAAG,IAAI,cAAc,MAAM,EAAE,IAAK;YAC7C,IAAI,SAAS,aAAa,CAAC,EAAE;YAC7B,IAAI,eAAe,eAAe,CAAC,OAAO;YAC1C,IAAI,eAAe,wBAAwB;YAC3C,IAAI,eAAe,GAAG;gBACpB,IAAK,IAAI,IAAI,GAAG,IAAI,cAAc,IAAK;oBACrC,IAAI,CAAC,IAAI,IAAI,IAAI,CAAC,IAAI,CAAC,YAAY,CAAC,EAAE;gBACxC;YACF,OAAO,IAAI,iBAAiB,GAAG;gBAC7B,IAAI,CAAC,IAAI,IAAI,IAAI,CAAC,IAAI,CAAC;YACzB;YACA,uEAAuE;YACvE,eAAe,CAAC,OAAO,GAAG;QAC5B;IACF;IACA,WAAW,SAAS,CAAC,aAAa,GAAG,SAAU,GAAG,EAClD,eAAe;IACf,GAAG,EACH,0DAA0D;IAC1D,mEAAmE;IACnE,yDAAyD;IACzD,MAAM,EAAE,aAAa;QACnB,IAAI,iBAAiB,IAAI,CAAC,iBAAiB;QAC3C,IAAK,IAAI,IAAI,GAAG,IAAI,IAAI,MAAM,EAAE,IAAK;YACnC,sDAAsD;YACtD,IAAI,MAAM,SAAS,IAAI,CAAC,cAAc,CAAC,GAAG,CAAC,EAAE,EAAE;YAC/C,IAAI,CAAC,gBAAgB;gBACnB,MAAM,CAAC,EAAE,GAAG;YACd;YACA,IAAI,CAAC,KAAK;gBACR;YACF;YACA,IAAI,YAAY,GAAG,CAAC,IAAI;YACxB,IAAI,eAAe,wBAAwB;YAC3C,IAAI,iBAAiB,GAAG;gBACtB,yDAAyD;gBACzD,qBAAqB;gBACrB,GAAG,CAAC,IAAI,GAAG;gBACX,IAAI,gBAAgB;oBAClB,OAAO,IAAI,CAAC;gBACd;YACF,OAAO,IAAI,iBAAiB,GAAG;gBAC7B,GAAG,CAAC,IAAI,GAAG;oBAAC;oBAAW;iBAAE;YAC3B,OAAO;gBACL,UAAU,IAAI,CAAC;YACjB;QACF;IACF;IACA,OAAO;AACT;uCACe", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 3295, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/tindahan/sari-sari-admin/node_modules/echarts/lib/data/helper/dimensionHelper.js"], "sourcesContent": ["\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\r\n* Licensed to the Apache Software Foundation (ASF) under one\r\n* or more contributor license agreements.  See the NOTICE file\r\n* distributed with this work for additional information\r\n* regarding copyright ownership.  The ASF licenses this file\r\n* to you under the Apache License, Version 2.0 (the\r\n* \"License\"); you may not use this file except in compliance\r\n* with the License.  You may obtain a copy of the License at\r\n*\r\n*   http://www.apache.org/licenses/LICENSE-2.0\r\n*\r\n* Unless required by applicable law or agreed to in writing,\r\n* software distributed under the License is distributed on an\r\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\r\n* KIND, either express or implied.  See the License for the\r\n* specific language governing permissions and limitations\r\n* under the License.\r\n*/\nimport { each, createHashMap, assert, map } from 'zrender/lib/core/util.js';\nimport { VISUAL_DIMENSIONS } from '../../util/types.js';\nvar DimensionUserOuput = /** @class */function () {\n  function DimensionUserOuput(encode, dimRequest) {\n    this._encode = encode;\n    this._schema = dimRequest;\n  }\n  DimensionUserOuput.prototype.get = function () {\n    return {\n      // Do not generate full dimension name until fist used.\n      fullDimensions: this._getFullDimensionNames(),\n      encode: this._encode\n    };\n  };\n  /**\r\n   * Get all data store dimension names.\r\n   * Theoretically a series data store is defined both by series and used dataset (if any).\r\n   * If some dimensions are omitted for performance reason in `this.dimensions`,\r\n   * the dimension name may not be auto-generated if user does not specify a dimension name.\r\n   * In this case, the dimension name is `null`/`undefined`.\r\n   */\n  DimensionUserOuput.prototype._getFullDimensionNames = function () {\n    if (!this._cachedDimNames) {\n      this._cachedDimNames = this._schema ? this._schema.makeOutputDimensionNames() : [];\n    }\n    return this._cachedDimNames;\n  };\n  return DimensionUserOuput;\n}();\n;\nexport function summarizeDimensions(data, schema) {\n  var summary = {};\n  var encode = summary.encode = {};\n  var notExtraCoordDimMap = createHashMap();\n  var defaultedLabel = [];\n  var defaultedTooltip = [];\n  var userOutputEncode = {};\n  each(data.dimensions, function (dimName) {\n    var dimItem = data.getDimensionInfo(dimName);\n    var coordDim = dimItem.coordDim;\n    if (coordDim) {\n      if (process.env.NODE_ENV !== 'production') {\n        assert(VISUAL_DIMENSIONS.get(coordDim) == null);\n      }\n      var coordDimIndex = dimItem.coordDimIndex;\n      getOrCreateEncodeArr(encode, coordDim)[coordDimIndex] = dimName;\n      if (!dimItem.isExtraCoord) {\n        notExtraCoordDimMap.set(coordDim, 1);\n        // Use the last coord dim (and label friendly) as default label,\n        // because when dataset is used, it is hard to guess which dimension\n        // can be value dimension. If both show x, y on label is not look good,\n        // and conventionally y axis is focused more.\n        if (mayLabelDimType(dimItem.type)) {\n          defaultedLabel[0] = dimName;\n        }\n        // User output encode do not contain generated coords.\n        // And it only has index. User can use index to retrieve value from the raw item array.\n        getOrCreateEncodeArr(userOutputEncode, coordDim)[coordDimIndex] = data.getDimensionIndex(dimItem.name);\n      }\n      if (dimItem.defaultTooltip) {\n        defaultedTooltip.push(dimName);\n      }\n    }\n    VISUAL_DIMENSIONS.each(function (v, otherDim) {\n      var encodeArr = getOrCreateEncodeArr(encode, otherDim);\n      var dimIndex = dimItem.otherDims[otherDim];\n      if (dimIndex != null && dimIndex !== false) {\n        encodeArr[dimIndex] = dimItem.name;\n      }\n    });\n  });\n  var dataDimsOnCoord = [];\n  var encodeFirstDimNotExtra = {};\n  notExtraCoordDimMap.each(function (v, coordDim) {\n    var dimArr = encode[coordDim];\n    encodeFirstDimNotExtra[coordDim] = dimArr[0];\n    // Not necessary to remove duplicate, because a data\n    // dim canot on more than one coordDim.\n    dataDimsOnCoord = dataDimsOnCoord.concat(dimArr);\n  });\n  summary.dataDimsOnCoord = dataDimsOnCoord;\n  summary.dataDimIndicesOnCoord = map(dataDimsOnCoord, function (dimName) {\n    return data.getDimensionInfo(dimName).storeDimIndex;\n  });\n  summary.encodeFirstDimNotExtra = encodeFirstDimNotExtra;\n  var encodeLabel = encode.label;\n  // FIXME `encode.label` is not recommended, because formatter cannot be set\n  // in this way. Use label.formatter instead. Maybe remove this approach someday.\n  if (encodeLabel && encodeLabel.length) {\n    defaultedLabel = encodeLabel.slice();\n  }\n  var encodeTooltip = encode.tooltip;\n  if (encodeTooltip && encodeTooltip.length) {\n    defaultedTooltip = encodeTooltip.slice();\n  } else if (!defaultedTooltip.length) {\n    defaultedTooltip = defaultedLabel.slice();\n  }\n  encode.defaultedLabel = defaultedLabel;\n  encode.defaultedTooltip = defaultedTooltip;\n  summary.userOutput = new DimensionUserOuput(userOutputEncode, schema);\n  return summary;\n}\nfunction getOrCreateEncodeArr(encode, dim) {\n  if (!encode.hasOwnProperty(dim)) {\n    encode[dim] = [];\n  }\n  return encode[dim];\n}\n// FIXME:TS should be type `AxisType`\nexport function getDimensionTypeByAxis(axisType) {\n  return axisType === 'category' ? 'ordinal' : axisType === 'time' ? 'time' : 'float';\n}\nfunction mayLabelDimType(dimType) {\n  // In most cases, ordinal and time do not suitable for label.\n  // Ordinal info can be displayed on axis. Time is too long.\n  return !(dimType === 'ordinal' || dimType === 'time');\n}\n// function findTheLastDimMayLabel(data) {\n//     // Get last value dim\n//     let dimensions = data.dimensions.slice();\n//     let valueType;\n//     let valueDim;\n//     while (dimensions.length && (\n//         valueDim = dimensions.pop(),\n//         valueType = data.getDimensionInfo(valueDim).type,\n//         valueType === 'ordinal' || valueType === 'time'\n//     )) {} // jshint ignore:line\n//     return valueDim;\n// }"], "names": [], "mappings": "AACA;;;;;;;;;;;;;;;;;AAiBA,GAGA;;CAEC,GAED;;;;;;;;;;;;;;;;;AAiBA;;;;AACA;AACA;;;AACA,IAAI,qBAAqB,WAAW,GAAE;IACpC,SAAS,mBAAmB,MAAM,EAAE,UAAU;QAC5C,IAAI,CAAC,OAAO,GAAG;QACf,IAAI,CAAC,OAAO,GAAG;IACjB;IACA,mBAAmB,SAAS,CAAC,GAAG,GAAG;QACjC,OAAO;YACL,uDAAuD;YACvD,gBAAgB,IAAI,CAAC,sBAAsB;YAC3C,QAAQ,IAAI,CAAC,OAAO;QACtB;IACF;IACA;;;;;;GAMC,GACD,mBAAmB,SAAS,CAAC,sBAAsB,GAAG;QACpD,IAAI,CAAC,IAAI,CAAC,eAAe,EAAE;YACzB,IAAI,CAAC,eAAe,GAAG,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC,OAAO,CAAC,wBAAwB,KAAK,EAAE;QACpF;QACA,OAAO,IAAI,CAAC,eAAe;IAC7B;IACA,OAAO;AACT;;AAEO,SAAS,oBAAoB,IAAI,EAAE,MAAM;IAC9C,IAAI,UAAU,CAAC;IACf,IAAI,SAAS,QAAQ,MAAM,GAAG,CAAC;IAC/B,IAAI,sBAAsB,CAAA,GAAA,8IAAA,CAAA,gBAAa,AAAD;IACtC,IAAI,iBAAiB,EAAE;IACvB,IAAI,mBAAmB,EAAE;IACzB,IAAI,mBAAmB,CAAC;IACxB,CAAA,GAAA,8IAAA,CAAA,OAAI,AAAD,EAAE,KAAK,UAAU,EAAE,SAAU,OAAO;QACrC,IAAI,UAAU,KAAK,gBAAgB,CAAC;QACpC,IAAI,WAAW,QAAQ,QAAQ;QAC/B,IAAI,UAAU;YACZ,wCAA2C;gBACzC,CAAA,GAAA,8IAAA,CAAA,SAAM,AAAD,EAAE,+IAAA,CAAA,oBAAiB,CAAC,GAAG,CAAC,aAAa;YAC5C;YACA,IAAI,gBAAgB,QAAQ,aAAa;YACzC,qBAAqB,QAAQ,SAAS,CAAC,cAAc,GAAG;YACxD,IAAI,CAAC,QAAQ,YAAY,EAAE;gBACzB,oBAAoB,GAAG,CAAC,UAAU;gBAClC,gEAAgE;gBAChE,oEAAoE;gBACpE,uEAAuE;gBACvE,6CAA6C;gBAC7C,IAAI,gBAAgB,QAAQ,IAAI,GAAG;oBACjC,cAAc,CAAC,EAAE,GAAG;gBACtB;gBACA,sDAAsD;gBACtD,uFAAuF;gBACvF,qBAAqB,kBAAkB,SAAS,CAAC,cAAc,GAAG,KAAK,iBAAiB,CAAC,QAAQ,IAAI;YACvG;YACA,IAAI,QAAQ,cAAc,EAAE;gBAC1B,iBAAiB,IAAI,CAAC;YACxB;QACF;QACA,+IAAA,CAAA,oBAAiB,CAAC,IAAI,CAAC,SAAU,CAAC,EAAE,QAAQ;YAC1C,IAAI,YAAY,qBAAqB,QAAQ;YAC7C,IAAI,WAAW,QAAQ,SAAS,CAAC,SAAS;YAC1C,IAAI,YAAY,QAAQ,aAAa,OAAO;gBAC1C,SAAS,CAAC,SAAS,GAAG,QAAQ,IAAI;YACpC;QACF;IACF;IACA,IAAI,kBAAkB,EAAE;IACxB,IAAI,yBAAyB,CAAC;IAC9B,oBAAoB,IAAI,CAAC,SAAU,CAAC,EAAE,QAAQ;QAC5C,IAAI,SAAS,MAAM,CAAC,SAAS;QAC7B,sBAAsB,CAAC,SAAS,GAAG,MAAM,CAAC,EAAE;QAC5C,oDAAoD;QACpD,uCAAuC;QACvC,kBAAkB,gBAAgB,MAAM,CAAC;IAC3C;IACA,QAAQ,eAAe,GAAG;IAC1B,QAAQ,qBAAqB,GAAG,CAAA,GAAA,8IAAA,CAAA,MAAG,AAAD,EAAE,iBAAiB,SAAU,OAAO;QACpE,OAAO,KAAK,gBAAgB,CAAC,SAAS,aAAa;IACrD;IACA,QAAQ,sBAAsB,GAAG;IACjC,IAAI,cAAc,OAAO,KAAK;IAC9B,2EAA2E;IAC3E,gFAAgF;IAChF,IAAI,eAAe,YAAY,MAAM,EAAE;QACrC,iBAAiB,YAAY,KAAK;IACpC;IACA,IAAI,gBAAgB,OAAO,OAAO;IAClC,IAAI,iBAAiB,cAAc,MAAM,EAAE;QACzC,mBAAmB,cAAc,KAAK;IACxC,OAAO,IAAI,CAAC,iBAAiB,MAAM,EAAE;QACnC,mBAAmB,eAAe,KAAK;IACzC;IACA,OAAO,cAAc,GAAG;IACxB,OAAO,gBAAgB,GAAG;IAC1B,QAAQ,UAAU,GAAG,IAAI,mBAAmB,kBAAkB;IAC9D,OAAO;AACT;AACA,SAAS,qBAAqB,MAAM,EAAE,GAAG;IACvC,IAAI,CAAC,OAAO,cAAc,CAAC,MAAM;QAC/B,MAAM,CAAC,IAAI,GAAG,EAAE;IAClB;IACA,OAAO,MAAM,CAAC,IAAI;AACpB;AAEO,SAAS,uBAAuB,QAAQ;IAC7C,OAAO,aAAa,aAAa,YAAY,aAAa,SAAS,SAAS;AAC9E;AACA,SAAS,gBAAgB,OAAO;IAC9B,6DAA6D;IAC7D,2DAA2D;IAC3D,OAAO,CAAC,CAAC,YAAY,aAAa,YAAY,MAAM;AACtD,EACA,0CAA0C;CAC1C,4BAA4B;CAC5B,gDAAgD;CAChD,qBAAqB;CACrB,oBAAoB;CACpB,oCAAoC;CACpC,uCAAuC;CACvC,4DAA4D;CAC5D,0DAA0D;CAC1D,kCAAkC;CAClC,uBAAuB;CACvB,IAAI", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 3469, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/tindahan/sari-sari-admin/node_modules/echarts/lib/data/SeriesDimensionDefine.js"], "sourcesContent": ["\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\r\n* Licensed to the Apache Software Foundation (ASF) under one\r\n* or more contributor license agreements.  See the NOTICE file\r\n* distributed with this work for additional information\r\n* regarding copyright ownership.  The ASF licenses this file\r\n* to you under the Apache License, Version 2.0 (the\r\n* \"License\"); you may not use this file except in compliance\r\n* with the License.  You may obtain a copy of the License at\r\n*\r\n*   http://www.apache.org/licenses/LICENSE-2.0\r\n*\r\n* Unless required by applicable law or agreed to in writing,\r\n* software distributed under the License is distributed on an\r\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\r\n* KIND, either express or implied.  See the License for the\r\n* specific language governing permissions and limitations\r\n* under the License.\r\n*/\nimport * as zrUtil from 'zrender/lib/core/util.js';\nvar SeriesDimensionDefine = /** @class */function () {\n  /**\r\n   * @param opt All of the fields will be shallow copied.\r\n   */\n  function SeriesDimensionDefine(opt) {\n    /**\r\n     * The format of `otherDims` is:\r\n     * ```js\r\n     * {\r\n     *     tooltip?: number\r\n     *     label?: number\r\n     *     itemName?: number\r\n     *     seriesName?: number\r\n     * }\r\n     * ```\r\n     *\r\n     * A `series.encode` can specified these fields:\r\n     * ```js\r\n     * encode: {\r\n     *     // \"3, 1, 5\" is the index of data dimension.\r\n     *     tooltip: [3, 1, 5],\r\n     *     label: [0, 3],\r\n     *     ...\r\n     * }\r\n     * ```\r\n     * `otherDims` is the parse result of the `series.encode` above, like:\r\n     * ```js\r\n     * // Suppose the index of this data dimension is `3`.\r\n     * this.otherDims = {\r\n     *     // `3` is at the index `0` of the `encode.tooltip`\r\n     *     tooltip: 0,\r\n     *     // `3` is at the index `1` of the `encode.label`\r\n     *     label: 1\r\n     * };\r\n     * ```\r\n     *\r\n     * This prop should never be `null`/`undefined` after initialized.\r\n     */\n    this.otherDims = {};\n    if (opt != null) {\n      zrUtil.extend(this, opt);\n    }\n  }\n  return SeriesDimensionDefine;\n}();\n;\nexport default SeriesDimensionDefine;"], "names": [], "mappings": "AACA;;;;;;;;;;;;;;;;;AAiBA,GAGA;;CAEC,GAED;;;;;;;;;;;;;;;;;AAiBA;;;AACA;;AACA,IAAI,wBAAwB,WAAW,GAAE;IACvC;;GAEC,GACD,SAAS,sBAAsB,GAAG;QAChC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;KAgCC,GACD,IAAI,CAAC,SAAS,GAAG,CAAC;QAClB,IAAI,OAAO,MAAM;YACf,CAAA,GAAA,8IAAA,CAAA,SAAa,AAAD,EAAE,IAAI,EAAE;QACtB;IACF;IACA,OAAO;AACT;;uCAEe", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 3561, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/tindahan/sari-sari-admin/node_modules/echarts/lib/data/helper/SeriesDataSchema.js"], "sourcesContent": ["\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\r\n* Licensed to the Apache Software Foundation (ASF) under one\r\n* or more contributor license agreements.  See the NOTICE file\r\n* distributed with this work for additional information\r\n* regarding copyright ownership.  The ASF licenses this file\r\n* to you under the Apache License, Version 2.0 (the\r\n* \"License\"); you may not use this file except in compliance\r\n* with the License.  You may obtain a copy of the License at\r\n*\r\n*   http://www.apache.org/licenses/LICENSE-2.0\r\n*\r\n* Unless required by applicable law or agreed to in writing,\r\n* software distributed under the License is distributed on an\r\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\r\n* KIND, either express or implied.  See the License for the\r\n* specific language governing permissions and limitations\r\n* under the License.\r\n*/\nimport { createHashMap, isObject, retrieve2 } from 'zrender/lib/core/util.js';\nimport { makeInner } from '../../util/model.js';\nimport { shouldRetrieveDataByName } from '../Source.js';\nvar inner = makeInner();\nvar dimTypeShort = {\n  float: 'f',\n  int: 'i',\n  ordinal: 'o',\n  number: 'n',\n  time: 't'\n};\n/**\r\n * Represents the dimension requirement of a series.\r\n *\r\n * NOTICE:\r\n * When there are too many dimensions in dataset and many series, only the used dimensions\r\n * (i.e., used by coord sys and declared in `series.encode`) are add to `dimensionDefineList`.\r\n * But users may query data by other unused dimension names.\r\n * In this case, users can only query data if and only if they have defined dimension names\r\n * via ec option, so we provide `getDimensionIndexFromSource`, which only query them from\r\n * `source` dimensions.\r\n */\nvar SeriesDataSchema = /** @class */function () {\n  function SeriesDataSchema(opt) {\n    this.dimensions = opt.dimensions;\n    this._dimOmitted = opt.dimensionOmitted;\n    this.source = opt.source;\n    this._fullDimCount = opt.fullDimensionCount;\n    this._updateDimOmitted(opt.dimensionOmitted);\n  }\n  SeriesDataSchema.prototype.isDimensionOmitted = function () {\n    return this._dimOmitted;\n  };\n  SeriesDataSchema.prototype._updateDimOmitted = function (dimensionOmitted) {\n    this._dimOmitted = dimensionOmitted;\n    if (!dimensionOmitted) {\n      return;\n    }\n    if (!this._dimNameMap) {\n      this._dimNameMap = ensureSourceDimNameMap(this.source);\n    }\n  };\n  /**\r\n   * @caution Can only be used when `dimensionOmitted: true`.\r\n   *\r\n   * Get index by user defined dimension name (i.e., not internal generate name).\r\n   * That is, get index from `dimensionsDefine`.\r\n   * If no `dimensionsDefine`, or no name get, return -1.\r\n   */\n  SeriesDataSchema.prototype.getSourceDimensionIndex = function (dimName) {\n    return retrieve2(this._dimNameMap.get(dimName), -1);\n  };\n  /**\r\n   * @caution Can only be used when `dimensionOmitted: true`.\r\n   *\r\n   * Notice: may return `null`/`undefined` if user not specify dimension names.\r\n   */\n  SeriesDataSchema.prototype.getSourceDimension = function (dimIndex) {\n    var dimensionsDefine = this.source.dimensionsDefine;\n    if (dimensionsDefine) {\n      return dimensionsDefine[dimIndex];\n    }\n  };\n  SeriesDataSchema.prototype.makeStoreSchema = function () {\n    var dimCount = this._fullDimCount;\n    var willRetrieveDataByName = shouldRetrieveDataByName(this.source);\n    var makeHashStrict = !shouldOmitUnusedDimensions(dimCount);\n    // If source don't have dimensions or series don't omit unsed dimensions.\n    // Generate from seriesDimList directly\n    var dimHash = '';\n    var dims = [];\n    for (var fullDimIdx = 0, seriesDimIdx = 0; fullDimIdx < dimCount; fullDimIdx++) {\n      var property = void 0;\n      var type = void 0;\n      var ordinalMeta = void 0;\n      var seriesDimDef = this.dimensions[seriesDimIdx];\n      // The list has been sorted by `storeDimIndex` asc.\n      if (seriesDimDef && seriesDimDef.storeDimIndex === fullDimIdx) {\n        property = willRetrieveDataByName ? seriesDimDef.name : null;\n        type = seriesDimDef.type;\n        ordinalMeta = seriesDimDef.ordinalMeta;\n        seriesDimIdx++;\n      } else {\n        var sourceDimDef = this.getSourceDimension(fullDimIdx);\n        if (sourceDimDef) {\n          property = willRetrieveDataByName ? sourceDimDef.name : null;\n          type = sourceDimDef.type;\n        }\n      }\n      dims.push({\n        property: property,\n        type: type,\n        ordinalMeta: ordinalMeta\n      });\n      // If retrieving data by index,\n      //   use <index, type, ordinalMeta> to determine whether data can be shared.\n      //   (Because in this case there might be no dimension name defined in dataset, but indices always exists).\n      //   (Indices are always 0, 1, 2, ..., so we can ignore them to shorten the hash).\n      // Otherwise if retrieving data by property name (like `data: [{aa: 123, bb: 765}, ...]`),\n      //   use <property, type, ordinalMeta> in hash.\n      if (willRetrieveDataByName && property != null\n      // For data stack, we have make sure each series has its own dim on this store.\n      // So we do not add property to hash to make sure they can share this store.\n      && (!seriesDimDef || !seriesDimDef.isCalculationCoord)) {\n        dimHash += makeHashStrict\n        // Use escape character '`' in case that property name contains '$'.\n        ? property.replace(/\\`/g, '`1').replace(/\\$/g, '`2')\n        // For better performance, when there are large dimensions, tolerant this defects that hardly meet.\n        : property;\n      }\n      dimHash += '$';\n      dimHash += dimTypeShort[type] || 'f';\n      if (ordinalMeta) {\n        dimHash += ordinalMeta.uid;\n      }\n      dimHash += '$';\n    }\n    // Source from endpoint(usually series) will be read differently\n    // when seriesLayoutBy or startIndex(which is affected by sourceHeader) are different.\n    // So we use this three props as key.\n    var source = this.source;\n    var hash = [source.seriesLayoutBy, source.startIndex, dimHash].join('$$');\n    return {\n      dimensions: dims,\n      hash: hash\n    };\n  };\n  SeriesDataSchema.prototype.makeOutputDimensionNames = function () {\n    var result = [];\n    for (var fullDimIdx = 0, seriesDimIdx = 0; fullDimIdx < this._fullDimCount; fullDimIdx++) {\n      var name_1 = void 0;\n      var seriesDimDef = this.dimensions[seriesDimIdx];\n      // The list has been sorted by `storeDimIndex` asc.\n      if (seriesDimDef && seriesDimDef.storeDimIndex === fullDimIdx) {\n        if (!seriesDimDef.isCalculationCoord) {\n          name_1 = seriesDimDef.name;\n        }\n        seriesDimIdx++;\n      } else {\n        var sourceDimDef = this.getSourceDimension(fullDimIdx);\n        if (sourceDimDef) {\n          name_1 = sourceDimDef.name;\n        }\n      }\n      result.push(name_1);\n    }\n    return result;\n  };\n  SeriesDataSchema.prototype.appendCalculationDimension = function (dimDef) {\n    this.dimensions.push(dimDef);\n    dimDef.isCalculationCoord = true;\n    this._fullDimCount++;\n    // If append dimension on a data store, consider the store\n    // might be shared by different series, series dimensions not\n    // really map to store dimensions.\n    this._updateDimOmitted(true);\n  };\n  return SeriesDataSchema;\n}();\nexport { SeriesDataSchema };\nexport function isSeriesDataSchema(schema) {\n  return schema instanceof SeriesDataSchema;\n}\nexport function createDimNameMap(dimsDef) {\n  var dataDimNameMap = createHashMap();\n  for (var i = 0; i < (dimsDef || []).length; i++) {\n    var dimDefItemRaw = dimsDef[i];\n    var userDimName = isObject(dimDefItemRaw) ? dimDefItemRaw.name : dimDefItemRaw;\n    if (userDimName != null && dataDimNameMap.get(userDimName) == null) {\n      dataDimNameMap.set(userDimName, i);\n    }\n  }\n  return dataDimNameMap;\n}\nexport function ensureSourceDimNameMap(source) {\n  var innerSource = inner(source);\n  return innerSource.dimNameMap || (innerSource.dimNameMap = createDimNameMap(source.dimensionsDefine));\n}\nexport function shouldOmitUnusedDimensions(dimCount) {\n  return dimCount > 30;\n}"], "names": [], "mappings": "AACA;;;;;;;;;;;;;;;;;AAiBA,GAGA;;CAEC,GAED;;;;;;;;;;;;;;;;;AAiBA;;;;;;;AACA;AACA;AACA;;;;AACA,IAAI,QAAQ,CAAA,GAAA,+IAAA,CAAA,YAAS,AAAD;AACpB,IAAI,eAAe;IACjB,OAAO;IACP,KAAK;IACL,SAAS;IACT,QAAQ;IACR,MAAM;AACR;AACA;;;;;;;;;;CAUC,GACD,IAAI,mBAAmB,WAAW,GAAE;IAClC,SAAS,iBAAiB,GAAG;QAC3B,IAAI,CAAC,UAAU,GAAG,IAAI,UAAU;QAChC,IAAI,CAAC,WAAW,GAAG,IAAI,gBAAgB;QACvC,IAAI,CAAC,MAAM,GAAG,IAAI,MAAM;QACxB,IAAI,CAAC,aAAa,GAAG,IAAI,kBAAkB;QAC3C,IAAI,CAAC,iBAAiB,CAAC,IAAI,gBAAgB;IAC7C;IACA,iBAAiB,SAAS,CAAC,kBAAkB,GAAG;QAC9C,OAAO,IAAI,CAAC,WAAW;IACzB;IACA,iBAAiB,SAAS,CAAC,iBAAiB,GAAG,SAAU,gBAAgB;QACvE,IAAI,CAAC,WAAW,GAAG;QACnB,IAAI,CAAC,kBAAkB;YACrB;QACF;QACA,IAAI,CAAC,IAAI,CAAC,WAAW,EAAE;YACrB,IAAI,CAAC,WAAW,GAAG,uBAAuB,IAAI,CAAC,MAAM;QACvD;IACF;IACA;;;;;;GAMC,GACD,iBAAiB,SAAS,CAAC,uBAAuB,GAAG,SAAU,OAAO;QACpE,OAAO,CAAA,GAAA,8IAAA,CAAA,YAAS,AAAD,EAAE,IAAI,CAAC,WAAW,CAAC,GAAG,CAAC,UAAU,CAAC;IACnD;IACA;;;;GAIC,GACD,iBAAiB,SAAS,CAAC,kBAAkB,GAAG,SAAU,QAAQ;QAChE,IAAI,mBAAmB,IAAI,CAAC,MAAM,CAAC,gBAAgB;QACnD,IAAI,kBAAkB;YACpB,OAAO,gBAAgB,CAAC,SAAS;QACnC;IACF;IACA,iBAAiB,SAAS,CAAC,eAAe,GAAG;QAC3C,IAAI,WAAW,IAAI,CAAC,aAAa;QACjC,IAAI,yBAAyB,CAAA,GAAA,gJAAA,CAAA,2BAAwB,AAAD,EAAE,IAAI,CAAC,MAAM;QACjE,IAAI,iBAAiB,CAAC,2BAA2B;QACjD,yEAAyE;QACzE,uCAAuC;QACvC,IAAI,UAAU;QACd,IAAI,OAAO,EAAE;QACb,IAAK,IAAI,aAAa,GAAG,eAAe,GAAG,aAAa,UAAU,aAAc;YAC9E,IAAI,WAAW,KAAK;YACpB,IAAI,OAAO,KAAK;YAChB,IAAI,cAAc,KAAK;YACvB,IAAI,eAAe,IAAI,CAAC,UAAU,CAAC,aAAa;YAChD,mDAAmD;YACnD,IAAI,gBAAgB,aAAa,aAAa,KAAK,YAAY;gBAC7D,WAAW,yBAAyB,aAAa,IAAI,GAAG;gBACxD,OAAO,aAAa,IAAI;gBACxB,cAAc,aAAa,WAAW;gBACtC;YACF,OAAO;gBACL,IAAI,eAAe,IAAI,CAAC,kBAAkB,CAAC;gBAC3C,IAAI,cAAc;oBAChB,WAAW,yBAAyB,aAAa,IAAI,GAAG;oBACxD,OAAO,aAAa,IAAI;gBAC1B;YACF;YACA,KAAK,IAAI,CAAC;gBACR,UAAU;gBACV,MAAM;gBACN,aAAa;YACf;YACA,+BAA+B;YAC/B,4EAA4E;YAC5E,2GAA2G;YAC3G,kFAAkF;YAClF,0FAA0F;YAC1F,+CAA+C;YAC/C,IAAI,0BAA0B,YAAY,QAGvC,CAAC,CAAC,gBAAgB,CAAC,aAAa,kBAAkB,GAAG;gBACtD,WAAW,iBAET,SAAS,OAAO,CAAC,OAAO,MAAM,OAAO,CAAC,OAAO,QAE7C;YACJ;YACA,WAAW;YACX,WAAW,YAAY,CAAC,KAAK,IAAI;YACjC,IAAI,aAAa;gBACf,WAAW,YAAY,GAAG;YAC5B;YACA,WAAW;QACb;QACA,gEAAgE;QAChE,sFAAsF;QACtF,qCAAqC;QACrC,IAAI,SAAS,IAAI,CAAC,MAAM;QACxB,IAAI,OAAO;YAAC,OAAO,cAAc;YAAE,OAAO,UAAU;YAAE;SAAQ,CAAC,IAAI,CAAC;QACpE,OAAO;YACL,YAAY;YACZ,MAAM;QACR;IACF;IACA,iBAAiB,SAAS,CAAC,wBAAwB,GAAG;QACpD,IAAI,SAAS,EAAE;QACf,IAAK,IAAI,aAAa,GAAG,eAAe,GAAG,aAAa,IAAI,CAAC,aAAa,EAAE,aAAc;YACxF,IAAI,SAAS,KAAK;YAClB,IAAI,eAAe,IAAI,CAAC,UAAU,CAAC,aAAa;YAChD,mDAAmD;YACnD,IAAI,gBAAgB,aAAa,aAAa,KAAK,YAAY;gBAC7D,IAAI,CAAC,aAAa,kBAAkB,EAAE;oBACpC,SAAS,aAAa,IAAI;gBAC5B;gBACA;YACF,OAAO;gBACL,IAAI,eAAe,IAAI,CAAC,kBAAkB,CAAC;gBAC3C,IAAI,cAAc;oBAChB,SAAS,aAAa,IAAI;gBAC5B;YACF;YACA,OAAO,IAAI,CAAC;QACd;QACA,OAAO;IACT;IACA,iBAAiB,SAAS,CAAC,0BAA0B,GAAG,SAAU,MAAM;QACtE,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC;QACrB,OAAO,kBAAkB,GAAG;QAC5B,IAAI,CAAC,aAAa;QAClB,0DAA0D;QAC1D,6DAA6D;QAC7D,kCAAkC;QAClC,IAAI,CAAC,iBAAiB,CAAC;IACzB;IACA,OAAO;AACT;;AAEO,SAAS,mBAAmB,MAAM;IACvC,OAAO,kBAAkB;AAC3B;AACO,SAAS,iBAAiB,OAAO;IACtC,IAAI,iBAAiB,CAAA,GAAA,8IAAA,CAAA,gBAAa,AAAD;IACjC,IAAK,IAAI,IAAI,GAAG,IAAI,CAAC,WAAW,EAAE,EAAE,MAAM,EAAE,IAAK;QAC/C,IAAI,gBAAgB,OAAO,CAAC,EAAE;QAC9B,IAAI,cAAc,CAAA,GAAA,8IAAA,CAAA,WAAQ,AAAD,EAAE,iBAAiB,cAAc,IAAI,GAAG;QACjE,IAAI,eAAe,QAAQ,eAAe,GAAG,CAAC,gBAAgB,MAAM;YAClE,eAAe,GAAG,CAAC,aAAa;QAClC;IACF;IACA,OAAO;AACT;AACO,SAAS,uBAAuB,MAAM;IAC3C,IAAI,cAAc,MAAM;IACxB,OAAO,YAAY,UAAU,IAAI,CAAC,YAAY,UAAU,GAAG,iBAAiB,OAAO,gBAAgB,CAAC;AACtG;AACO,SAAS,2BAA2B,QAAQ;IACjD,OAAO,WAAW;AACpB", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 3788, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/tindahan/sari-sari-admin/node_modules/echarts/lib/data/SeriesData.js"], "sourcesContent": ["\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\r\n* Licensed to the Apache Software Foundation (ASF) under one\r\n* or more contributor license agreements.  See the NOTICE file\r\n* distributed with this work for additional information\r\n* regarding copyright ownership.  The ASF licenses this file\r\n* to you under the Apache License, Version 2.0 (the\r\n* \"License\"); you may not use this file except in compliance\r\n* with the License.  You may obtain a copy of the License at\r\n*\r\n*   http://www.apache.org/licenses/LICENSE-2.0\r\n*\r\n* Unless required by applicable law or agreed to in writing,\r\n* software distributed under the License is distributed on an\r\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\r\n* KIND, either express or implied.  See the License for the\r\n* specific language governing permissions and limitations\r\n* under the License.\r\n*/\n/* global Int32Array */\nimport * as zrUtil from 'zrender/lib/core/util.js';\nimport Model from '../model/Model.js';\nimport DataDiffer from './DataDiffer.js';\nimport { DefaultDataProvider } from './helper/dataProvider.js';\nimport { summarizeDimensions } from './helper/dimensionHelper.js';\nimport SeriesDimensionDefine from './SeriesDimensionDefine.js';\nimport { SOURCE_FORMAT_TYPED_ARRAY, SOURCE_FORMAT_ORIGINAL } from '../util/types.js';\nimport { convertOptionIdName, isDataItemOption } from '../util/model.js';\nimport { setCommonECData } from '../util/innerStore.js';\nimport { isSourceInstance } from './Source.js';\nimport DataStore from './DataStore.js';\nimport { isSeriesDataSchema } from './helper/SeriesDataSchema.js';\nvar isObject = zrUtil.isObject;\nvar map = zrUtil.map;\nvar CtorInt32Array = typeof Int32Array === 'undefined' ? Array : Int32Array;\n// Use prefix to avoid index to be the same as otherIdList[idx],\n// which will cause weird update animation.\nvar ID_PREFIX = 'e\\0\\0';\nvar INDEX_NOT_FOUND = -1;\n// type SeriesDimensionIndex = DimensionIndex;\nvar TRANSFERABLE_PROPERTIES = ['hasItemOption', '_nameList', '_idList', '_invertedIndicesMap', '_dimSummary', 'userOutput', '_rawData', '_dimValueGetter', '_nameDimIdx', '_idDimIdx', '_nameRepeatCount'];\nvar CLONE_PROPERTIES = ['_approximateExtent'];\n// -----------------------------\n// Internal method declarations:\n// -----------------------------\nvar prepareInvertedIndex;\nvar getId;\nvar getIdNameFromStore;\nvar normalizeDimensions;\nvar transferProperties;\nvar cloneListForMapAndSample;\nvar makeIdFromName;\nvar SeriesData = /** @class */function () {\n  /**\r\n   * @param dimensionsInput.dimensions\r\n   *        For example, ['someDimName', {name: 'someDimName', type: 'someDimType'}, ...].\r\n   *        Dimensions should be concrete names like x, y, z, lng, lat, angle, radius\r\n   */\n  function SeriesData(dimensionsInput, hostModel) {\n    this.type = 'list';\n    this._dimOmitted = false;\n    this._nameList = [];\n    this._idList = [];\n    // Models of data option is stored sparse for optimizing memory cost\n    // Never used yet (not used yet).\n    // private _optionModels: Model[] = [];\n    // Global visual properties after visual coding\n    this._visual = {};\n    // Global layout properties.\n    this._layout = {};\n    // Item visual properties after visual coding\n    this._itemVisuals = [];\n    // Item layout properties after layout\n    this._itemLayouts = [];\n    // Graphic elements\n    this._graphicEls = [];\n    // key: dim, value: extent\n    this._approximateExtent = {};\n    this._calculationInfo = {};\n    // Having detected that there is data item is non primitive type\n    // (in type `OptionDataItemObject`).\n    // Like `data: [ { value: xx, itemStyle: {...} }, ...]`\n    // At present it only happen in `SOURCE_FORMAT_ORIGINAL`.\n    this.hasItemOption = false;\n    // Methods that create a new list based on this list should be listed here.\n    // Notice that those method should `RETURN` the new list.\n    this.TRANSFERABLE_METHODS = ['cloneShallow', 'downSample', 'minmaxDownSample', 'lttbDownSample', 'map'];\n    // Methods that change indices of this list should be listed here.\n    this.CHANGABLE_METHODS = ['filterSelf', 'selectRange'];\n    this.DOWNSAMPLE_METHODS = ['downSample', 'minmaxDownSample', 'lttbDownSample'];\n    var dimensions;\n    var assignStoreDimIdx = false;\n    if (isSeriesDataSchema(dimensionsInput)) {\n      dimensions = dimensionsInput.dimensions;\n      this._dimOmitted = dimensionsInput.isDimensionOmitted();\n      this._schema = dimensionsInput;\n    } else {\n      assignStoreDimIdx = true;\n      dimensions = dimensionsInput;\n    }\n    dimensions = dimensions || ['x', 'y'];\n    var dimensionInfos = {};\n    var dimensionNames = [];\n    var invertedIndicesMap = {};\n    var needsHasOwn = false;\n    var emptyObj = {};\n    for (var i = 0; i < dimensions.length; i++) {\n      // Use the original dimensions[i], where other flag props may exists.\n      var dimInfoInput = dimensions[i];\n      var dimensionInfo = zrUtil.isString(dimInfoInput) ? new SeriesDimensionDefine({\n        name: dimInfoInput\n      }) : !(dimInfoInput instanceof SeriesDimensionDefine) ? new SeriesDimensionDefine(dimInfoInput) : dimInfoInput;\n      var dimensionName = dimensionInfo.name;\n      dimensionInfo.type = dimensionInfo.type || 'float';\n      if (!dimensionInfo.coordDim) {\n        dimensionInfo.coordDim = dimensionName;\n        dimensionInfo.coordDimIndex = 0;\n      }\n      var otherDims = dimensionInfo.otherDims = dimensionInfo.otherDims || {};\n      dimensionNames.push(dimensionName);\n      dimensionInfos[dimensionName] = dimensionInfo;\n      if (emptyObj[dimensionName] != null) {\n        needsHasOwn = true;\n      }\n      if (dimensionInfo.createInvertedIndices) {\n        invertedIndicesMap[dimensionName] = [];\n      }\n      if (otherDims.itemName === 0) {\n        this._nameDimIdx = i;\n      }\n      if (otherDims.itemId === 0) {\n        this._idDimIdx = i;\n      }\n      if (process.env.NODE_ENV !== 'production') {\n        zrUtil.assert(assignStoreDimIdx || dimensionInfo.storeDimIndex >= 0);\n      }\n      if (assignStoreDimIdx) {\n        dimensionInfo.storeDimIndex = i;\n      }\n    }\n    this.dimensions = dimensionNames;\n    this._dimInfos = dimensionInfos;\n    this._initGetDimensionInfo(needsHasOwn);\n    this.hostModel = hostModel;\n    this._invertedIndicesMap = invertedIndicesMap;\n    if (this._dimOmitted) {\n      var dimIdxToName_1 = this._dimIdxToName = zrUtil.createHashMap();\n      zrUtil.each(dimensionNames, function (dimName) {\n        dimIdxToName_1.set(dimensionInfos[dimName].storeDimIndex, dimName);\n      });\n    }\n  }\n  /**\r\n   *\r\n   * Get concrete dimension name by dimension name or dimension index.\r\n   * If input a dimension name, do not validate whether the dimension name exits.\r\n   *\r\n   * @caution\r\n   * @param dim Must make sure the dimension is `SeriesDimensionLoose`.\r\n   * Because only those dimensions will have auto-generated dimension names if not\r\n   * have a user-specified name, and other dimensions will get a return of null/undefined.\r\n   *\r\n   * @notice Because of this reason, should better use `getDimensionIndex` instead, for examples:\r\n   * ```js\r\n   * const val = data.getStore().get(data.getDimensionIndex(dim), dataIdx);\r\n   * ```\r\n   *\r\n   * @return Concrete dim name.\r\n   */\n  SeriesData.prototype.getDimension = function (dim) {\n    var dimIdx = this._recognizeDimIndex(dim);\n    if (dimIdx == null) {\n      return dim;\n    }\n    dimIdx = dim;\n    if (!this._dimOmitted) {\n      return this.dimensions[dimIdx];\n    }\n    // Retrieve from series dimension definition because it probably contains\n    // generated dimension name (like 'x', 'y').\n    var dimName = this._dimIdxToName.get(dimIdx);\n    if (dimName != null) {\n      return dimName;\n    }\n    var sourceDimDef = this._schema.getSourceDimension(dimIdx);\n    if (sourceDimDef) {\n      return sourceDimDef.name;\n    }\n  };\n  /**\r\n   * Get dimension index in data store. Return -1 if not found.\r\n   * Can be used to index value from getRawValue.\r\n   */\n  SeriesData.prototype.getDimensionIndex = function (dim) {\n    var dimIdx = this._recognizeDimIndex(dim);\n    if (dimIdx != null) {\n      return dimIdx;\n    }\n    if (dim == null) {\n      return -1;\n    }\n    var dimInfo = this._getDimInfo(dim);\n    return dimInfo ? dimInfo.storeDimIndex : this._dimOmitted ? this._schema.getSourceDimensionIndex(dim) : -1;\n  };\n  /**\r\n   * The meanings of the input parameter `dim`:\r\n   *\r\n   * + If dim is a number (e.g., `1`), it means the index of the dimension.\r\n   *   For example, `getDimension(0)` will return 'x' or 'lng' or 'radius'.\r\n   * + If dim is a number-like string (e.g., `\"1\"`):\r\n   *     + If there is the same concrete dim name defined in `series.dimensions` or `dataset.dimensions`,\r\n   *        it means that concrete name.\r\n   *     + If not, it will be converted to a number, which means the index of the dimension.\r\n   *        (why? because of the backward compatibility. We have been tolerating number-like string in\r\n   *        dimension setting, although now it seems that it is not a good idea.)\r\n   *     For example, `visualMap[i].dimension: \"1\"` is the same meaning as `visualMap[i].dimension: 1`,\r\n   *     if no dimension name is defined as `\"1\"`.\r\n   * + If dim is a not-number-like string, it means the concrete dim name.\r\n   *   For example, it can be be default name `\"x\"`, `\"y\"`, `\"z\"`, `\"lng\"`, `\"lat\"`, `\"angle\"`, `\"radius\"`,\r\n   *   or customized in `dimensions` property of option like `\"age\"`.\r\n   *\r\n   * @return recognized `DimensionIndex`. Otherwise return null/undefined (means that dim is `DimensionName`).\r\n   */\n  SeriesData.prototype._recognizeDimIndex = function (dim) {\n    if (zrUtil.isNumber(dim)\n    // If being a number-like string but not being defined as a dimension name.\n    || dim != null && !isNaN(dim) && !this._getDimInfo(dim) && (!this._dimOmitted || this._schema.getSourceDimensionIndex(dim) < 0)) {\n      return +dim;\n    }\n  };\n  SeriesData.prototype._getStoreDimIndex = function (dim) {\n    var dimIdx = this.getDimensionIndex(dim);\n    if (process.env.NODE_ENV !== 'production') {\n      if (dimIdx == null) {\n        throw new Error('Unknown dimension ' + dim);\n      }\n    }\n    return dimIdx;\n  };\n  /**\r\n   * Get type and calculation info of particular dimension\r\n   * @param dim\r\n   *        Dimension can be concrete names like x, y, z, lng, lat, angle, radius\r\n   *        Or a ordinal number. For example getDimensionInfo(0) will return 'x' or 'lng' or 'radius'\r\n   */\n  SeriesData.prototype.getDimensionInfo = function (dim) {\n    // Do not clone, because there may be categories in dimInfo.\n    return this._getDimInfo(this.getDimension(dim));\n  };\n  SeriesData.prototype._initGetDimensionInfo = function (needsHasOwn) {\n    var dimensionInfos = this._dimInfos;\n    this._getDimInfo = needsHasOwn ? function (dimName) {\n      return dimensionInfos.hasOwnProperty(dimName) ? dimensionInfos[dimName] : undefined;\n    } : function (dimName) {\n      return dimensionInfos[dimName];\n    };\n  };\n  /**\r\n   * concrete dimension name list on coord.\r\n   */\n  SeriesData.prototype.getDimensionsOnCoord = function () {\n    return this._dimSummary.dataDimsOnCoord.slice();\n  };\n  SeriesData.prototype.mapDimension = function (coordDim, idx) {\n    var dimensionsSummary = this._dimSummary;\n    if (idx == null) {\n      return dimensionsSummary.encodeFirstDimNotExtra[coordDim];\n    }\n    var dims = dimensionsSummary.encode[coordDim];\n    return dims ? dims[idx] : null;\n  };\n  SeriesData.prototype.mapDimensionsAll = function (coordDim) {\n    var dimensionsSummary = this._dimSummary;\n    var dims = dimensionsSummary.encode[coordDim];\n    return (dims || []).slice();\n  };\n  SeriesData.prototype.getStore = function () {\n    return this._store;\n  };\n  /**\r\n   * Initialize from data\r\n   * @param data source or data or data store.\r\n   * @param nameList The name of a datum is used on data diff and\r\n   *        default label/tooltip.\r\n   *        A name can be specified in encode.itemName,\r\n   *        or dataItem.name (only for series option data),\r\n   *        or provided in nameList from outside.\r\n   */\n  SeriesData.prototype.initData = function (data, nameList, dimValueGetter) {\n    var _this = this;\n    var store;\n    if (data instanceof DataStore) {\n      store = data;\n    }\n    if (!store) {\n      var dimensions = this.dimensions;\n      var provider = isSourceInstance(data) || zrUtil.isArrayLike(data) ? new DefaultDataProvider(data, dimensions.length) : data;\n      store = new DataStore();\n      var dimensionInfos = map(dimensions, function (dimName) {\n        return {\n          type: _this._dimInfos[dimName].type,\n          property: dimName\n        };\n      });\n      store.initData(provider, dimensionInfos, dimValueGetter);\n    }\n    this._store = store;\n    // Reset\n    this._nameList = (nameList || []).slice();\n    this._idList = [];\n    this._nameRepeatCount = {};\n    this._doInit(0, store.count());\n    // Cache summary info for fast visit. See \"dimensionHelper\".\n    // Needs to be initialized after store is prepared.\n    this._dimSummary = summarizeDimensions(this, this._schema);\n    this.userOutput = this._dimSummary.userOutput;\n  };\n  /**\r\n   * Caution: Can be only called on raw data (before `this._indices` created).\r\n   */\n  SeriesData.prototype.appendData = function (data) {\n    var range = this._store.appendData(data);\n    this._doInit(range[0], range[1]);\n  };\n  /**\r\n   * Caution: Can be only called on raw data (before `this._indices` created).\r\n   * This method does not modify `rawData` (`dataProvider`), but only\r\n   * add values to store.\r\n   *\r\n   * The final count will be increased by `Math.max(values.length, names.length)`.\r\n   *\r\n   * @param values That is the SourceType: 'arrayRows', like\r\n   *        [\r\n   *            [12, 33, 44],\r\n   *            [NaN, 43, 1],\r\n   *            ['-', 'asdf', 0]\r\n   *        ]\r\n   *        Each item is exactly corresponding to a dimension.\r\n   */\n  SeriesData.prototype.appendValues = function (values, names) {\n    var _a = this._store.appendValues(values, names && names.length),\n      start = _a.start,\n      end = _a.end;\n    var shouldMakeIdFromName = this._shouldMakeIdFromName();\n    this._updateOrdinalMeta();\n    if (names) {\n      for (var idx = start; idx < end; idx++) {\n        var sourceIdx = idx - start;\n        this._nameList[idx] = names[sourceIdx];\n        if (shouldMakeIdFromName) {\n          makeIdFromName(this, idx);\n        }\n      }\n    }\n  };\n  SeriesData.prototype._updateOrdinalMeta = function () {\n    var store = this._store;\n    var dimensions = this.dimensions;\n    for (var i = 0; i < dimensions.length; i++) {\n      var dimInfo = this._dimInfos[dimensions[i]];\n      if (dimInfo.ordinalMeta) {\n        store.collectOrdinalMeta(dimInfo.storeDimIndex, dimInfo.ordinalMeta);\n      }\n    }\n  };\n  SeriesData.prototype._shouldMakeIdFromName = function () {\n    var provider = this._store.getProvider();\n    return this._idDimIdx == null && provider.getSource().sourceFormat !== SOURCE_FORMAT_TYPED_ARRAY && !provider.fillStorage;\n  };\n  SeriesData.prototype._doInit = function (start, end) {\n    if (start >= end) {\n      return;\n    }\n    var store = this._store;\n    var provider = store.getProvider();\n    this._updateOrdinalMeta();\n    var nameList = this._nameList;\n    var idList = this._idList;\n    var sourceFormat = provider.getSource().sourceFormat;\n    var isFormatOriginal = sourceFormat === SOURCE_FORMAT_ORIGINAL;\n    // Each data item is value\n    // [1, 2]\n    // 2\n    // Bar chart, line chart which uses category axis\n    // only gives the 'y' value. 'x' value is the indices of category\n    // Use a tempValue to normalize the value to be a (x, y) value\n    // If dataItem is {name: ...} or {id: ...}, it has highest priority.\n    // This kind of ids and names are always stored `_nameList` and `_idList`.\n    if (isFormatOriginal && !provider.pure) {\n      var sharedDataItem = [];\n      for (var idx = start; idx < end; idx++) {\n        // NOTICE: Try not to write things into dataItem\n        var dataItem = provider.getItem(idx, sharedDataItem);\n        if (!this.hasItemOption && isDataItemOption(dataItem)) {\n          this.hasItemOption = true;\n        }\n        if (dataItem) {\n          var itemName = dataItem.name;\n          if (nameList[idx] == null && itemName != null) {\n            nameList[idx] = convertOptionIdName(itemName, null);\n          }\n          var itemId = dataItem.id;\n          if (idList[idx] == null && itemId != null) {\n            idList[idx] = convertOptionIdName(itemId, null);\n          }\n        }\n      }\n    }\n    if (this._shouldMakeIdFromName()) {\n      for (var idx = start; idx < end; idx++) {\n        makeIdFromName(this, idx);\n      }\n    }\n    prepareInvertedIndex(this);\n  };\n  /**\r\n   * PENDING: In fact currently this function is only used to short-circuit\r\n   * the calling of `scale.unionExtentFromData` when data have been filtered by modules\r\n   * like \"dataZoom\". `scale.unionExtentFromData` is used to calculate data extent for series on\r\n   * an axis, but if a \"axis related data filter module\" is used, the extent of the axis have\r\n   * been fixed and no need to calling `scale.unionExtentFromData` actually.\r\n   * But if we add \"custom data filter\" in future, which is not \"axis related\", this method may\r\n   * be still needed.\r\n   *\r\n   * Optimize for the scenario that data is filtered by a given extent.\r\n   * Consider that if data amount is more than hundreds of thousand,\r\n   * extent calculation will cost more than 10ms and the cache will\r\n   * be erased because of the filtering.\r\n   */\n  SeriesData.prototype.getApproximateExtent = function (dim) {\n    return this._approximateExtent[dim] || this._store.getDataExtent(this._getStoreDimIndex(dim));\n  };\n  /**\r\n   * Calculate extent on a filtered data might be time consuming.\r\n   * Approximate extent is only used for: calculate extent of filtered data outside.\r\n   */\n  SeriesData.prototype.setApproximateExtent = function (extent, dim) {\n    dim = this.getDimension(dim);\n    this._approximateExtent[dim] = extent.slice();\n  };\n  SeriesData.prototype.getCalculationInfo = function (key) {\n    return this._calculationInfo[key];\n  };\n  SeriesData.prototype.setCalculationInfo = function (key, value) {\n    isObject(key) ? zrUtil.extend(this._calculationInfo, key) : this._calculationInfo[key] = value;\n  };\n  /**\r\n   * @return Never be null/undefined. `number` will be converted to string. Because:\r\n   * In most cases, name is used in display, where returning a string is more convenient.\r\n   * In other cases, name is used in query (see `indexOfName`), where we can keep the\r\n   * rule that name `2` equals to name `'2'`.\r\n   */\n  SeriesData.prototype.getName = function (idx) {\n    var rawIndex = this.getRawIndex(idx);\n    var name = this._nameList[rawIndex];\n    if (name == null && this._nameDimIdx != null) {\n      name = getIdNameFromStore(this, this._nameDimIdx, rawIndex);\n    }\n    if (name == null) {\n      name = '';\n    }\n    return name;\n  };\n  SeriesData.prototype._getCategory = function (dimIdx, idx) {\n    var ordinal = this._store.get(dimIdx, idx);\n    var ordinalMeta = this._store.getOrdinalMeta(dimIdx);\n    if (ordinalMeta) {\n      return ordinalMeta.categories[ordinal];\n    }\n    return ordinal;\n  };\n  /**\r\n   * @return Never null/undefined. `number` will be converted to string. Because:\r\n   * In all cases having encountered at present, id is used in making diff comparison, which\r\n   * are usually based on hash map. We can keep the rule that the internal id are always string\r\n   * (treat `2` is the same as `'2'`) to make the related logic simple.\r\n   */\n  SeriesData.prototype.getId = function (idx) {\n    return getId(this, this.getRawIndex(idx));\n  };\n  SeriesData.prototype.count = function () {\n    return this._store.count();\n  };\n  /**\r\n   * Get value. Return NaN if idx is out of range.\r\n   *\r\n   * @notice Should better to use `data.getStore().get(dimIndex, dataIdx)` instead.\r\n   */\n  SeriesData.prototype.get = function (dim, idx) {\n    var store = this._store;\n    var dimInfo = this._dimInfos[dim];\n    if (dimInfo) {\n      return store.get(dimInfo.storeDimIndex, idx);\n    }\n  };\n  /**\r\n   * @notice Should better to use `data.getStore().getByRawIndex(dimIndex, dataIdx)` instead.\r\n   */\n  SeriesData.prototype.getByRawIndex = function (dim, rawIdx) {\n    var store = this._store;\n    var dimInfo = this._dimInfos[dim];\n    if (dimInfo) {\n      return store.getByRawIndex(dimInfo.storeDimIndex, rawIdx);\n    }\n  };\n  SeriesData.prototype.getIndices = function () {\n    return this._store.getIndices();\n  };\n  SeriesData.prototype.getDataExtent = function (dim) {\n    return this._store.getDataExtent(this._getStoreDimIndex(dim));\n  };\n  SeriesData.prototype.getSum = function (dim) {\n    return this._store.getSum(this._getStoreDimIndex(dim));\n  };\n  SeriesData.prototype.getMedian = function (dim) {\n    return this._store.getMedian(this._getStoreDimIndex(dim));\n  };\n  SeriesData.prototype.getValues = function (dimensions, idx) {\n    var _this = this;\n    var store = this._store;\n    return zrUtil.isArray(dimensions) ? store.getValues(map(dimensions, function (dim) {\n      return _this._getStoreDimIndex(dim);\n    }), idx) : store.getValues(dimensions);\n  };\n  /**\r\n   * If value is NaN. Including '-'\r\n   * Only check the coord dimensions.\r\n   */\n  SeriesData.prototype.hasValue = function (idx) {\n    var dataDimIndicesOnCoord = this._dimSummary.dataDimIndicesOnCoord;\n    for (var i = 0, len = dataDimIndicesOnCoord.length; i < len; i++) {\n      // Ordinal type originally can be string or number.\n      // But when an ordinal type is used on coord, it can\n      // not be string but only number. So we can also use isNaN.\n      if (isNaN(this._store.get(dataDimIndicesOnCoord[i], idx))) {\n        return false;\n      }\n    }\n    return true;\n  };\n  /**\r\n   * Retrieve the index with given name\r\n   */\n  SeriesData.prototype.indexOfName = function (name) {\n    for (var i = 0, len = this._store.count(); i < len; i++) {\n      if (this.getName(i) === name) {\n        return i;\n      }\n    }\n    return -1;\n  };\n  SeriesData.prototype.getRawIndex = function (idx) {\n    return this._store.getRawIndex(idx);\n  };\n  SeriesData.prototype.indexOfRawIndex = function (rawIndex) {\n    return this._store.indexOfRawIndex(rawIndex);\n  };\n  /**\r\n   * Only support the dimension which inverted index created.\r\n   * Do not support other cases until required.\r\n   * @param dim concrete dim\r\n   * @param value ordinal index\r\n   * @return rawIndex\r\n   */\n  SeriesData.prototype.rawIndexOf = function (dim, value) {\n    var invertedIndices = dim && this._invertedIndicesMap[dim];\n    if (process.env.NODE_ENV !== 'production') {\n      if (!invertedIndices) {\n        throw new Error('Do not supported yet');\n      }\n    }\n    var rawIndex = invertedIndices && invertedIndices[value];\n    if (rawIndex == null || isNaN(rawIndex)) {\n      return INDEX_NOT_FOUND;\n    }\n    return rawIndex;\n  };\n  /**\r\n   * Retrieve the index of nearest value\r\n   * @param dim\r\n   * @param value\r\n   * @param [maxDistance=Infinity]\r\n   * @return If and only if multiple indices has\r\n   *         the same value, they are put to the result.\r\n   */\n  SeriesData.prototype.indicesOfNearest = function (dim, value, maxDistance) {\n    return this._store.indicesOfNearest(this._getStoreDimIndex(dim), value, maxDistance);\n  };\n  SeriesData.prototype.each = function (dims, cb, ctx) {\n    'use strict';\n\n    if (zrUtil.isFunction(dims)) {\n      ctx = cb;\n      cb = dims;\n      dims = [];\n    }\n    // ctxCompat just for compat echarts3\n    var fCtx = ctx || this;\n    var dimIndices = map(normalizeDimensions(dims), this._getStoreDimIndex, this);\n    this._store.each(dimIndices, fCtx ? zrUtil.bind(cb, fCtx) : cb);\n  };\n  SeriesData.prototype.filterSelf = function (dims, cb, ctx) {\n    'use strict';\n\n    if (zrUtil.isFunction(dims)) {\n      ctx = cb;\n      cb = dims;\n      dims = [];\n    }\n    // ctxCompat just for compat echarts3\n    var fCtx = ctx || this;\n    var dimIndices = map(normalizeDimensions(dims), this._getStoreDimIndex, this);\n    this._store = this._store.filter(dimIndices, fCtx ? zrUtil.bind(cb, fCtx) : cb);\n    return this;\n  };\n  /**\r\n   * Select data in range. (For optimization of filter)\r\n   * (Manually inline code, support 5 million data filtering in data zoom.)\r\n   */\n  SeriesData.prototype.selectRange = function (range) {\n    'use strict';\n\n    var _this = this;\n    var innerRange = {};\n    var dims = zrUtil.keys(range);\n    var dimIndices = [];\n    zrUtil.each(dims, function (dim) {\n      var dimIdx = _this._getStoreDimIndex(dim);\n      innerRange[dimIdx] = range[dim];\n      dimIndices.push(dimIdx);\n    });\n    this._store = this._store.selectRange(innerRange);\n    return this;\n  };\n  /* eslint-enable max-len */\n  SeriesData.prototype.mapArray = function (dims, cb, ctx) {\n    'use strict';\n\n    if (zrUtil.isFunction(dims)) {\n      ctx = cb;\n      cb = dims;\n      dims = [];\n    }\n    // ctxCompat just for compat echarts3\n    ctx = ctx || this;\n    var result = [];\n    this.each(dims, function () {\n      result.push(cb && cb.apply(this, arguments));\n    }, ctx);\n    return result;\n  };\n  SeriesData.prototype.map = function (dims, cb, ctx, ctxCompat) {\n    'use strict';\n\n    // ctxCompat just for compat echarts3\n    var fCtx = ctx || ctxCompat || this;\n    var dimIndices = map(normalizeDimensions(dims), this._getStoreDimIndex, this);\n    var list = cloneListForMapAndSample(this);\n    list._store = this._store.map(dimIndices, fCtx ? zrUtil.bind(cb, fCtx) : cb);\n    return list;\n  };\n  SeriesData.prototype.modify = function (dims, cb, ctx, ctxCompat) {\n    var _this = this;\n    // ctxCompat just for compat echarts3\n    var fCtx = ctx || ctxCompat || this;\n    if (process.env.NODE_ENV !== 'production') {\n      zrUtil.each(normalizeDimensions(dims), function (dim) {\n        var dimInfo = _this.getDimensionInfo(dim);\n        if (!dimInfo.isCalculationCoord) {\n          console.error('Danger: only stack dimension can be modified');\n        }\n      });\n    }\n    var dimIndices = map(normalizeDimensions(dims), this._getStoreDimIndex, this);\n    // If do shallow clone here, if there are too many stacked series,\n    // it still cost lots of memory, because `_store.dimensions` are not shared.\n    // We should consider there probably be shallow clone happen in each series\n    // in consequent filter/map.\n    this._store.modify(dimIndices, fCtx ? zrUtil.bind(cb, fCtx) : cb);\n  };\n  /**\r\n   * Large data down sampling on given dimension\r\n   * @param sampleIndex Sample index for name and id\r\n   */\n  SeriesData.prototype.downSample = function (dimension, rate, sampleValue, sampleIndex) {\n    var list = cloneListForMapAndSample(this);\n    list._store = this._store.downSample(this._getStoreDimIndex(dimension), rate, sampleValue, sampleIndex);\n    return list;\n  };\n  /**\r\n   * Large data down sampling using min-max\r\n   * @param {string} valueDimension\r\n   * @param {number} rate\r\n   */\n  SeriesData.prototype.minmaxDownSample = function (valueDimension, rate) {\n    var list = cloneListForMapAndSample(this);\n    list._store = this._store.minmaxDownSample(this._getStoreDimIndex(valueDimension), rate);\n    return list;\n  };\n  /**\r\n   * Large data down sampling using largest-triangle-three-buckets\r\n   * @param {string} valueDimension\r\n   * @param {number} targetCount\r\n   */\n  SeriesData.prototype.lttbDownSample = function (valueDimension, rate) {\n    var list = cloneListForMapAndSample(this);\n    list._store = this._store.lttbDownSample(this._getStoreDimIndex(valueDimension), rate);\n    return list;\n  };\n  SeriesData.prototype.getRawDataItem = function (idx) {\n    return this._store.getRawDataItem(idx);\n  };\n  /**\r\n   * Get model of one data item.\r\n   */\n  // TODO: Type of data item\n  SeriesData.prototype.getItemModel = function (idx) {\n    var hostModel = this.hostModel;\n    var dataItem = this.getRawDataItem(idx);\n    return new Model(dataItem, hostModel, hostModel && hostModel.ecModel);\n  };\n  /**\r\n   * Create a data differ\r\n   */\n  SeriesData.prototype.diff = function (otherList) {\n    var thisList = this;\n    return new DataDiffer(otherList ? otherList.getStore().getIndices() : [], this.getStore().getIndices(), function (idx) {\n      return getId(otherList, idx);\n    }, function (idx) {\n      return getId(thisList, idx);\n    });\n  };\n  /**\r\n   * Get visual property.\r\n   */\n  SeriesData.prototype.getVisual = function (key) {\n    var visual = this._visual;\n    return visual && visual[key];\n  };\n  SeriesData.prototype.setVisual = function (kvObj, val) {\n    this._visual = this._visual || {};\n    if (isObject(kvObj)) {\n      zrUtil.extend(this._visual, kvObj);\n    } else {\n      this._visual[kvObj] = val;\n    }\n  };\n  /**\r\n   * Get visual property of single data item\r\n   */\n  // eslint-disable-next-line\n  SeriesData.prototype.getItemVisual = function (idx, key) {\n    var itemVisual = this._itemVisuals[idx];\n    var val = itemVisual && itemVisual[key];\n    if (val == null) {\n      // Use global visual property\n      return this.getVisual(key);\n    }\n    return val;\n  };\n  /**\r\n   * If exists visual property of single data item\r\n   */\n  SeriesData.prototype.hasItemVisual = function () {\n    return this._itemVisuals.length > 0;\n  };\n  /**\r\n   * Make sure itemVisual property is unique\r\n   */\n  // TODO: use key to save visual to reduce memory.\n  SeriesData.prototype.ensureUniqueItemVisual = function (idx, key) {\n    var itemVisuals = this._itemVisuals;\n    var itemVisual = itemVisuals[idx];\n    if (!itemVisual) {\n      itemVisual = itemVisuals[idx] = {};\n    }\n    var val = itemVisual[key];\n    if (val == null) {\n      val = this.getVisual(key);\n      // TODO Performance?\n      if (zrUtil.isArray(val)) {\n        val = val.slice();\n      } else if (isObject(val)) {\n        val = zrUtil.extend({}, val);\n      }\n      itemVisual[key] = val;\n    }\n    return val;\n  };\n  // eslint-disable-next-line\n  SeriesData.prototype.setItemVisual = function (idx, key, value) {\n    var itemVisual = this._itemVisuals[idx] || {};\n    this._itemVisuals[idx] = itemVisual;\n    if (isObject(key)) {\n      zrUtil.extend(itemVisual, key);\n    } else {\n      itemVisual[key] = value;\n    }\n  };\n  /**\r\n   * Clear itemVisuals and list visual.\r\n   */\n  SeriesData.prototype.clearAllVisual = function () {\n    this._visual = {};\n    this._itemVisuals = [];\n  };\n  SeriesData.prototype.setLayout = function (key, val) {\n    isObject(key) ? zrUtil.extend(this._layout, key) : this._layout[key] = val;\n  };\n  /**\r\n   * Get layout property.\r\n   */\n  SeriesData.prototype.getLayout = function (key) {\n    return this._layout[key];\n  };\n  /**\r\n   * Get layout of single data item\r\n   */\n  SeriesData.prototype.getItemLayout = function (idx) {\n    return this._itemLayouts[idx];\n  };\n  /**\r\n   * Set layout of single data item\r\n   */\n  SeriesData.prototype.setItemLayout = function (idx, layout, merge) {\n    this._itemLayouts[idx] = merge ? zrUtil.extend(this._itemLayouts[idx] || {}, layout) : layout;\n  };\n  /**\r\n   * Clear all layout of single data item\r\n   */\n  SeriesData.prototype.clearItemLayouts = function () {\n    this._itemLayouts.length = 0;\n  };\n  /**\r\n   * Set graphic element relative to data. It can be set as null\r\n   */\n  SeriesData.prototype.setItemGraphicEl = function (idx, el) {\n    var seriesIndex = this.hostModel && this.hostModel.seriesIndex;\n    setCommonECData(seriesIndex, this.dataType, idx, el);\n    this._graphicEls[idx] = el;\n  };\n  SeriesData.prototype.getItemGraphicEl = function (idx) {\n    return this._graphicEls[idx];\n  };\n  SeriesData.prototype.eachItemGraphicEl = function (cb, context) {\n    zrUtil.each(this._graphicEls, function (el, idx) {\n      if (el) {\n        cb && cb.call(context, el, idx);\n      }\n    });\n  };\n  /**\r\n   * Shallow clone a new list except visual and layout properties, and graph elements.\r\n   * New list only change the indices.\r\n   */\n  SeriesData.prototype.cloneShallow = function (list) {\n    if (!list) {\n      list = new SeriesData(this._schema ? this._schema : map(this.dimensions, this._getDimInfo, this), this.hostModel);\n    }\n    transferProperties(list, this);\n    list._store = this._store;\n    return list;\n  };\n  /**\r\n   * Wrap some method to add more feature\r\n   */\n  SeriesData.prototype.wrapMethod = function (methodName, injectFunction) {\n    var originalMethod = this[methodName];\n    if (!zrUtil.isFunction(originalMethod)) {\n      return;\n    }\n    this.__wrappedMethods = this.__wrappedMethods || [];\n    this.__wrappedMethods.push(methodName);\n    this[methodName] = function () {\n      var res = originalMethod.apply(this, arguments);\n      return injectFunction.apply(this, [res].concat(zrUtil.slice(arguments)));\n    };\n  };\n  // ----------------------------------------------------------\n  // A work around for internal method visiting private member.\n  // ----------------------------------------------------------\n  SeriesData.internalField = function () {\n    prepareInvertedIndex = function (data) {\n      var invertedIndicesMap = data._invertedIndicesMap;\n      zrUtil.each(invertedIndicesMap, function (invertedIndices, dim) {\n        var dimInfo = data._dimInfos[dim];\n        // Currently, only dimensions that has ordinalMeta can create inverted indices.\n        var ordinalMeta = dimInfo.ordinalMeta;\n        var store = data._store;\n        if (ordinalMeta) {\n          invertedIndices = invertedIndicesMap[dim] = new CtorInt32Array(ordinalMeta.categories.length);\n          // The default value of TypedArray is 0. To avoid miss\n          // mapping to 0, we should set it as INDEX_NOT_FOUND.\n          for (var i = 0; i < invertedIndices.length; i++) {\n            invertedIndices[i] = INDEX_NOT_FOUND;\n          }\n          for (var i = 0; i < store.count(); i++) {\n            // Only support the case that all values are distinct.\n            invertedIndices[store.get(dimInfo.storeDimIndex, i)] = i;\n          }\n        }\n      });\n    };\n    getIdNameFromStore = function (data, dimIdx, idx) {\n      return convertOptionIdName(data._getCategory(dimIdx, idx), null);\n    };\n    /**\r\n     * @see the comment of `List['getId']`.\r\n     */\n    getId = function (data, rawIndex) {\n      var id = data._idList[rawIndex];\n      if (id == null && data._idDimIdx != null) {\n        id = getIdNameFromStore(data, data._idDimIdx, rawIndex);\n      }\n      if (id == null) {\n        id = ID_PREFIX + rawIndex;\n      }\n      return id;\n    };\n    normalizeDimensions = function (dimensions) {\n      if (!zrUtil.isArray(dimensions)) {\n        dimensions = dimensions != null ? [dimensions] : [];\n      }\n      return dimensions;\n    };\n    /**\r\n     * Data in excludeDimensions is copied, otherwise transferred.\r\n     */\n    cloneListForMapAndSample = function (original) {\n      var list = new SeriesData(original._schema ? original._schema : map(original.dimensions, original._getDimInfo, original), original.hostModel);\n      // FIXME If needs stackedOn, value may already been stacked\n      transferProperties(list, original);\n      return list;\n    };\n    transferProperties = function (target, source) {\n      zrUtil.each(TRANSFERABLE_PROPERTIES.concat(source.__wrappedMethods || []), function (propName) {\n        if (source.hasOwnProperty(propName)) {\n          target[propName] = source[propName];\n        }\n      });\n      target.__wrappedMethods = source.__wrappedMethods;\n      zrUtil.each(CLONE_PROPERTIES, function (propName) {\n        target[propName] = zrUtil.clone(source[propName]);\n      });\n      target._calculationInfo = zrUtil.extend({}, source._calculationInfo);\n    };\n    makeIdFromName = function (data, idx) {\n      var nameList = data._nameList;\n      var idList = data._idList;\n      var nameDimIdx = data._nameDimIdx;\n      var idDimIdx = data._idDimIdx;\n      var name = nameList[idx];\n      var id = idList[idx];\n      if (name == null && nameDimIdx != null) {\n        nameList[idx] = name = getIdNameFromStore(data, nameDimIdx, idx);\n      }\n      if (id == null && idDimIdx != null) {\n        idList[idx] = id = getIdNameFromStore(data, idDimIdx, idx);\n      }\n      if (id == null && name != null) {\n        var nameRepeatCount = data._nameRepeatCount;\n        var nmCnt = nameRepeatCount[name] = (nameRepeatCount[name] || 0) + 1;\n        id = name;\n        if (nmCnt > 1) {\n          id += '__ec__' + nmCnt;\n        }\n        idList[idx] = id;\n      }\n    };\n  }();\n  return SeriesData;\n}();\nexport default SeriesData;"], "names": [], "mappings": "AACA;;;;;;;;;;;;;;;;;AAiBA,GAGA;;CAEC,GAED;;;;;;;;;;;;;;;;;AAiBA,GACA,qBAAqB;;;AACrB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;AACA,IAAI,WAAW,8IAAA,CAAA,WAAe;AAC9B,IAAI,MAAM,8IAAA,CAAA,MAAU;AACpB,IAAI,iBAAiB,OAAO,eAAe,cAAc,QAAQ;AACjE,gEAAgE;AAChE,2CAA2C;AAC3C,IAAI,YAAY;AAChB,IAAI,kBAAkB,CAAC;AACvB,8CAA8C;AAC9C,IAAI,0BAA0B;IAAC;IAAiB;IAAa;IAAW;IAAuB;IAAe;IAAc;IAAY;IAAmB;IAAe;IAAa;CAAmB;AAC1M,IAAI,mBAAmB;IAAC;CAAqB;AAC7C,gCAAgC;AAChC,gCAAgC;AAChC,gCAAgC;AAChC,IAAI;AACJ,IAAI;AACJ,IAAI;AACJ,IAAI;AACJ,IAAI;AACJ,IAAI;AACJ,IAAI;AACJ,IAAI,aAAa,WAAW,GAAE;IAC5B;;;;GAIC,GACD,SAAS,WAAW,eAAe,EAAE,SAAS;QAC5C,IAAI,CAAC,IAAI,GAAG;QACZ,IAAI,CAAC,WAAW,GAAG;QACnB,IAAI,CAAC,SAAS,GAAG,EAAE;QACnB,IAAI,CAAC,OAAO,GAAG,EAAE;QACjB,oEAAoE;QACpE,iCAAiC;QACjC,uCAAuC;QACvC,+CAA+C;QAC/C,IAAI,CAAC,OAAO,GAAG,CAAC;QAChB,4BAA4B;QAC5B,IAAI,CAAC,OAAO,GAAG,CAAC;QAChB,6CAA6C;QAC7C,IAAI,CAAC,YAAY,GAAG,EAAE;QACtB,sCAAsC;QACtC,IAAI,CAAC,YAAY,GAAG,EAAE;QACtB,mBAAmB;QACnB,IAAI,CAAC,WAAW,GAAG,EAAE;QACrB,0BAA0B;QAC1B,IAAI,CAAC,kBAAkB,GAAG,CAAC;QAC3B,IAAI,CAAC,gBAAgB,GAAG,CAAC;QACzB,gEAAgE;QAChE,oCAAoC;QACpC,uDAAuD;QACvD,yDAAyD;QACzD,IAAI,CAAC,aAAa,GAAG;QACrB,2EAA2E;QAC3E,yDAAyD;QACzD,IAAI,CAAC,oBAAoB,GAAG;YAAC;YAAgB;YAAc;YAAoB;YAAkB;SAAM;QACvG,kEAAkE;QAClE,IAAI,CAAC,iBAAiB,GAAG;YAAC;YAAc;SAAc;QACtD,IAAI,CAAC,kBAAkB,GAAG;YAAC;YAAc;YAAoB;SAAiB;QAC9E,IAAI;QACJ,IAAI,oBAAoB;QACxB,IAAI,CAAA,GAAA,oKAAA,CAAA,qBAAkB,AAAD,EAAE,kBAAkB;YACvC,aAAa,gBAAgB,UAAU;YACvC,IAAI,CAAC,WAAW,GAAG,gBAAgB,kBAAkB;YACrD,IAAI,CAAC,OAAO,GAAG;QACjB,OAAO;YACL,oBAAoB;YACpB,aAAa;QACf;QACA,aAAa,cAAc;YAAC;YAAK;SAAI;QACrC,IAAI,iBAAiB,CAAC;QACtB,IAAI,iBAAiB,EAAE;QACvB,IAAI,qBAAqB,CAAC;QAC1B,IAAI,cAAc;QAClB,IAAI,WAAW,CAAC;QAChB,IAAK,IAAI,IAAI,GAAG,IAAI,WAAW,MAAM,EAAE,IAAK;YAC1C,qEAAqE;YACrE,IAAI,eAAe,UAAU,CAAC,EAAE;YAChC,IAAI,gBAAgB,CAAA,GAAA,8IAAA,CAAA,WAAe,AAAD,EAAE,gBAAgB,IAAI,+JAAA,CAAA,UAAqB,CAAC;gBAC5E,MAAM;YACR,KAAK,CAAC,CAAC,wBAAwB,+JAAA,CAAA,UAAqB,IAAI,IAAI,+JAAA,CAAA,UAAqB,CAAC,gBAAgB;YAClG,IAAI,gBAAgB,cAAc,IAAI;YACtC,cAAc,IAAI,GAAG,cAAc,IAAI,IAAI;YAC3C,IAAI,CAAC,cAAc,QAAQ,EAAE;gBAC3B,cAAc,QAAQ,GAAG;gBACzB,cAAc,aAAa,GAAG;YAChC;YACA,IAAI,YAAY,cAAc,SAAS,GAAG,cAAc,SAAS,IAAI,CAAC;YACtE,eAAe,IAAI,CAAC;YACpB,cAAc,CAAC,cAAc,GAAG;YAChC,IAAI,QAAQ,CAAC,cAAc,IAAI,MAAM;gBACnC,cAAc;YAChB;YACA,IAAI,cAAc,qBAAqB,EAAE;gBACvC,kBAAkB,CAAC,cAAc,GAAG,EAAE;YACxC;YACA,IAAI,UAAU,QAAQ,KAAK,GAAG;gBAC5B,IAAI,CAAC,WAAW,GAAG;YACrB;YACA,IAAI,UAAU,MAAM,KAAK,GAAG;gBAC1B,IAAI,CAAC,SAAS,GAAG;YACnB;YACA,wCAA2C;gBACzC,CAAA,GAAA,8IAAA,CAAA,SAAa,AAAD,EAAE,qBAAqB,cAAc,aAAa,IAAI;YACpE;YACA,IAAI,mBAAmB;gBACrB,cAAc,aAAa,GAAG;YAChC;QACF;QACA,IAAI,CAAC,UAAU,GAAG;QAClB,IAAI,CAAC,SAAS,GAAG;QACjB,IAAI,CAAC,qBAAqB,CAAC;QAC3B,IAAI,CAAC,SAAS,GAAG;QACjB,IAAI,CAAC,mBAAmB,GAAG;QAC3B,IAAI,IAAI,CAAC,WAAW,EAAE;YACpB,IAAI,iBAAiB,IAAI,CAAC,aAAa,GAAG,CAAA,GAAA,8IAAA,CAAA,gBAAoB,AAAD;YAC7D,CAAA,GAAA,8IAAA,CAAA,OAAW,AAAD,EAAE,gBAAgB,SAAU,OAAO;gBAC3C,eAAe,GAAG,CAAC,cAAc,CAAC,QAAQ,CAAC,aAAa,EAAE;YAC5D;QACF;IACF;IACA;;;;;;;;;;;;;;;;GAgBC,GACD,WAAW,SAAS,CAAC,YAAY,GAAG,SAAU,GAAG;QAC/C,IAAI,SAAS,IAAI,CAAC,kBAAkB,CAAC;QACrC,IAAI,UAAU,MAAM;YAClB,OAAO;QACT;QACA,SAAS;QACT,IAAI,CAAC,IAAI,CAAC,WAAW,EAAE;YACrB,OAAO,IAAI,CAAC,UAAU,CAAC,OAAO;QAChC;QACA,yEAAyE;QACzE,4CAA4C;QAC5C,IAAI,UAAU,IAAI,CAAC,aAAa,CAAC,GAAG,CAAC;QACrC,IAAI,WAAW,MAAM;YACnB,OAAO;QACT;QACA,IAAI,eAAe,IAAI,CAAC,OAAO,CAAC,kBAAkB,CAAC;QACnD,IAAI,cAAc;YAChB,OAAO,aAAa,IAAI;QAC1B;IACF;IACA;;;GAGC,GACD,WAAW,SAAS,CAAC,iBAAiB,GAAG,SAAU,GAAG;QACpD,IAAI,SAAS,IAAI,CAAC,kBAAkB,CAAC;QACrC,IAAI,UAAU,MAAM;YAClB,OAAO;QACT;QACA,IAAI,OAAO,MAAM;YACf,OAAO,CAAC;QACV;QACA,IAAI,UAAU,IAAI,CAAC,WAAW,CAAC;QAC/B,OAAO,UAAU,QAAQ,aAAa,GAAG,IAAI,CAAC,WAAW,GAAG,IAAI,CAAC,OAAO,CAAC,uBAAuB,CAAC,OAAO,CAAC;IAC3G;IACA;;;;;;;;;;;;;;;;;;GAkBC,GACD,WAAW,SAAS,CAAC,kBAAkB,GAAG,SAAU,GAAG;QACrD,IAAI,CAAA,GAAA,8IAAA,CAAA,WAAe,AAAD,EAAE,QAEjB,OAAO,QAAQ,CAAC,MAAM,QAAQ,CAAC,IAAI,CAAC,WAAW,CAAC,QAAQ,CAAC,CAAC,IAAI,CAAC,WAAW,IAAI,IAAI,CAAC,OAAO,CAAC,uBAAuB,CAAC,OAAO,CAAC,GAAG;YAC/H,OAAO,CAAC;QACV;IACF;IACA,WAAW,SAAS,CAAC,iBAAiB,GAAG,SAAU,GAAG;QACpD,IAAI,SAAS,IAAI,CAAC,iBAAiB,CAAC;QACpC,wCAA2C;YACzC,IAAI,UAAU,MAAM;gBAClB,MAAM,IAAI,MAAM,uBAAuB;YACzC;QACF;QACA,OAAO;IACT;IACA;;;;;GAKC,GACD,WAAW,SAAS,CAAC,gBAAgB,GAAG,SAAU,GAAG;QACnD,4DAA4D;QAC5D,OAAO,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,YAAY,CAAC;IAC5C;IACA,WAAW,SAAS,CAAC,qBAAqB,GAAG,SAAU,WAAW;QAChE,IAAI,iBAAiB,IAAI,CAAC,SAAS;QACnC,IAAI,CAAC,WAAW,GAAG,cAAc,SAAU,OAAO;YAChD,OAAO,eAAe,cAAc,CAAC,WAAW,cAAc,CAAC,QAAQ,GAAG;QAC5E,IAAI,SAAU,OAAO;YACnB,OAAO,cAAc,CAAC,QAAQ;QAChC;IACF;IACA;;GAEC,GACD,WAAW,SAAS,CAAC,oBAAoB,GAAG;QAC1C,OAAO,IAAI,CAAC,WAAW,CAAC,eAAe,CAAC,KAAK;IAC/C;IACA,WAAW,SAAS,CAAC,YAAY,GAAG,SAAU,QAAQ,EAAE,GAAG;QACzD,IAAI,oBAAoB,IAAI,CAAC,WAAW;QACxC,IAAI,OAAO,MAAM;YACf,OAAO,kBAAkB,sBAAsB,CAAC,SAAS;QAC3D;QACA,IAAI,OAAO,kBAAkB,MAAM,CAAC,SAAS;QAC7C,OAAO,OAAO,IAAI,CAAC,IAAI,GAAG;IAC5B;IACA,WAAW,SAAS,CAAC,gBAAgB,GAAG,SAAU,QAAQ;QACxD,IAAI,oBAAoB,IAAI,CAAC,WAAW;QACxC,IAAI,OAAO,kBAAkB,MAAM,CAAC,SAAS;QAC7C,OAAO,CAAC,QAAQ,EAAE,EAAE,KAAK;IAC3B;IACA,WAAW,SAAS,CAAC,QAAQ,GAAG;QAC9B,OAAO,IAAI,CAAC,MAAM;IACpB;IACA;;;;;;;;GAQC,GACD,WAAW,SAAS,CAAC,QAAQ,GAAG,SAAU,IAAI,EAAE,QAAQ,EAAE,cAAc;QACtE,IAAI,QAAQ,IAAI;QAChB,IAAI;QACJ,IAAI,gBAAgB,mJAAA,CAAA,UAAS,EAAE;YAC7B,QAAQ;QACV;QACA,IAAI,CAAC,OAAO;YACV,IAAI,aAAa,IAAI,CAAC,UAAU;YAChC,IAAI,WAAW,CAAA,GAAA,gJAAA,CAAA,mBAAgB,AAAD,EAAE,SAAS,CAAA,GAAA,8IAAA,CAAA,cAAkB,AAAD,EAAE,QAAQ,IAAI,gKAAA,CAAA,sBAAmB,CAAC,MAAM,WAAW,MAAM,IAAI;YACvH,QAAQ,IAAI,mJAAA,CAAA,UAAS;YACrB,IAAI,iBAAiB,IAAI,YAAY,SAAU,OAAO;gBACpD,OAAO;oBACL,MAAM,MAAM,SAAS,CAAC,QAAQ,CAAC,IAAI;oBACnC,UAAU;gBACZ;YACF;YACA,MAAM,QAAQ,CAAC,UAAU,gBAAgB;QAC3C;QACA,IAAI,CAAC,MAAM,GAAG;QACd,QAAQ;QACR,IAAI,CAAC,SAAS,GAAG,CAAC,YAAY,EAAE,EAAE,KAAK;QACvC,IAAI,CAAC,OAAO,GAAG,EAAE;QACjB,IAAI,CAAC,gBAAgB,GAAG,CAAC;QACzB,IAAI,CAAC,OAAO,CAAC,GAAG,MAAM,KAAK;QAC3B,4DAA4D;QAC5D,mDAAmD;QACnD,IAAI,CAAC,WAAW,GAAG,CAAA,GAAA,mKAAA,CAAA,sBAAmB,AAAD,EAAE,IAAI,EAAE,IAAI,CAAC,OAAO;QACzD,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC,WAAW,CAAC,UAAU;IAC/C;IACA;;GAEC,GACD,WAAW,SAAS,CAAC,UAAU,GAAG,SAAU,IAAI;QAC9C,IAAI,QAAQ,IAAI,CAAC,MAAM,CAAC,UAAU,CAAC;QACnC,IAAI,CAAC,OAAO,CAAC,KAAK,CAAC,EAAE,EAAE,KAAK,CAAC,EAAE;IACjC;IACA;;;;;;;;;;;;;;GAcC,GACD,WAAW,SAAS,CAAC,YAAY,GAAG,SAAU,MAAM,EAAE,KAAK;QACzD,IAAI,KAAK,IAAI,CAAC,MAAM,CAAC,YAAY,CAAC,QAAQ,SAAS,MAAM,MAAM,GAC7D,QAAQ,GAAG,KAAK,EAChB,MAAM,GAAG,GAAG;QACd,IAAI,uBAAuB,IAAI,CAAC,qBAAqB;QACrD,IAAI,CAAC,kBAAkB;QACvB,IAAI,OAAO;YACT,IAAK,IAAI,MAAM,OAAO,MAAM,KAAK,MAAO;gBACtC,IAAI,YAAY,MAAM;gBACtB,IAAI,CAAC,SAAS,CAAC,IAAI,GAAG,KAAK,CAAC,UAAU;gBACtC,IAAI,sBAAsB;oBACxB,eAAe,IAAI,EAAE;gBACvB;YACF;QACF;IACF;IACA,WAAW,SAAS,CAAC,kBAAkB,GAAG;QACxC,IAAI,QAAQ,IAAI,CAAC,MAAM;QACvB,IAAI,aAAa,IAAI,CAAC,UAAU;QAChC,IAAK,IAAI,IAAI,GAAG,IAAI,WAAW,MAAM,EAAE,IAAK;YAC1C,IAAI,UAAU,IAAI,CAAC,SAAS,CAAC,UAAU,CAAC,EAAE,CAAC;YAC3C,IAAI,QAAQ,WAAW,EAAE;gBACvB,MAAM,kBAAkB,CAAC,QAAQ,aAAa,EAAE,QAAQ,WAAW;YACrE;QACF;IACF;IACA,WAAW,SAAS,CAAC,qBAAqB,GAAG;QAC3C,IAAI,WAAW,IAAI,CAAC,MAAM,CAAC,WAAW;QACtC,OAAO,IAAI,CAAC,SAAS,IAAI,QAAQ,SAAS,SAAS,GAAG,YAAY,KAAK,+IAAA,CAAA,4BAAyB,IAAI,CAAC,SAAS,WAAW;IAC3H;IACA,WAAW,SAAS,CAAC,OAAO,GAAG,SAAU,KAAK,EAAE,GAAG;QACjD,IAAI,SAAS,KAAK;YAChB;QACF;QACA,IAAI,QAAQ,IAAI,CAAC,MAAM;QACvB,IAAI,WAAW,MAAM,WAAW;QAChC,IAAI,CAAC,kBAAkB;QACvB,IAAI,WAAW,IAAI,CAAC,SAAS;QAC7B,IAAI,SAAS,IAAI,CAAC,OAAO;QACzB,IAAI,eAAe,SAAS,SAAS,GAAG,YAAY;QACpD,IAAI,mBAAmB,iBAAiB,+IAAA,CAAA,yBAAsB;QAC9D,0BAA0B;QAC1B,SAAS;QACT,IAAI;QACJ,iDAAiD;QACjD,iEAAiE;QACjE,8DAA8D;QAC9D,oEAAoE;QACpE,0EAA0E;QAC1E,IAAI,oBAAoB,CAAC,SAAS,IAAI,EAAE;YACtC,IAAI,iBAAiB,EAAE;YACvB,IAAK,IAAI,MAAM,OAAO,MAAM,KAAK,MAAO;gBACtC,gDAAgD;gBAChD,IAAI,WAAW,SAAS,OAAO,CAAC,KAAK;gBACrC,IAAI,CAAC,IAAI,CAAC,aAAa,IAAI,CAAA,GAAA,+IAAA,CAAA,mBAAgB,AAAD,EAAE,WAAW;oBACrD,IAAI,CAAC,aAAa,GAAG;gBACvB;gBACA,IAAI,UAAU;oBACZ,IAAI,WAAW,SAAS,IAAI;oBAC5B,IAAI,QAAQ,CAAC,IAAI,IAAI,QAAQ,YAAY,MAAM;wBAC7C,QAAQ,CAAC,IAAI,GAAG,CAAA,GAAA,+IAAA,CAAA,sBAAmB,AAAD,EAAE,UAAU;oBAChD;oBACA,IAAI,SAAS,SAAS,EAAE;oBACxB,IAAI,MAAM,CAAC,IAAI,IAAI,QAAQ,UAAU,MAAM;wBACzC,MAAM,CAAC,IAAI,GAAG,CAAA,GAAA,+IAAA,CAAA,sBAAmB,AAAD,EAAE,QAAQ;oBAC5C;gBACF;YACF;QACF;QACA,IAAI,IAAI,CAAC,qBAAqB,IAAI;YAChC,IAAK,IAAI,MAAM,OAAO,MAAM,KAAK,MAAO;gBACtC,eAAe,IAAI,EAAE;YACvB;QACF;QACA,qBAAqB,IAAI;IAC3B;IACA;;;;;;;;;;;;;GAaC,GACD,WAAW,SAAS,CAAC,oBAAoB,GAAG,SAAU,GAAG;QACvD,OAAO,IAAI,CAAC,kBAAkB,CAAC,IAAI,IAAI,IAAI,CAAC,MAAM,CAAC,aAAa,CAAC,IAAI,CAAC,iBAAiB,CAAC;IAC1F;IACA;;;GAGC,GACD,WAAW,SAAS,CAAC,oBAAoB,GAAG,SAAU,MAAM,EAAE,GAAG;QAC/D,MAAM,IAAI,CAAC,YAAY,CAAC;QACxB,IAAI,CAAC,kBAAkB,CAAC,IAAI,GAAG,OAAO,KAAK;IAC7C;IACA,WAAW,SAAS,CAAC,kBAAkB,GAAG,SAAU,GAAG;QACrD,OAAO,IAAI,CAAC,gBAAgB,CAAC,IAAI;IACnC;IACA,WAAW,SAAS,CAAC,kBAAkB,GAAG,SAAU,GAAG,EAAE,KAAK;QAC5D,SAAS,OAAO,CAAA,GAAA,8IAAA,CAAA,SAAa,AAAD,EAAE,IAAI,CAAC,gBAAgB,EAAE,OAAO,IAAI,CAAC,gBAAgB,CAAC,IAAI,GAAG;IAC3F;IACA;;;;;GAKC,GACD,WAAW,SAAS,CAAC,OAAO,GAAG,SAAU,GAAG;QAC1C,IAAI,WAAW,IAAI,CAAC,WAAW,CAAC;QAChC,IAAI,OAAO,IAAI,CAAC,SAAS,CAAC,SAAS;QACnC,IAAI,QAAQ,QAAQ,IAAI,CAAC,WAAW,IAAI,MAAM;YAC5C,OAAO,mBAAmB,IAAI,EAAE,IAAI,CAAC,WAAW,EAAE;QACpD;QACA,IAAI,QAAQ,MAAM;YAChB,OAAO;QACT;QACA,OAAO;IACT;IACA,WAAW,SAAS,CAAC,YAAY,GAAG,SAAU,MAAM,EAAE,GAAG;QACvD,IAAI,UAAU,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,QAAQ;QACtC,IAAI,cAAc,IAAI,CAAC,MAAM,CAAC,cAAc,CAAC;QAC7C,IAAI,aAAa;YACf,OAAO,YAAY,UAAU,CAAC,QAAQ;QACxC;QACA,OAAO;IACT;IACA;;;;;GAKC,GACD,WAAW,SAAS,CAAC,KAAK,GAAG,SAAU,GAAG;QACxC,OAAO,MAAM,IAAI,EAAE,IAAI,CAAC,WAAW,CAAC;IACtC;IACA,WAAW,SAAS,CAAC,KAAK,GAAG;QAC3B,OAAO,IAAI,CAAC,MAAM,CAAC,KAAK;IAC1B;IACA;;;;GAIC,GACD,WAAW,SAAS,CAAC,GAAG,GAAG,SAAU,GAAG,EAAE,GAAG;QAC3C,IAAI,QAAQ,IAAI,CAAC,MAAM;QACvB,IAAI,UAAU,IAAI,CAAC,SAAS,CAAC,IAAI;QACjC,IAAI,SAAS;YACX,OAAO,MAAM,GAAG,CAAC,QAAQ,aAAa,EAAE;QAC1C;IACF;IACA;;GAEC,GACD,WAAW,SAAS,CAAC,aAAa,GAAG,SAAU,GAAG,EAAE,MAAM;QACxD,IAAI,QAAQ,IAAI,CAAC,MAAM;QACvB,IAAI,UAAU,IAAI,CAAC,SAAS,CAAC,IAAI;QACjC,IAAI,SAAS;YACX,OAAO,MAAM,aAAa,CAAC,QAAQ,aAAa,EAAE;QACpD;IACF;IACA,WAAW,SAAS,CAAC,UAAU,GAAG;QAChC,OAAO,IAAI,CAAC,MAAM,CAAC,UAAU;IAC/B;IACA,WAAW,SAAS,CAAC,aAAa,GAAG,SAAU,GAAG;QAChD,OAAO,IAAI,CAAC,MAAM,CAAC,aAAa,CAAC,IAAI,CAAC,iBAAiB,CAAC;IAC1D;IACA,WAAW,SAAS,CAAC,MAAM,GAAG,SAAU,GAAG;QACzC,OAAO,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,iBAAiB,CAAC;IACnD;IACA,WAAW,SAAS,CAAC,SAAS,GAAG,SAAU,GAAG;QAC5C,OAAO,IAAI,CAAC,MAAM,CAAC,SAAS,CAAC,IAAI,CAAC,iBAAiB,CAAC;IACtD;IACA,WAAW,SAAS,CAAC,SAAS,GAAG,SAAU,UAAU,EAAE,GAAG;QACxD,IAAI,QAAQ,IAAI;QAChB,IAAI,QAAQ,IAAI,CAAC,MAAM;QACvB,OAAO,CAAA,GAAA,8IAAA,CAAA,UAAc,AAAD,EAAE,cAAc,MAAM,SAAS,CAAC,IAAI,YAAY,SAAU,GAAG;YAC/E,OAAO,MAAM,iBAAiB,CAAC;QACjC,IAAI,OAAO,MAAM,SAAS,CAAC;IAC7B;IACA;;;GAGC,GACD,WAAW,SAAS,CAAC,QAAQ,GAAG,SAAU,GAAG;QAC3C,IAAI,wBAAwB,IAAI,CAAC,WAAW,CAAC,qBAAqB;QAClE,IAAK,IAAI,IAAI,GAAG,MAAM,sBAAsB,MAAM,EAAE,IAAI,KAAK,IAAK;YAChE,mDAAmD;YACnD,oDAAoD;YACpD,2DAA2D;YAC3D,IAAI,MAAM,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,qBAAqB,CAAC,EAAE,EAAE,OAAO;gBACzD,OAAO;YACT;QACF;QACA,OAAO;IACT;IACA;;GAEC,GACD,WAAW,SAAS,CAAC,WAAW,GAAG,SAAU,IAAI;QAC/C,IAAK,IAAI,IAAI,GAAG,MAAM,IAAI,CAAC,MAAM,CAAC,KAAK,IAAI,IAAI,KAAK,IAAK;YACvD,IAAI,IAAI,CAAC,OAAO,CAAC,OAAO,MAAM;gBAC5B,OAAO;YACT;QACF;QACA,OAAO,CAAC;IACV;IACA,WAAW,SAAS,CAAC,WAAW,GAAG,SAAU,GAAG;QAC9C,OAAO,IAAI,CAAC,MAAM,CAAC,WAAW,CAAC;IACjC;IACA,WAAW,SAAS,CAAC,eAAe,GAAG,SAAU,QAAQ;QACvD,OAAO,IAAI,CAAC,MAAM,CAAC,eAAe,CAAC;IACrC;IACA;;;;;;GAMC,GACD,WAAW,SAAS,CAAC,UAAU,GAAG,SAAU,GAAG,EAAE,KAAK;QACpD,IAAI,kBAAkB,OAAO,IAAI,CAAC,mBAAmB,CAAC,IAAI;QAC1D,wCAA2C;YACzC,IAAI,CAAC,iBAAiB;gBACpB,MAAM,IAAI,MAAM;YAClB;QACF;QACA,IAAI,WAAW,mBAAmB,eAAe,CAAC,MAAM;QACxD,IAAI,YAAY,QAAQ,MAAM,WAAW;YACvC,OAAO;QACT;QACA,OAAO;IACT;IACA;;;;;;;GAOC,GACD,WAAW,SAAS,CAAC,gBAAgB,GAAG,SAAU,GAAG,EAAE,KAAK,EAAE,WAAW;QACvE,OAAO,IAAI,CAAC,MAAM,CAAC,gBAAgB,CAAC,IAAI,CAAC,iBAAiB,CAAC,MAAM,OAAO;IAC1E;IACA,WAAW,SAAS,CAAC,IAAI,GAAG,SAAU,IAAI,EAAE,EAAE,EAAE,GAAG;QACjD;QAEA,IAAI,CAAA,GAAA,8IAAA,CAAA,aAAiB,AAAD,EAAE,OAAO;YAC3B,MAAM;YACN,KAAK;YACL,OAAO,EAAE;QACX;QACA,qCAAqC;QACrC,IAAI,OAAO,OAAO,IAAI;QACtB,IAAI,aAAa,IAAI,oBAAoB,OAAO,IAAI,CAAC,iBAAiB,EAAE,IAAI;QAC5E,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,YAAY,OAAO,CAAA,GAAA,8IAAA,CAAA,OAAW,AAAD,EAAE,IAAI,QAAQ;IAC9D;IACA,WAAW,SAAS,CAAC,UAAU,GAAG,SAAU,IAAI,EAAE,EAAE,EAAE,GAAG;QACvD;QAEA,IAAI,CAAA,GAAA,8IAAA,CAAA,aAAiB,AAAD,EAAE,OAAO;YAC3B,MAAM;YACN,KAAK;YACL,OAAO,EAAE;QACX;QACA,qCAAqC;QACrC,IAAI,OAAO,OAAO,IAAI;QACtB,IAAI,aAAa,IAAI,oBAAoB,OAAO,IAAI,CAAC,iBAAiB,EAAE,IAAI;QAC5E,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,YAAY,OAAO,CAAA,GAAA,8IAAA,CAAA,OAAW,AAAD,EAAE,IAAI,QAAQ;QAC5E,OAAO,IAAI;IACb;IACA;;;GAGC,GACD,WAAW,SAAS,CAAC,WAAW,GAAG,SAAU,KAAK;QAChD;QAEA,IAAI,QAAQ,IAAI;QAChB,IAAI,aAAa,CAAC;QAClB,IAAI,OAAO,CAAA,GAAA,8IAAA,CAAA,OAAW,AAAD,EAAE;QACvB,IAAI,aAAa,EAAE;QACnB,CAAA,GAAA,8IAAA,CAAA,OAAW,AAAD,EAAE,MAAM,SAAU,GAAG;YAC7B,IAAI,SAAS,MAAM,iBAAiB,CAAC;YACrC,UAAU,CAAC,OAAO,GAAG,KAAK,CAAC,IAAI;YAC/B,WAAW,IAAI,CAAC;QAClB;QACA,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC,MAAM,CAAC,WAAW,CAAC;QACtC,OAAO,IAAI;IACb;IACA,yBAAyB,GACzB,WAAW,SAAS,CAAC,QAAQ,GAAG,SAAU,IAAI,EAAE,EAAE,EAAE,GAAG;QACrD;QAEA,IAAI,CAAA,GAAA,8IAAA,CAAA,aAAiB,AAAD,EAAE,OAAO;YAC3B,MAAM;YACN,KAAK;YACL,OAAO,EAAE;QACX;QACA,qCAAqC;QACrC,MAAM,OAAO,IAAI;QACjB,IAAI,SAAS,EAAE;QACf,IAAI,CAAC,IAAI,CAAC,MAAM;YACd,OAAO,IAAI,CAAC,MAAM,GAAG,KAAK,CAAC,IAAI,EAAE;QACnC,GAAG;QACH,OAAO;IACT;IACA,WAAW,SAAS,CAAC,GAAG,GAAG,SAAU,IAAI,EAAE,EAAE,EAAE,GAAG,EAAE,SAAS;QAC3D;QAEA,qCAAqC;QACrC,IAAI,OAAO,OAAO,aAAa,IAAI;QACnC,IAAI,aAAa,IAAI,oBAAoB,OAAO,IAAI,CAAC,iBAAiB,EAAE,IAAI;QAC5E,IAAI,OAAO,yBAAyB,IAAI;QACxC,KAAK,MAAM,GAAG,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,YAAY,OAAO,CAAA,GAAA,8IAAA,CAAA,OAAW,AAAD,EAAE,IAAI,QAAQ;QACzE,OAAO;IACT;IACA,WAAW,SAAS,CAAC,MAAM,GAAG,SAAU,IAAI,EAAE,EAAE,EAAE,GAAG,EAAE,SAAS;QAC9D,IAAI,QAAQ,IAAI;QAChB,qCAAqC;QACrC,IAAI,OAAO,OAAO,aAAa,IAAI;QACnC,wCAA2C;YACzC,CAAA,GAAA,8IAAA,CAAA,OAAW,AAAD,EAAE,oBAAoB,OAAO,SAAU,GAAG;gBAClD,IAAI,UAAU,MAAM,gBAAgB,CAAC;gBACrC,IAAI,CAAC,QAAQ,kBAAkB,EAAE;oBAC/B,QAAQ,KAAK,CAAC;gBAChB;YACF;QACF;QACA,IAAI,aAAa,IAAI,oBAAoB,OAAO,IAAI,CAAC,iBAAiB,EAAE,IAAI;QAC5E,kEAAkE;QAClE,4EAA4E;QAC5E,2EAA2E;QAC3E,4BAA4B;QAC5B,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,YAAY,OAAO,CAAA,GAAA,8IAAA,CAAA,OAAW,AAAD,EAAE,IAAI,QAAQ;IAChE;IACA;;;GAGC,GACD,WAAW,SAAS,CAAC,UAAU,GAAG,SAAU,SAAS,EAAE,IAAI,EAAE,WAAW,EAAE,WAAW;QACnF,IAAI,OAAO,yBAAyB,IAAI;QACxC,KAAK,MAAM,GAAG,IAAI,CAAC,MAAM,CAAC,UAAU,CAAC,IAAI,CAAC,iBAAiB,CAAC,YAAY,MAAM,aAAa;QAC3F,OAAO;IACT;IACA;;;;GAIC,GACD,WAAW,SAAS,CAAC,gBAAgB,GAAG,SAAU,cAAc,EAAE,IAAI;QACpE,IAAI,OAAO,yBAAyB,IAAI;QACxC,KAAK,MAAM,GAAG,IAAI,CAAC,MAAM,CAAC,gBAAgB,CAAC,IAAI,CAAC,iBAAiB,CAAC,iBAAiB;QACnF,OAAO;IACT;IACA;;;;GAIC,GACD,WAAW,SAAS,CAAC,cAAc,GAAG,SAAU,cAAc,EAAE,IAAI;QAClE,IAAI,OAAO,yBAAyB,IAAI;QACxC,KAAK,MAAM,GAAG,IAAI,CAAC,MAAM,CAAC,cAAc,CAAC,IAAI,CAAC,iBAAiB,CAAC,iBAAiB;QACjF,OAAO;IACT;IACA,WAAW,SAAS,CAAC,cAAc,GAAG,SAAU,GAAG;QACjD,OAAO,IAAI,CAAC,MAAM,CAAC,cAAc,CAAC;IACpC;IACA;;GAEC,GACD,0BAA0B;IAC1B,WAAW,SAAS,CAAC,YAAY,GAAG,SAAU,GAAG;QAC/C,IAAI,YAAY,IAAI,CAAC,SAAS;QAC9B,IAAI,WAAW,IAAI,CAAC,cAAc,CAAC;QACnC,OAAO,IAAI,gJAAA,CAAA,UAAK,CAAC,UAAU,WAAW,aAAa,UAAU,OAAO;IACtE;IACA;;GAEC,GACD,WAAW,SAAS,CAAC,IAAI,GAAG,SAAU,SAAS;QAC7C,IAAI,WAAW,IAAI;QACnB,OAAO,IAAI,oJAAA,CAAA,UAAU,CAAC,YAAY,UAAU,QAAQ,GAAG,UAAU,KAAK,EAAE,EAAE,IAAI,CAAC,QAAQ,GAAG,UAAU,IAAI,SAAU,GAAG;YACnH,OAAO,MAAM,WAAW;QAC1B,GAAG,SAAU,GAAG;YACd,OAAO,MAAM,UAAU;QACzB;IACF;IACA;;GAEC,GACD,WAAW,SAAS,CAAC,SAAS,GAAG,SAAU,GAAG;QAC5C,IAAI,SAAS,IAAI,CAAC,OAAO;QACzB,OAAO,UAAU,MAAM,CAAC,IAAI;IAC9B;IACA,WAAW,SAAS,CAAC,SAAS,GAAG,SAAU,KAAK,EAAE,GAAG;QACnD,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC,OAAO,IAAI,CAAC;QAChC,IAAI,SAAS,QAAQ;YACnB,CAAA,GAAA,8IAAA,CAAA,SAAa,AAAD,EAAE,IAAI,CAAC,OAAO,EAAE;QAC9B,OAAO;YACL,IAAI,CAAC,OAAO,CAAC,MAAM,GAAG;QACxB;IACF;IACA;;GAEC,GACD,2BAA2B;IAC3B,WAAW,SAAS,CAAC,aAAa,GAAG,SAAU,GAAG,EAAE,GAAG;QACrD,IAAI,aAAa,IAAI,CAAC,YAAY,CAAC,IAAI;QACvC,IAAI,MAAM,cAAc,UAAU,CAAC,IAAI;QACvC,IAAI,OAAO,MAAM;YACf,6BAA6B;YAC7B,OAAO,IAAI,CAAC,SAAS,CAAC;QACxB;QACA,OAAO;IACT;IACA;;GAEC,GACD,WAAW,SAAS,CAAC,aAAa,GAAG;QACnC,OAAO,IAAI,CAAC,YAAY,CAAC,MAAM,GAAG;IACpC;IACA;;GAEC,GACD,iDAAiD;IACjD,WAAW,SAAS,CAAC,sBAAsB,GAAG,SAAU,GAAG,EAAE,GAAG;QAC9D,IAAI,cAAc,IAAI,CAAC,YAAY;QACnC,IAAI,aAAa,WAAW,CAAC,IAAI;QACjC,IAAI,CAAC,YAAY;YACf,aAAa,WAAW,CAAC,IAAI,GAAG,CAAC;QACnC;QACA,IAAI,MAAM,UAAU,CAAC,IAAI;QACzB,IAAI,OAAO,MAAM;YACf,MAAM,IAAI,CAAC,SAAS,CAAC;YACrB,oBAAoB;YACpB,IAAI,CAAA,GAAA,8IAAA,CAAA,UAAc,AAAD,EAAE,MAAM;gBACvB,MAAM,IAAI,KAAK;YACjB,OAAO,IAAI,SAAS,MAAM;gBACxB,MAAM,CAAA,GAAA,8IAAA,CAAA,SAAa,AAAD,EAAE,CAAC,GAAG;YAC1B;YACA,UAAU,CAAC,IAAI,GAAG;QACpB;QACA,OAAO;IACT;IACA,2BAA2B;IAC3B,WAAW,SAAS,CAAC,aAAa,GAAG,SAAU,GAAG,EAAE,GAAG,EAAE,KAAK;QAC5D,IAAI,aAAa,IAAI,CAAC,YAAY,CAAC,IAAI,IAAI,CAAC;QAC5C,IAAI,CAAC,YAAY,CAAC,IAAI,GAAG;QACzB,IAAI,SAAS,MAAM;YACjB,CAAA,GAAA,8IAAA,CAAA,SAAa,AAAD,EAAE,YAAY;QAC5B,OAAO;YACL,UAAU,CAAC,IAAI,GAAG;QACpB;IACF;IACA;;GAEC,GACD,WAAW,SAAS,CAAC,cAAc,GAAG;QACpC,IAAI,CAAC,OAAO,GAAG,CAAC;QAChB,IAAI,CAAC,YAAY,GAAG,EAAE;IACxB;IACA,WAAW,SAAS,CAAC,SAAS,GAAG,SAAU,GAAG,EAAE,GAAG;QACjD,SAAS,OAAO,CAAA,GAAA,8IAAA,CAAA,SAAa,AAAD,EAAE,IAAI,CAAC,OAAO,EAAE,OAAO,IAAI,CAAC,OAAO,CAAC,IAAI,GAAG;IACzE;IACA;;GAEC,GACD,WAAW,SAAS,CAAC,SAAS,GAAG,SAAU,GAAG;QAC5C,OAAO,IAAI,CAAC,OAAO,CAAC,IAAI;IAC1B;IACA;;GAEC,GACD,WAAW,SAAS,CAAC,aAAa,GAAG,SAAU,GAAG;QAChD,OAAO,IAAI,CAAC,YAAY,CAAC,IAAI;IAC/B;IACA;;GAEC,GACD,WAAW,SAAS,CAAC,aAAa,GAAG,SAAU,GAAG,EAAE,MAAM,EAAE,KAAK;QAC/D,IAAI,CAAC,YAAY,CAAC,IAAI,GAAG,QAAQ,CAAA,GAAA,8IAAA,CAAA,SAAa,AAAD,EAAE,IAAI,CAAC,YAAY,CAAC,IAAI,IAAI,CAAC,GAAG,UAAU;IACzF;IACA;;GAEC,GACD,WAAW,SAAS,CAAC,gBAAgB,GAAG;QACtC,IAAI,CAAC,YAAY,CAAC,MAAM,GAAG;IAC7B;IACA;;GAEC,GACD,WAAW,SAAS,CAAC,gBAAgB,GAAG,SAAU,GAAG,EAAE,EAAE;QACvD,IAAI,cAAc,IAAI,CAAC,SAAS,IAAI,IAAI,CAAC,SAAS,CAAC,WAAW;QAC9D,CAAA,GAAA,oJAAA,CAAA,kBAAe,AAAD,EAAE,aAAa,IAAI,CAAC,QAAQ,EAAE,KAAK;QACjD,IAAI,CAAC,WAAW,CAAC,IAAI,GAAG;IAC1B;IACA,WAAW,SAAS,CAAC,gBAAgB,GAAG,SAAU,GAAG;QACnD,OAAO,IAAI,CAAC,WAAW,CAAC,IAAI;IAC9B;IACA,WAAW,SAAS,CAAC,iBAAiB,GAAG,SAAU,EAAE,EAAE,OAAO;QAC5D,CAAA,GAAA,8IAAA,CAAA,OAAW,AAAD,EAAE,IAAI,CAAC,WAAW,EAAE,SAAU,EAAE,EAAE,GAAG;YAC7C,IAAI,IAAI;gBACN,MAAM,GAAG,IAAI,CAAC,SAAS,IAAI;YAC7B;QACF;IACF;IACA;;;GAGC,GACD,WAAW,SAAS,CAAC,YAAY,GAAG,SAAU,IAAI;QAChD,IAAI,CAAC,MAAM;YACT,OAAO,IAAI,WAAW,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC,OAAO,GAAG,IAAI,IAAI,CAAC,UAAU,EAAE,IAAI,CAAC,WAAW,EAAE,IAAI,GAAG,IAAI,CAAC,SAAS;QAClH;QACA,mBAAmB,MAAM,IAAI;QAC7B,KAAK,MAAM,GAAG,IAAI,CAAC,MAAM;QACzB,OAAO;IACT;IACA;;GAEC,GACD,WAAW,SAAS,CAAC,UAAU,GAAG,SAAU,UAAU,EAAE,cAAc;QACpE,IAAI,iBAAiB,IAAI,CAAC,WAAW;QACrC,IAAI,CAAC,CAAA,GAAA,8IAAA,CAAA,aAAiB,AAAD,EAAE,iBAAiB;YACtC;QACF;QACA,IAAI,CAAC,gBAAgB,GAAG,IAAI,CAAC,gBAAgB,IAAI,EAAE;QACnD,IAAI,CAAC,gBAAgB,CAAC,IAAI,CAAC;QAC3B,IAAI,CAAC,WAAW,GAAG;YACjB,IAAI,MAAM,eAAe,KAAK,CAAC,IAAI,EAAE;YACrC,OAAO,eAAe,KAAK,CAAC,IAAI,EAAE;gBAAC;aAAI,CAAC,MAAM,CAAC,CAAA,GAAA,8IAAA,CAAA,QAAY,AAAD,EAAE;QAC9D;IACF;IACA,6DAA6D;IAC7D,6DAA6D;IAC7D,6DAA6D;IAC7D,WAAW,aAAa,GAAG;QACzB,uBAAuB,SAAU,IAAI;YACnC,IAAI,qBAAqB,KAAK,mBAAmB;YACjD,CAAA,GAAA,8IAAA,CAAA,OAAW,AAAD,EAAE,oBAAoB,SAAU,eAAe,EAAE,GAAG;gBAC5D,IAAI,UAAU,KAAK,SAAS,CAAC,IAAI;gBACjC,+EAA+E;gBAC/E,IAAI,cAAc,QAAQ,WAAW;gBACrC,IAAI,QAAQ,KAAK,MAAM;gBACvB,IAAI,aAAa;oBACf,kBAAkB,kBAAkB,CAAC,IAAI,GAAG,IAAI,eAAe,YAAY,UAAU,CAAC,MAAM;oBAC5F,sDAAsD;oBACtD,qDAAqD;oBACrD,IAAK,IAAI,IAAI,GAAG,IAAI,gBAAgB,MAAM,EAAE,IAAK;wBAC/C,eAAe,CAAC,EAAE,GAAG;oBACvB;oBACA,IAAK,IAAI,IAAI,GAAG,IAAI,MAAM,KAAK,IAAI,IAAK;wBACtC,sDAAsD;wBACtD,eAAe,CAAC,MAAM,GAAG,CAAC,QAAQ,aAAa,EAAE,GAAG,GAAG;oBACzD;gBACF;YACF;QACF;QACA,qBAAqB,SAAU,IAAI,EAAE,MAAM,EAAE,GAAG;YAC9C,OAAO,CAAA,GAAA,+IAAA,CAAA,sBAAmB,AAAD,EAAE,KAAK,YAAY,CAAC,QAAQ,MAAM;QAC7D;QACA;;KAEC,GACD,QAAQ,SAAU,IAAI,EAAE,QAAQ;YAC9B,IAAI,KAAK,KAAK,OAAO,CAAC,SAAS;YAC/B,IAAI,MAAM,QAAQ,KAAK,SAAS,IAAI,MAAM;gBACxC,KAAK,mBAAmB,MAAM,KAAK,SAAS,EAAE;YAChD;YACA,IAAI,MAAM,MAAM;gBACd,KAAK,YAAY;YACnB;YACA,OAAO;QACT;QACA,sBAAsB,SAAU,UAAU;YACxC,IAAI,CAAC,CAAA,GAAA,8IAAA,CAAA,UAAc,AAAD,EAAE,aAAa;gBAC/B,aAAa,cAAc,OAAO;oBAAC;iBAAW,GAAG,EAAE;YACrD;YACA,OAAO;QACT;QACA;;KAEC,GACD,2BAA2B,SAAU,QAAQ;YAC3C,IAAI,OAAO,IAAI,WAAW,SAAS,OAAO,GAAG,SAAS,OAAO,GAAG,IAAI,SAAS,UAAU,EAAE,SAAS,WAAW,EAAE,WAAW,SAAS,SAAS;YAC5I,2DAA2D;YAC3D,mBAAmB,MAAM;YACzB,OAAO;QACT;QACA,qBAAqB,SAAU,MAAM,EAAE,MAAM;YAC3C,CAAA,GAAA,8IAAA,CAAA,OAAW,AAAD,EAAE,wBAAwB,MAAM,CAAC,OAAO,gBAAgB,IAAI,EAAE,GAAG,SAAU,QAAQ;gBAC3F,IAAI,OAAO,cAAc,CAAC,WAAW;oBACnC,MAAM,CAAC,SAAS,GAAG,MAAM,CAAC,SAAS;gBACrC;YACF;YACA,OAAO,gBAAgB,GAAG,OAAO,gBAAgB;YACjD,CAAA,GAAA,8IAAA,CAAA,OAAW,AAAD,EAAE,kBAAkB,SAAU,QAAQ;gBAC9C,MAAM,CAAC,SAAS,GAAG,CAAA,GAAA,8IAAA,CAAA,QAAY,AAAD,EAAE,MAAM,CAAC,SAAS;YAClD;YACA,OAAO,gBAAgB,GAAG,CAAA,GAAA,8IAAA,CAAA,SAAa,AAAD,EAAE,CAAC,GAAG,OAAO,gBAAgB;QACrE;QACA,iBAAiB,SAAU,IAAI,EAAE,GAAG;YAClC,IAAI,WAAW,KAAK,SAAS;YAC7B,IAAI,SAAS,KAAK,OAAO;YACzB,IAAI,aAAa,KAAK,WAAW;YACjC,IAAI,WAAW,KAAK,SAAS;YAC7B,IAAI,OAAO,QAAQ,CAAC,IAAI;YACxB,IAAI,KAAK,MAAM,CAAC,IAAI;YACpB,IAAI,QAAQ,QAAQ,cAAc,MAAM;gBACtC,QAAQ,CAAC,IAAI,GAAG,OAAO,mBAAmB,MAAM,YAAY;YAC9D;YACA,IAAI,MAAM,QAAQ,YAAY,MAAM;gBAClC,MAAM,CAAC,IAAI,GAAG,KAAK,mBAAmB,MAAM,UAAU;YACxD;YACA,IAAI,MAAM,QAAQ,QAAQ,MAAM;gBAC9B,IAAI,kBAAkB,KAAK,gBAAgB;gBAC3C,IAAI,QAAQ,eAAe,CAAC,KAAK,GAAG,CAAC,eAAe,CAAC,KAAK,IAAI,CAAC,IAAI;gBACnE,KAAK;gBACL,IAAI,QAAQ,GAAG;oBACb,MAAM,WAAW;gBACnB;gBACA,MAAM,CAAC,IAAI,GAAG;YAChB;QACF;IACF;IACA,OAAO;AACT;uCACe", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 4783, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/tindahan/sari-sari-admin/node_modules/echarts/lib/data/helper/createDimensions.js"], "sourcesContent": ["\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\r\n* Licensed to the Apache Software Foundation (ASF) under one\r\n* or more contributor license agreements.  See the NOTICE file\r\n* distributed with this work for additional information\r\n* regarding copyright ownership.  The ASF licenses this file\r\n* to you under the Apache License, Version 2.0 (the\r\n* \"License\"); you may not use this file except in compliance\r\n* with the License.  You may obtain a copy of the License at\r\n*\r\n*   http://www.apache.org/licenses/LICENSE-2.0\r\n*\r\n* Unless required by applicable law or agreed to in writing,\r\n* software distributed under the License is distributed on an\r\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\r\n* KIND, either express or implied.  See the License for the\r\n* specific language governing permissions and limitations\r\n* under the License.\r\n*/\nimport { VISUAL_DIMENSIONS } from '../../util/types.js';\nimport SeriesDimensionDefine from '../SeriesDimensionDefine.js';\nimport { createHashMap, defaults, each, extend, isObject, isString } from 'zrender/lib/core/util.js';\nimport { createSourceFromSeriesDataOption, isSourceInstance } from '../Source.js';\nimport { CtorInt32Array } from '../DataStore.js';\nimport { normalizeToArray } from '../../util/model.js';\nimport { BE_ORDINAL, guessOrdinal } from './sourceHelper.js';\nimport { createDimNameMap, ensureSourceDimNameMap, SeriesDataSchema, shouldOmitUnusedDimensions } from './SeriesDataSchema.js';\n/**\r\n * For outside usage compat (like echarts-gl are using it).\r\n */\nexport function createDimensions(source, opt) {\n  return prepareSeriesDataSchema(source, opt).dimensions;\n}\n/**\r\n * This method builds the relationship between:\r\n * + \"what the coord sys or series requires (see `coordDimensions`)\",\r\n * + \"what the user defines (in `encode` and `dimensions`, see `opt.dimensionsDefine` and `opt.encodeDefine`)\"\r\n * + \"what the data source provids (see `source`)\".\r\n *\r\n * Some guess strategy will be adapted if user does not define something.\r\n * If no 'value' dimension specified, the first no-named dimension will be\r\n * named as 'value'.\r\n *\r\n * @return The results are always sorted by `storeDimIndex` asc.\r\n */\nexport default function prepareSeriesDataSchema(\n// TODO: TYPE completeDimensions type\nsource, opt) {\n  if (!isSourceInstance(source)) {\n    source = createSourceFromSeriesDataOption(source);\n  }\n  opt = opt || {};\n  var sysDims = opt.coordDimensions || [];\n  var dimsDef = opt.dimensionsDefine || source.dimensionsDefine || [];\n  var coordDimNameMap = createHashMap();\n  var resultList = [];\n  var dimCount = getDimCount(source, sysDims, dimsDef, opt.dimensionsCount);\n  // Try to ignore unused dimensions if sharing a high dimension datastore\n  // 30 is an experience value.\n  var omitUnusedDimensions = opt.canOmitUnusedDimensions && shouldOmitUnusedDimensions(dimCount);\n  var isUsingSourceDimensionsDef = dimsDef === source.dimensionsDefine;\n  var dataDimNameMap = isUsingSourceDimensionsDef ? ensureSourceDimNameMap(source) : createDimNameMap(dimsDef);\n  var encodeDef = opt.encodeDefine;\n  if (!encodeDef && opt.encodeDefaulter) {\n    encodeDef = opt.encodeDefaulter(source, dimCount);\n  }\n  var encodeDefMap = createHashMap(encodeDef);\n  var indicesMap = new CtorInt32Array(dimCount);\n  for (var i = 0; i < indicesMap.length; i++) {\n    indicesMap[i] = -1;\n  }\n  function getResultItem(dimIdx) {\n    var idx = indicesMap[dimIdx];\n    if (idx < 0) {\n      var dimDefItemRaw = dimsDef[dimIdx];\n      var dimDefItem = isObject(dimDefItemRaw) ? dimDefItemRaw : {\n        name: dimDefItemRaw\n      };\n      var resultItem = new SeriesDimensionDefine();\n      var userDimName = dimDefItem.name;\n      if (userDimName != null && dataDimNameMap.get(userDimName) != null) {\n        // Only if `series.dimensions` is defined in option\n        // displayName, will be set, and dimension will be displayed vertically in\n        // tooltip by default.\n        resultItem.name = resultItem.displayName = userDimName;\n      }\n      dimDefItem.type != null && (resultItem.type = dimDefItem.type);\n      dimDefItem.displayName != null && (resultItem.displayName = dimDefItem.displayName);\n      var newIdx = resultList.length;\n      indicesMap[dimIdx] = newIdx;\n      resultItem.storeDimIndex = dimIdx;\n      resultList.push(resultItem);\n      return resultItem;\n    }\n    return resultList[idx];\n  }\n  if (!omitUnusedDimensions) {\n    for (var i = 0; i < dimCount; i++) {\n      getResultItem(i);\n    }\n  }\n  // Set `coordDim` and `coordDimIndex` by `encodeDefMap` and normalize `encodeDefMap`.\n  encodeDefMap.each(function (dataDimsRaw, coordDim) {\n    var dataDims = normalizeToArray(dataDimsRaw).slice();\n    // Note: It is allowed that `dataDims.length` is `0`, e.g., options is\n    // `{encode: {x: -1, y: 1}}`. Should not filter anything in\n    // this case.\n    if (dataDims.length === 1 && !isString(dataDims[0]) && dataDims[0] < 0) {\n      encodeDefMap.set(coordDim, false);\n      return;\n    }\n    var validDataDims = encodeDefMap.set(coordDim, []);\n    each(dataDims, function (resultDimIdxOrName, idx) {\n      // The input resultDimIdx can be dim name or index.\n      var resultDimIdx = isString(resultDimIdxOrName) ? dataDimNameMap.get(resultDimIdxOrName) : resultDimIdxOrName;\n      if (resultDimIdx != null && resultDimIdx < dimCount) {\n        validDataDims[idx] = resultDimIdx;\n        applyDim(getResultItem(resultDimIdx), coordDim, idx);\n      }\n    });\n  });\n  // Apply templates and default order from `sysDims`.\n  var availDimIdx = 0;\n  each(sysDims, function (sysDimItemRaw) {\n    var coordDim;\n    var sysDimItemDimsDef;\n    var sysDimItemOtherDims;\n    var sysDimItem;\n    if (isString(sysDimItemRaw)) {\n      coordDim = sysDimItemRaw;\n      sysDimItem = {};\n    } else {\n      sysDimItem = sysDimItemRaw;\n      coordDim = sysDimItem.name;\n      var ordinalMeta = sysDimItem.ordinalMeta;\n      sysDimItem.ordinalMeta = null;\n      sysDimItem = extend({}, sysDimItem);\n      sysDimItem.ordinalMeta = ordinalMeta;\n      // `coordDimIndex` should not be set directly.\n      sysDimItemDimsDef = sysDimItem.dimsDef;\n      sysDimItemOtherDims = sysDimItem.otherDims;\n      sysDimItem.name = sysDimItem.coordDim = sysDimItem.coordDimIndex = sysDimItem.dimsDef = sysDimItem.otherDims = null;\n    }\n    var dataDims = encodeDefMap.get(coordDim);\n    // negative resultDimIdx means no need to mapping.\n    if (dataDims === false) {\n      return;\n    }\n    dataDims = normalizeToArray(dataDims);\n    // dimensions provides default dim sequences.\n    if (!dataDims.length) {\n      for (var i = 0; i < (sysDimItemDimsDef && sysDimItemDimsDef.length || 1); i++) {\n        while (availDimIdx < dimCount && getResultItem(availDimIdx).coordDim != null) {\n          availDimIdx++;\n        }\n        availDimIdx < dimCount && dataDims.push(availDimIdx++);\n      }\n    }\n    // Apply templates.\n    each(dataDims, function (resultDimIdx, coordDimIndex) {\n      var resultItem = getResultItem(resultDimIdx);\n      // Coordinate system has a higher priority on dim type than source.\n      if (isUsingSourceDimensionsDef && sysDimItem.type != null) {\n        resultItem.type = sysDimItem.type;\n      }\n      applyDim(defaults(resultItem, sysDimItem), coordDim, coordDimIndex);\n      if (resultItem.name == null && sysDimItemDimsDef) {\n        var sysDimItemDimsDefItem = sysDimItemDimsDef[coordDimIndex];\n        !isObject(sysDimItemDimsDefItem) && (sysDimItemDimsDefItem = {\n          name: sysDimItemDimsDefItem\n        });\n        resultItem.name = resultItem.displayName = sysDimItemDimsDefItem.name;\n        resultItem.defaultTooltip = sysDimItemDimsDefItem.defaultTooltip;\n      }\n      // FIXME refactor, currently only used in case: {otherDims: {tooltip: false}}\n      sysDimItemOtherDims && defaults(resultItem.otherDims, sysDimItemOtherDims);\n    });\n  });\n  function applyDim(resultItem, coordDim, coordDimIndex) {\n    if (VISUAL_DIMENSIONS.get(coordDim) != null) {\n      resultItem.otherDims[coordDim] = coordDimIndex;\n    } else {\n      resultItem.coordDim = coordDim;\n      resultItem.coordDimIndex = coordDimIndex;\n      coordDimNameMap.set(coordDim, true);\n    }\n  }\n  // Make sure the first extra dim is 'value'.\n  var generateCoord = opt.generateCoord;\n  var generateCoordCount = opt.generateCoordCount;\n  var fromZero = generateCoordCount != null;\n  generateCoordCount = generateCoord ? generateCoordCount || 1 : 0;\n  var extra = generateCoord || 'value';\n  function ifNoNameFillWithCoordName(resultItem) {\n    if (resultItem.name == null) {\n      // Duplication will be removed in the next step.\n      resultItem.name = resultItem.coordDim;\n    }\n  }\n  // Set dim `name` and other `coordDim` and other props.\n  if (!omitUnusedDimensions) {\n    for (var resultDimIdx = 0; resultDimIdx < dimCount; resultDimIdx++) {\n      var resultItem = getResultItem(resultDimIdx);\n      var coordDim = resultItem.coordDim;\n      if (coordDim == null) {\n        // TODO no need to generate coordDim for isExtraCoord?\n        resultItem.coordDim = genCoordDimName(extra, coordDimNameMap, fromZero);\n        resultItem.coordDimIndex = 0;\n        // Series specified generateCoord is using out.\n        if (!generateCoord || generateCoordCount <= 0) {\n          resultItem.isExtraCoord = true;\n        }\n        generateCoordCount--;\n      }\n      ifNoNameFillWithCoordName(resultItem);\n      if (resultItem.type == null && (guessOrdinal(source, resultDimIdx) === BE_ORDINAL.Must\n      // Consider the case:\n      // {\n      //    dataset: {source: [\n      //        ['2001', 123],\n      //        ['2002', 456],\n      //        ...\n      //        ['The others', 987],\n      //    ]},\n      //    series: {type: 'pie'}\n      // }\n      // The first column should better be treated as a \"ordinal\" although it\n      // might not be detected as an \"ordinal\" by `guessOrdinal`.\n      || resultItem.isExtraCoord && (resultItem.otherDims.itemName != null || resultItem.otherDims.seriesName != null))) {\n        resultItem.type = 'ordinal';\n      }\n    }\n  } else {\n    each(resultList, function (resultItem) {\n      // PENDING: guessOrdinal or let user specify type: 'ordinal' manually?\n      ifNoNameFillWithCoordName(resultItem);\n    });\n    // Sort dimensions: there are some rule that use the last dim as label,\n    // and for some latter travel process easier.\n    resultList.sort(function (item0, item1) {\n      return item0.storeDimIndex - item1.storeDimIndex;\n    });\n  }\n  removeDuplication(resultList);\n  return new SeriesDataSchema({\n    source: source,\n    dimensions: resultList,\n    fullDimensionCount: dimCount,\n    dimensionOmitted: omitUnusedDimensions\n  });\n}\nfunction removeDuplication(result) {\n  var duplicationMap = createHashMap();\n  for (var i = 0; i < result.length; i++) {\n    var dim = result[i];\n    var dimOriginalName = dim.name;\n    var count = duplicationMap.get(dimOriginalName) || 0;\n    if (count > 0) {\n      // Starts from 0.\n      dim.name = dimOriginalName + (count - 1);\n    }\n    count++;\n    duplicationMap.set(dimOriginalName, count);\n  }\n}\n// ??? TODO\n// Originally detect dimCount by data[0]. Should we\n// optimize it to only by sysDims and dimensions and encode.\n// So only necessary dims will be initialized.\n// But\n// (1) custom series should be considered. where other dims\n// may be visited.\n// (2) sometimes user need to calculate bubble size or use visualMap\n// on other dimensions besides coordSys needed.\n// So, dims that is not used by system, should be shared in data store?\nfunction getDimCount(source, sysDims, dimsDef, optDimCount) {\n  // Note that the result dimCount should not small than columns count\n  // of data, otherwise `dataDimNameMap` checking will be incorrect.\n  var dimCount = Math.max(source.dimensionsDetectedCount || 1, sysDims.length, dimsDef.length, optDimCount || 0);\n  each(sysDims, function (sysDimItem) {\n    var sysDimItemDimsDef;\n    if (isObject(sysDimItem) && (sysDimItemDimsDef = sysDimItem.dimsDef)) {\n      dimCount = Math.max(dimCount, sysDimItemDimsDef.length);\n    }\n  });\n  return dimCount;\n}\nfunction genCoordDimName(name, map, fromZero) {\n  if (fromZero || map.hasKey(name)) {\n    var i = 0;\n    while (map.hasKey(name + i)) {\n      i++;\n    }\n    name += i;\n  }\n  map.set(name, true);\n  return name;\n}"], "names": [], "mappings": "AACA;;;;;;;;;;;;;;;;;AAiBA,GAGA;;CAEC,GAED;;;;;;;;;;;;;;;;;AAiBA;;;;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;AAIO,SAAS,iBAAiB,MAAM,EAAE,GAAG;IAC1C,OAAO,wBAAwB,QAAQ,KAAK,UAAU;AACxD;AAae,SAAS,wBACxB,qCAAqC;AACrC,MAAM,EAAE,GAAG;IACT,IAAI,CAAC,CAAA,GAAA,gJAAA,CAAA,mBAAgB,AAAD,EAAE,SAAS;QAC7B,SAAS,CAAA,GAAA,gJAAA,CAAA,mCAAgC,AAAD,EAAE;IAC5C;IACA,MAAM,OAAO,CAAC;IACd,IAAI,UAAU,IAAI,eAAe,IAAI,EAAE;IACvC,IAAI,UAAU,IAAI,gBAAgB,IAAI,OAAO,gBAAgB,IAAI,EAAE;IACnE,IAAI,kBAAkB,CAAA,GAAA,8IAAA,CAAA,gBAAa,AAAD;IAClC,IAAI,aAAa,EAAE;IACnB,IAAI,WAAW,YAAY,QAAQ,SAAS,SAAS,IAAI,eAAe;IACxE,wEAAwE;IACxE,6BAA6B;IAC7B,IAAI,uBAAuB,IAAI,uBAAuB,IAAI,CAAA,GAAA,oKAAA,CAAA,6BAA0B,AAAD,EAAE;IACrF,IAAI,6BAA6B,YAAY,OAAO,gBAAgB;IACpE,IAAI,iBAAiB,6BAA6B,CAAA,GAAA,oKAAA,CAAA,yBAAsB,AAAD,EAAE,UAAU,CAAA,GAAA,oKAAA,CAAA,mBAAgB,AAAD,EAAE;IACpG,IAAI,YAAY,IAAI,YAAY;IAChC,IAAI,CAAC,aAAa,IAAI,eAAe,EAAE;QACrC,YAAY,IAAI,eAAe,CAAC,QAAQ;IAC1C;IACA,IAAI,eAAe,CAAA,GAAA,8IAAA,CAAA,gBAAa,AAAD,EAAE;IACjC,IAAI,aAAa,IAAI,mJAAA,CAAA,iBAAc,CAAC;IACpC,IAAK,IAAI,IAAI,GAAG,IAAI,WAAW,MAAM,EAAE,IAAK;QAC1C,UAAU,CAAC,EAAE,GAAG,CAAC;IACnB;IACA,SAAS,cAAc,MAAM;QAC3B,IAAI,MAAM,UAAU,CAAC,OAAO;QAC5B,IAAI,MAAM,GAAG;YACX,IAAI,gBAAgB,OAAO,CAAC,OAAO;YACnC,IAAI,aAAa,CAAA,GAAA,8IAAA,CAAA,WAAQ,AAAD,EAAE,iBAAiB,gBAAgB;gBACzD,MAAM;YACR;YACA,IAAI,aAAa,IAAI,+JAAA,CAAA,UAAqB;YAC1C,IAAI,cAAc,WAAW,IAAI;YACjC,IAAI,eAAe,QAAQ,eAAe,GAAG,CAAC,gBAAgB,MAAM;gBAClE,mDAAmD;gBACnD,0EAA0E;gBAC1E,sBAAsB;gBACtB,WAAW,IAAI,GAAG,WAAW,WAAW,GAAG;YAC7C;YACA,WAAW,IAAI,IAAI,QAAQ,CAAC,WAAW,IAAI,GAAG,WAAW,IAAI;YAC7D,WAAW,WAAW,IAAI,QAAQ,CAAC,WAAW,WAAW,GAAG,WAAW,WAAW;YAClF,IAAI,SAAS,WAAW,MAAM;YAC9B,UAAU,CAAC,OAAO,GAAG;YACrB,WAAW,aAAa,GAAG;YAC3B,WAAW,IAAI,CAAC;YAChB,OAAO;QACT;QACA,OAAO,UAAU,CAAC,IAAI;IACxB;IACA,IAAI,CAAC,sBAAsB;QACzB,IAAK,IAAI,IAAI,GAAG,IAAI,UAAU,IAAK;YACjC,cAAc;QAChB;IACF;IACA,qFAAqF;IACrF,aAAa,IAAI,CAAC,SAAU,WAAW,EAAE,QAAQ;QAC/C,IAAI,WAAW,CAAA,GAAA,+IAAA,CAAA,mBAAgB,AAAD,EAAE,aAAa,KAAK;QAClD,sEAAsE;QACtE,2DAA2D;QAC3D,aAAa;QACb,IAAI,SAAS,MAAM,KAAK,KAAK,CAAC,CAAA,GAAA,8IAAA,CAAA,WAAQ,AAAD,EAAE,QAAQ,CAAC,EAAE,KAAK,QAAQ,CAAC,EAAE,GAAG,GAAG;YACtE,aAAa,GAAG,CAAC,UAAU;YAC3B;QACF;QACA,IAAI,gBAAgB,aAAa,GAAG,CAAC,UAAU,EAAE;QACjD,CAAA,GAAA,8IAAA,CAAA,OAAI,AAAD,EAAE,UAAU,SAAU,kBAAkB,EAAE,GAAG;YAC9C,mDAAmD;YACnD,IAAI,eAAe,CAAA,GAAA,8IAAA,CAAA,WAAQ,AAAD,EAAE,sBAAsB,eAAe,GAAG,CAAC,sBAAsB;YAC3F,IAAI,gBAAgB,QAAQ,eAAe,UAAU;gBACnD,aAAa,CAAC,IAAI,GAAG;gBACrB,SAAS,cAAc,eAAe,UAAU;YAClD;QACF;IACF;IACA,oDAAoD;IACpD,IAAI,cAAc;IAClB,CAAA,GAAA,8IAAA,CAAA,OAAI,AAAD,EAAE,SAAS,SAAU,aAAa;QACnC,IAAI;QACJ,IAAI;QACJ,IAAI;QACJ,IAAI;QACJ,IAAI,CAAA,GAAA,8IAAA,CAAA,WAAQ,AAAD,EAAE,gBAAgB;YAC3B,WAAW;YACX,aAAa,CAAC;QAChB,OAAO;YACL,aAAa;YACb,WAAW,WAAW,IAAI;YAC1B,IAAI,cAAc,WAAW,WAAW;YACxC,WAAW,WAAW,GAAG;YACzB,aAAa,CAAA,GAAA,8IAAA,CAAA,SAAM,AAAD,EAAE,CAAC,GAAG;YACxB,WAAW,WAAW,GAAG;YACzB,8CAA8C;YAC9C,oBAAoB,WAAW,OAAO;YACtC,sBAAsB,WAAW,SAAS;YAC1C,WAAW,IAAI,GAAG,WAAW,QAAQ,GAAG,WAAW,aAAa,GAAG,WAAW,OAAO,GAAG,WAAW,SAAS,GAAG;QACjH;QACA,IAAI,WAAW,aAAa,GAAG,CAAC;QAChC,kDAAkD;QAClD,IAAI,aAAa,OAAO;YACtB;QACF;QACA,WAAW,CAAA,GAAA,+IAAA,CAAA,mBAAgB,AAAD,EAAE;QAC5B,6CAA6C;QAC7C,IAAI,CAAC,SAAS,MAAM,EAAE;YACpB,IAAK,IAAI,IAAI,GAAG,IAAI,CAAC,qBAAqB,kBAAkB,MAAM,IAAI,CAAC,GAAG,IAAK;gBAC7E,MAAO,cAAc,YAAY,cAAc,aAAa,QAAQ,IAAI,KAAM;oBAC5E;gBACF;gBACA,cAAc,YAAY,SAAS,IAAI,CAAC;YAC1C;QACF;QACA,mBAAmB;QACnB,CAAA,GAAA,8IAAA,CAAA,OAAI,AAAD,EAAE,UAAU,SAAU,YAAY,EAAE,aAAa;YAClD,IAAI,aAAa,cAAc;YAC/B,mEAAmE;YACnE,IAAI,8BAA8B,WAAW,IAAI,IAAI,MAAM;gBACzD,WAAW,IAAI,GAAG,WAAW,IAAI;YACnC;YACA,SAAS,CAAA,GAAA,8IAAA,CAAA,WAAQ,AAAD,EAAE,YAAY,aAAa,UAAU;YACrD,IAAI,WAAW,IAAI,IAAI,QAAQ,mBAAmB;gBAChD,IAAI,wBAAwB,iBAAiB,CAAC,cAAc;gBAC5D,CAAC,CAAA,GAAA,8IAAA,CAAA,WAAQ,AAAD,EAAE,0BAA0B,CAAC,wBAAwB;oBAC3D,MAAM;gBACR,CAAC;gBACD,WAAW,IAAI,GAAG,WAAW,WAAW,GAAG,sBAAsB,IAAI;gBACrE,WAAW,cAAc,GAAG,sBAAsB,cAAc;YAClE;YACA,6EAA6E;YAC7E,uBAAuB,CAAA,GAAA,8IAAA,CAAA,WAAQ,AAAD,EAAE,WAAW,SAAS,EAAE;QACxD;IACF;IACA,SAAS,SAAS,UAAU,EAAE,QAAQ,EAAE,aAAa;QACnD,IAAI,+IAAA,CAAA,oBAAiB,CAAC,GAAG,CAAC,aAAa,MAAM;YAC3C,WAAW,SAAS,CAAC,SAAS,GAAG;QACnC,OAAO;YACL,WAAW,QAAQ,GAAG;YACtB,WAAW,aAAa,GAAG;YAC3B,gBAAgB,GAAG,CAAC,UAAU;QAChC;IACF;IACA,4CAA4C;IAC5C,IAAI,gBAAgB,IAAI,aAAa;IACrC,IAAI,qBAAqB,IAAI,kBAAkB;IAC/C,IAAI,WAAW,sBAAsB;IACrC,qBAAqB,gBAAgB,sBAAsB,IAAI;IAC/D,IAAI,QAAQ,iBAAiB;IAC7B,SAAS,0BAA0B,UAAU;QAC3C,IAAI,WAAW,IAAI,IAAI,MAAM;YAC3B,gDAAgD;YAChD,WAAW,IAAI,GAAG,WAAW,QAAQ;QACvC;IACF;IACA,uDAAuD;IACvD,IAAI,CAAC,sBAAsB;QACzB,IAAK,IAAI,eAAe,GAAG,eAAe,UAAU,eAAgB;YAClE,IAAI,aAAa,cAAc;YAC/B,IAAI,WAAW,WAAW,QAAQ;YAClC,IAAI,YAAY,MAAM;gBACpB,sDAAsD;gBACtD,WAAW,QAAQ,GAAG,gBAAgB,OAAO,iBAAiB;gBAC9D,WAAW,aAAa,GAAG;gBAC3B,+CAA+C;gBAC/C,IAAI,CAAC,iBAAiB,sBAAsB,GAAG;oBAC7C,WAAW,YAAY,GAAG;gBAC5B;gBACA;YACF;YACA,0BAA0B;YAC1B,IAAI,WAAW,IAAI,IAAI,QAAQ,CAAC,CAAA,GAAA,gKAAA,CAAA,eAAY,AAAD,EAAE,QAAQ,kBAAkB,gKAAA,CAAA,aAAU,CAAC,IAAI,IAanF,WAAW,YAAY,IAAI,CAAC,WAAW,SAAS,CAAC,QAAQ,IAAI,QAAQ,WAAW,SAAS,CAAC,UAAU,IAAI,IAAI,CAAC,GAAG;gBACjH,WAAW,IAAI,GAAG;YACpB;QACF;IACF,OAAO;QACL,CAAA,GAAA,8IAAA,CAAA,OAAI,AAAD,EAAE,YAAY,SAAU,UAAU;YACnC,sEAAsE;YACtE,0BAA0B;QAC5B;QACA,uEAAuE;QACvE,6CAA6C;QAC7C,WAAW,IAAI,CAAC,SAAU,KAAK,EAAE,KAAK;YACpC,OAAO,MAAM,aAAa,GAAG,MAAM,aAAa;QAClD;IACF;IACA,kBAAkB;IAClB,OAAO,IAAI,oKAAA,CAAA,mBAAgB,CAAC;QAC1B,QAAQ;QACR,YAAY;QACZ,oBAAoB;QACpB,kBAAkB;IACpB;AACF;AACA,SAAS,kBAAkB,MAAM;IAC/B,IAAI,iBAAiB,CAAA,GAAA,8IAAA,CAAA,gBAAa,AAAD;IACjC,IAAK,IAAI,IAAI,GAAG,IAAI,OAAO,MAAM,EAAE,IAAK;QACtC,IAAI,MAAM,MAAM,CAAC,EAAE;QACnB,IAAI,kBAAkB,IAAI,IAAI;QAC9B,IAAI,QAAQ,eAAe,GAAG,CAAC,oBAAoB;QACnD,IAAI,QAAQ,GAAG;YACb,iBAAiB;YACjB,IAAI,IAAI,GAAG,kBAAkB,CAAC,QAAQ,CAAC;QACzC;QACA;QACA,eAAe,GAAG,CAAC,iBAAiB;IACtC;AACF;AACA,WAAW;AACX,mDAAmD;AACnD,4DAA4D;AAC5D,8CAA8C;AAC9C,MAAM;AACN,2DAA2D;AAC3D,kBAAkB;AAClB,oEAAoE;AACpE,+CAA+C;AAC/C,uEAAuE;AACvE,SAAS,YAAY,MAAM,EAAE,OAAO,EAAE,OAAO,EAAE,WAAW;IACxD,oEAAoE;IACpE,kEAAkE;IAClE,IAAI,WAAW,KAAK,GAAG,CAAC,OAAO,uBAAuB,IAAI,GAAG,QAAQ,MAAM,EAAE,QAAQ,MAAM,EAAE,eAAe;IAC5G,CAAA,GAAA,8IAAA,CAAA,OAAI,AAAD,EAAE,SAAS,SAAU,UAAU;QAChC,IAAI;QACJ,IAAI,CAAA,GAAA,8IAAA,CAAA,WAAQ,AAAD,EAAE,eAAe,CAAC,oBAAoB,WAAW,OAAO,GAAG;YACpE,WAAW,KAAK,GAAG,CAAC,UAAU,kBAAkB,MAAM;QACxD;IACF;IACA,OAAO;AACT;AACA,SAAS,gBAAgB,IAAI,EAAE,GAAG,EAAE,QAAQ;IAC1C,IAAI,YAAY,IAAI,MAAM,CAAC,OAAO;QAChC,IAAI,IAAI;QACR,MAAO,IAAI,MAAM,CAAC,OAAO,GAAI;YAC3B;QACF;QACA,QAAQ;IACV;IACA,IAAI,GAAG,CAAC,MAAM;IACd,OAAO;AACT", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 5087, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/tindahan/sari-sari-admin/node_modules/echarts/lib/data/helper/dataStackHelper.js"], "sourcesContent": ["\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\r\n* Licensed to the Apache Software Foundation (ASF) under one\r\n* or more contributor license agreements.  See the NOTICE file\r\n* distributed with this work for additional information\r\n* regarding copyright ownership.  The ASF licenses this file\r\n* to you under the Apache License, Version 2.0 (the\r\n* \"License\"); you may not use this file except in compliance\r\n* with the License.  You may obtain a copy of the License at\r\n*\r\n*   http://www.apache.org/licenses/LICENSE-2.0\r\n*\r\n* Unless required by applicable law or agreed to in writing,\r\n* software distributed under the License is distributed on an\r\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\r\n* KIND, either express or implied.  See the License for the\r\n* specific language governing permissions and limitations\r\n* under the License.\r\n*/\nimport { each, isString } from 'zrender/lib/core/util.js';\nimport { isSeriesDataSchema } from './SeriesDataSchema.js';\n/**\r\n * Note that it is too complicated to support 3d stack by value\r\n * (have to create two-dimension inverted index), so in 3d case\r\n * we just support that stacked by index.\r\n *\r\n * @param seriesModel\r\n * @param dimensionsInput The same as the input of <module:echarts/data/SeriesData>.\r\n *        The input will be modified.\r\n * @param opt\r\n * @param opt.stackedCoordDimension Specify a coord dimension if needed.\r\n * @param opt.byIndex=false\r\n * @return calculationInfo\r\n * {\r\n *     stackedDimension: string\r\n *     stackedByDimension: string\r\n *     isStackedByIndex: boolean\r\n *     stackedOverDimension: string\r\n *     stackResultDimension: string\r\n * }\r\n */\nexport function enableDataStack(seriesModel, dimensionsInput, opt) {\n  opt = opt || {};\n  var byIndex = opt.byIndex;\n  var stackedCoordDimension = opt.stackedCoordDimension;\n  var dimensionDefineList;\n  var schema;\n  var store;\n  if (isLegacyDimensionsInput(dimensionsInput)) {\n    dimensionDefineList = dimensionsInput;\n  } else {\n    schema = dimensionsInput.schema;\n    dimensionDefineList = schema.dimensions;\n    store = dimensionsInput.store;\n  }\n  // Compatibal: when `stack` is set as '', do not stack.\n  var mayStack = !!(seriesModel && seriesModel.get('stack'));\n  var stackedByDimInfo;\n  var stackedDimInfo;\n  var stackResultDimension;\n  var stackedOverDimension;\n  each(dimensionDefineList, function (dimensionInfo, index) {\n    if (isString(dimensionInfo)) {\n      dimensionDefineList[index] = dimensionInfo = {\n        name: dimensionInfo\n      };\n    }\n    if (mayStack && !dimensionInfo.isExtraCoord) {\n      // Find the first ordinal dimension as the stackedByDimInfo.\n      if (!byIndex && !stackedByDimInfo && dimensionInfo.ordinalMeta) {\n        stackedByDimInfo = dimensionInfo;\n      }\n      // Find the first stackable dimension as the stackedDimInfo.\n      if (!stackedDimInfo && dimensionInfo.type !== 'ordinal' && dimensionInfo.type !== 'time' && (!stackedCoordDimension || stackedCoordDimension === dimensionInfo.coordDim)) {\n        stackedDimInfo = dimensionInfo;\n      }\n    }\n  });\n  if (stackedDimInfo && !byIndex && !stackedByDimInfo) {\n    // Compatible with previous design, value axis (time axis) only stack by index.\n    // It may make sense if the user provides elaborately constructed data.\n    byIndex = true;\n  }\n  // Add stack dimension, they can be both calculated by coordinate system in `unionExtent`.\n  // That put stack logic in List is for using conveniently in echarts extensions, but it\n  // might not be a good way.\n  if (stackedDimInfo) {\n    // Use a weird name that not duplicated with other names.\n    // Also need to use seriesModel.id as postfix because different\n    // series may share same data store. The stack dimension needs to be distinguished.\n    stackResultDimension = '__\\0ecstackresult_' + seriesModel.id;\n    stackedOverDimension = '__\\0ecstackedover_' + seriesModel.id;\n    // Create inverted index to fast query index by value.\n    if (stackedByDimInfo) {\n      stackedByDimInfo.createInvertedIndices = true;\n    }\n    var stackedDimCoordDim_1 = stackedDimInfo.coordDim;\n    var stackedDimType = stackedDimInfo.type;\n    var stackedDimCoordIndex_1 = 0;\n    each(dimensionDefineList, function (dimensionInfo) {\n      if (dimensionInfo.coordDim === stackedDimCoordDim_1) {\n        stackedDimCoordIndex_1++;\n      }\n    });\n    var stackedOverDimensionDefine = {\n      name: stackResultDimension,\n      coordDim: stackedDimCoordDim_1,\n      coordDimIndex: stackedDimCoordIndex_1,\n      type: stackedDimType,\n      isExtraCoord: true,\n      isCalculationCoord: true,\n      storeDimIndex: dimensionDefineList.length\n    };\n    var stackResultDimensionDefine = {\n      name: stackedOverDimension,\n      // This dimension contains stack base (generally, 0), so do not set it as\n      // `stackedDimCoordDim` to avoid extent calculation, consider log scale.\n      coordDim: stackedOverDimension,\n      coordDimIndex: stackedDimCoordIndex_1 + 1,\n      type: stackedDimType,\n      isExtraCoord: true,\n      isCalculationCoord: true,\n      storeDimIndex: dimensionDefineList.length + 1\n    };\n    if (schema) {\n      if (store) {\n        stackedOverDimensionDefine.storeDimIndex = store.ensureCalculationDimension(stackedOverDimension, stackedDimType);\n        stackResultDimensionDefine.storeDimIndex = store.ensureCalculationDimension(stackResultDimension, stackedDimType);\n      }\n      schema.appendCalculationDimension(stackedOverDimensionDefine);\n      schema.appendCalculationDimension(stackResultDimensionDefine);\n    } else {\n      dimensionDefineList.push(stackedOverDimensionDefine);\n      dimensionDefineList.push(stackResultDimensionDefine);\n    }\n  }\n  return {\n    stackedDimension: stackedDimInfo && stackedDimInfo.name,\n    stackedByDimension: stackedByDimInfo && stackedByDimInfo.name,\n    isStackedByIndex: byIndex,\n    stackedOverDimension: stackedOverDimension,\n    stackResultDimension: stackResultDimension\n  };\n}\nfunction isLegacyDimensionsInput(dimensionsInput) {\n  return !isSeriesDataSchema(dimensionsInput.schema);\n}\nexport function isDimensionStacked(data, stackedDim) {\n  // Each single series only maps to one pair of axis. So we do not need to\n  // check stackByDim, whatever stacked by a dimension or stacked by index.\n  return !!stackedDim && stackedDim === data.getCalculationInfo('stackedDimension');\n}\nexport function getStackedDimension(data, targetDim) {\n  return isDimensionStacked(data, targetDim) ? data.getCalculationInfo('stackResultDimension') : targetDim;\n}"], "names": [], "mappings": "AACA;;;;;;;;;;;;;;;;;AAiBA,GAGA;;CAEC,GAED;;;;;;;;;;;;;;;;;AAiBA;;;;;AACA;AACA;;;AAqBO,SAAS,gBAAgB,WAAW,EAAE,eAAe,EAAE,GAAG;IAC/D,MAAM,OAAO,CAAC;IACd,IAAI,UAAU,IAAI,OAAO;IACzB,IAAI,wBAAwB,IAAI,qBAAqB;IACrD,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,IAAI,wBAAwB,kBAAkB;QAC5C,sBAAsB;IACxB,OAAO;QACL,SAAS,gBAAgB,MAAM;QAC/B,sBAAsB,OAAO,UAAU;QACvC,QAAQ,gBAAgB,KAAK;IAC/B;IACA,uDAAuD;IACvD,IAAI,WAAW,CAAC,CAAC,CAAC,eAAe,YAAY,GAAG,CAAC,QAAQ;IACzD,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,CAAA,GAAA,8IAAA,CAAA,OAAI,AAAD,EAAE,qBAAqB,SAAU,aAAa,EAAE,KAAK;QACtD,IAAI,CAAA,GAAA,8IAAA,CAAA,WAAQ,AAAD,EAAE,gBAAgB;YAC3B,mBAAmB,CAAC,MAAM,GAAG,gBAAgB;gBAC3C,MAAM;YACR;QACF;QACA,IAAI,YAAY,CAAC,cAAc,YAAY,EAAE;YAC3C,4DAA4D;YAC5D,IAAI,CAAC,WAAW,CAAC,oBAAoB,cAAc,WAAW,EAAE;gBAC9D,mBAAmB;YACrB;YACA,4DAA4D;YAC5D,IAAI,CAAC,kBAAkB,cAAc,IAAI,KAAK,aAAa,cAAc,IAAI,KAAK,UAAU,CAAC,CAAC,yBAAyB,0BAA0B,cAAc,QAAQ,GAAG;gBACxK,iBAAiB;YACnB;QACF;IACF;IACA,IAAI,kBAAkB,CAAC,WAAW,CAAC,kBAAkB;QACnD,+EAA+E;QAC/E,uEAAuE;QACvE,UAAU;IACZ;IACA,0FAA0F;IAC1F,uFAAuF;IACvF,2BAA2B;IAC3B,IAAI,gBAAgB;QAClB,yDAAyD;QACzD,+DAA+D;QAC/D,mFAAmF;QACnF,uBAAuB,uBAAuB,YAAY,EAAE;QAC5D,uBAAuB,uBAAuB,YAAY,EAAE;QAC5D,sDAAsD;QACtD,IAAI,kBAAkB;YACpB,iBAAiB,qBAAqB,GAAG;QAC3C;QACA,IAAI,uBAAuB,eAAe,QAAQ;QAClD,IAAI,iBAAiB,eAAe,IAAI;QACxC,IAAI,yBAAyB;QAC7B,CAAA,GAAA,8IAAA,CAAA,OAAI,AAAD,EAAE,qBAAqB,SAAU,aAAa;YAC/C,IAAI,cAAc,QAAQ,KAAK,sBAAsB;gBACnD;YACF;QACF;QACA,IAAI,6BAA6B;YAC/B,MAAM;YACN,UAAU;YACV,eAAe;YACf,MAAM;YACN,cAAc;YACd,oBAAoB;YACpB,eAAe,oBAAoB,MAAM;QAC3C;QACA,IAAI,6BAA6B;YAC/B,MAAM;YACN,yEAAyE;YACzE,wEAAwE;YACxE,UAAU;YACV,eAAe,yBAAyB;YACxC,MAAM;YACN,cAAc;YACd,oBAAoB;YACpB,eAAe,oBAAoB,MAAM,GAAG;QAC9C;QACA,IAAI,QAAQ;YACV,IAAI,OAAO;gBACT,2BAA2B,aAAa,GAAG,MAAM,0BAA0B,CAAC,sBAAsB;gBAClG,2BAA2B,aAAa,GAAG,MAAM,0BAA0B,CAAC,sBAAsB;YACpG;YACA,OAAO,0BAA0B,CAAC;YAClC,OAAO,0BAA0B,CAAC;QACpC,OAAO;YACL,oBAAoB,IAAI,CAAC;YACzB,oBAAoB,IAAI,CAAC;QAC3B;IACF;IACA,OAAO;QACL,kBAAkB,kBAAkB,eAAe,IAAI;QACvD,oBAAoB,oBAAoB,iBAAiB,IAAI;QAC7D,kBAAkB;QAClB,sBAAsB;QACtB,sBAAsB;IACxB;AACF;AACA,SAAS,wBAAwB,eAAe;IAC9C,OAAO,CAAC,CAAA,GAAA,oKAAA,CAAA,qBAAkB,AAAD,EAAE,gBAAgB,MAAM;AACnD;AACO,SAAS,mBAAmB,IAAI,EAAE,UAAU;IACjD,yEAAyE;IACzE,yEAAyE;IACzE,OAAO,CAAC,CAAC,cAAc,eAAe,KAAK,kBAAkB,CAAC;AAChE;AACO,SAAS,oBAAoB,IAAI,EAAE,SAAS;IACjD,OAAO,mBAAmB,MAAM,aAAa,KAAK,kBAAkB,CAAC,0BAA0B;AACjG", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 5252, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/tindahan/sari-sari-admin/node_modules/echarts/lib/data/OrdinalMeta.js"], "sourcesContent": ["\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\r\n* Licensed to the Apache Software Foundation (ASF) under one\r\n* or more contributor license agreements.  See the NOTICE file\r\n* distributed with this work for additional information\r\n* regarding copyright ownership.  The ASF licenses this file\r\n* to you under the Apache License, Version 2.0 (the\r\n* \"License\"); you may not use this file except in compliance\r\n* with the License.  You may obtain a copy of the License at\r\n*\r\n*   http://www.apache.org/licenses/LICENSE-2.0\r\n*\r\n* Unless required by applicable law or agreed to in writing,\r\n* software distributed under the License is distributed on an\r\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\r\n* KIND, either express or implied.  See the License for the\r\n* specific language governing permissions and limitations\r\n* under the License.\r\n*/\nimport { createHashMap, isObject, map, isString } from 'zrender/lib/core/util.js';\nvar uidBase = 0;\nvar OrdinalMeta = /** @class */function () {\n  function OrdinalMeta(opt) {\n    this.categories = opt.categories || [];\n    this._needCollect = opt.needCollect;\n    this._deduplication = opt.deduplication;\n    this.uid = ++uidBase;\n  }\n  OrdinalMeta.createByAxisModel = function (axisModel) {\n    var option = axisModel.option;\n    var data = option.data;\n    var categories = data && map(data, getName);\n    return new OrdinalMeta({\n      categories: categories,\n      needCollect: !categories,\n      // deduplication is default in axis.\n      deduplication: option.dedplication !== false\n    });\n  };\n  ;\n  OrdinalMeta.prototype.getOrdinal = function (category) {\n    // @ts-ignore\n    return this._getOrCreateMap().get(category);\n  };\n  /**\r\n   * @return The ordinal. If not found, return NaN.\r\n   */\n  OrdinalMeta.prototype.parseAndCollect = function (category) {\n    var index;\n    var needCollect = this._needCollect;\n    // The value of category dim can be the index of the given category set.\n    // This feature is only supported when !needCollect, because we should\n    // consider a common case: a value is 2017, which is a number but is\n    // expected to be tread as a category. This case usually happen in dataset,\n    // where it happent to be no need of the index feature.\n    if (!isString(category) && !needCollect) {\n      return category;\n    }\n    // Optimize for the scenario:\n    // category is ['2012-01-01', '2012-01-02', ...], where the input\n    // data has been ensured not duplicate and is large data.\n    // Notice, if a dataset dimension provide categroies, usually echarts\n    // should remove duplication except user tell echarts dont do that\n    // (set axis.deduplication = false), because echarts do not know whether\n    // the values in the category dimension has duplication (consider the\n    // parallel-aqi example)\n    if (needCollect && !this._deduplication) {\n      index = this.categories.length;\n      this.categories[index] = category;\n      return index;\n    }\n    var map = this._getOrCreateMap();\n    // @ts-ignore\n    index = map.get(category);\n    if (index == null) {\n      if (needCollect) {\n        index = this.categories.length;\n        this.categories[index] = category;\n        // @ts-ignore\n        map.set(category, index);\n      } else {\n        index = NaN;\n      }\n    }\n    return index;\n  };\n  // Consider big data, do not create map until needed.\n  OrdinalMeta.prototype._getOrCreateMap = function () {\n    return this._map || (this._map = createHashMap(this.categories));\n  };\n  return OrdinalMeta;\n}();\nfunction getName(obj) {\n  if (isObject(obj) && obj.value != null) {\n    return obj.value;\n  } else {\n    return obj + '';\n  }\n}\nexport default OrdinalMeta;"], "names": [], "mappings": "AACA;;;;;;;;;;;;;;;;;AAiBA,GAGA;;CAEC,GAED;;;;;;;;;;;;;;;;;AAiBA;;;AACA;;AACA,IAAI,UAAU;AACd,IAAI,cAAc,WAAW,GAAE;IAC7B,SAAS,YAAY,GAAG;QACtB,IAAI,CAAC,UAAU,GAAG,IAAI,UAAU,IAAI,EAAE;QACtC,IAAI,CAAC,YAAY,GAAG,IAAI,WAAW;QACnC,IAAI,CAAC,cAAc,GAAG,IAAI,aAAa;QACvC,IAAI,CAAC,GAAG,GAAG,EAAE;IACf;IACA,YAAY,iBAAiB,GAAG,SAAU,SAAS;QACjD,IAAI,SAAS,UAAU,MAAM;QAC7B,IAAI,OAAO,OAAO,IAAI;QACtB,IAAI,aAAa,QAAQ,CAAA,GAAA,8IAAA,CAAA,MAAG,AAAD,EAAE,MAAM;QACnC,OAAO,IAAI,YAAY;YACrB,YAAY;YACZ,aAAa,CAAC;YACd,oCAAoC;YACpC,eAAe,OAAO,YAAY,KAAK;QACzC;IACF;;IAEA,YAAY,SAAS,CAAC,UAAU,GAAG,SAAU,QAAQ;QACnD,aAAa;QACb,OAAO,IAAI,CAAC,eAAe,GAAG,GAAG,CAAC;IACpC;IACA;;GAEC,GACD,YAAY,SAAS,CAAC,eAAe,GAAG,SAAU,QAAQ;QACxD,IAAI;QACJ,IAAI,cAAc,IAAI,CAAC,YAAY;QACnC,wEAAwE;QACxE,sEAAsE;QACtE,oEAAoE;QACpE,2EAA2E;QAC3E,uDAAuD;QACvD,IAAI,CAAC,CAAA,GAAA,8IAAA,CAAA,WAAQ,AAAD,EAAE,aAAa,CAAC,aAAa;YACvC,OAAO;QACT;QACA,6BAA6B;QAC7B,iEAAiE;QACjE,yDAAyD;QACzD,qEAAqE;QACrE,kEAAkE;QAClE,wEAAwE;QACxE,qEAAqE;QACrE,wBAAwB;QACxB,IAAI,eAAe,CAAC,IAAI,CAAC,cAAc,EAAE;YACvC,QAAQ,IAAI,CAAC,UAAU,CAAC,MAAM;YAC9B,IAAI,CAAC,UAAU,CAAC,MAAM,GAAG;YACzB,OAAO;QACT;QACA,IAAI,MAAM,IAAI,CAAC,eAAe;QAC9B,aAAa;QACb,QAAQ,IAAI,GAAG,CAAC;QAChB,IAAI,SAAS,MAAM;YACjB,IAAI,aAAa;gBACf,QAAQ,IAAI,CAAC,UAAU,CAAC,MAAM;gBAC9B,IAAI,CAAC,UAAU,CAAC,MAAM,GAAG;gBACzB,aAAa;gBACb,IAAI,GAAG,CAAC,UAAU;YACpB,OAAO;gBACL,QAAQ;YACV;QACF;QACA,OAAO;IACT;IACA,qDAAqD;IACrD,YAAY,SAAS,CAAC,eAAe,GAAG;QACtC,OAAO,IAAI,CAAC,IAAI,IAAI,CAAC,IAAI,CAAC,IAAI,GAAG,CAAA,GAAA,8IAAA,CAAA,gBAAa,AAAD,EAAE,IAAI,CAAC,UAAU,CAAC;IACjE;IACA,OAAO;AACT;AACA,SAAS,QAAQ,GAAG;IAClB,IAAI,CAAA,GAAA,8IAAA,CAAA,WAAQ,AAAD,EAAE,QAAQ,IAAI,KAAK,IAAI,MAAM;QACtC,OAAO,IAAI,KAAK;IAClB,OAAO;QACL,OAAO,MAAM;IACf;AACF;uCACe", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 5378, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/tindahan/sari-sari-admin/node_modules/echarts/lib/data/helper/linkSeriesData.js"], "sourcesContent": ["\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\r\n* Licensed to the Apache Software Foundation (ASF) under one\r\n* or more contributor license agreements.  See the NOTICE file\r\n* distributed with this work for additional information\r\n* regarding copyright ownership.  The ASF licenses this file\r\n* to you under the Apache License, Version 2.0 (the\r\n* \"License\"); you may not use this file except in compliance\r\n* with the License.  You may obtain a copy of the License at\r\n*\r\n*   http://www.apache.org/licenses/LICENSE-2.0\r\n*\r\n* Unless required by applicable law or agreed to in writing,\r\n* software distributed under the License is distributed on an\r\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\r\n* KIND, either express or implied.  See the License for the\r\n* specific language governing permissions and limitations\r\n* under the License.\r\n*/\n/**\r\n * Link lists and struct (graph or tree)\r\n */\nimport { curry, each, assert, extend, map, keys } from 'zrender/lib/core/util.js';\nimport { makeInner } from '../../util/model.js';\nvar inner = makeInner();\nfunction linkSeriesData(opt) {\n  var mainData = opt.mainData;\n  var datas = opt.datas;\n  if (!datas) {\n    datas = {\n      main: mainData\n    };\n    opt.datasAttr = {\n      main: 'data'\n    };\n  }\n  opt.datas = opt.mainData = null;\n  linkAll(mainData, datas, opt);\n  // Porxy data original methods.\n  each(datas, function (data) {\n    each(mainData.TRANSFERABLE_METHODS, function (methodName) {\n      data.wrapMethod(methodName, curry(transferInjection, opt));\n    });\n  });\n  // Beyond transfer, additional features should be added to `cloneShallow`.\n  mainData.wrapMethod('cloneShallow', curry(cloneShallowInjection, opt));\n  // Only mainData trigger change, because struct.update may trigger\n  // another changable methods, which may bring about dead lock.\n  each(mainData.CHANGABLE_METHODS, function (methodName) {\n    mainData.wrapMethod(methodName, curry(changeInjection, opt));\n  });\n  // Make sure datas contains mainData.\n  assert(datas[mainData.dataType] === mainData);\n}\nfunction transferInjection(opt, res) {\n  if (isMainData(this)) {\n    // Transfer datas to new main data.\n    var datas = extend({}, inner(this).datas);\n    datas[this.dataType] = res;\n    linkAll(res, datas, opt);\n  } else {\n    // Modify the reference in main data to point newData.\n    linkSingle(res, this.dataType, inner(this).mainData, opt);\n  }\n  return res;\n}\nfunction changeInjection(opt, res) {\n  opt.struct && opt.struct.update();\n  return res;\n}\nfunction cloneShallowInjection(opt, res) {\n  // cloneShallow, which brings about some fragilities, may be inappropriate\n  // to be exposed as an API. So for implementation simplicity we can make\n  // the restriction that cloneShallow of not-mainData should not be invoked\n  // outside, but only be invoked here.\n  each(inner(res).datas, function (data, dataType) {\n    data !== res && linkSingle(data.cloneShallow(), dataType, res, opt);\n  });\n  return res;\n}\n/**\r\n * Supplement method to List.\r\n *\r\n * @public\r\n * @param [dataType] If not specified, return mainData.\r\n */\nfunction getLinkedData(dataType) {\n  var mainData = inner(this).mainData;\n  return dataType == null || mainData == null ? mainData : inner(mainData).datas[dataType];\n}\n/**\r\n * Get list of all linked data\r\n */\nfunction getLinkedDataAll() {\n  var mainData = inner(this).mainData;\n  return mainData == null ? [{\n    data: mainData\n  }] : map(keys(inner(mainData).datas), function (type) {\n    return {\n      type: type,\n      data: inner(mainData).datas[type]\n    };\n  });\n}\nfunction isMainData(data) {\n  return inner(data).mainData === data;\n}\nfunction linkAll(mainData, datas, opt) {\n  inner(mainData).datas = {};\n  each(datas, function (data, dataType) {\n    linkSingle(data, dataType, mainData, opt);\n  });\n}\nfunction linkSingle(data, dataType, mainData, opt) {\n  inner(mainData).datas[dataType] = data;\n  inner(data).mainData = mainData;\n  data.dataType = dataType;\n  if (opt.struct) {\n    data[opt.structAttr] = opt.struct;\n    opt.struct[opt.datasAttr[dataType]] = data;\n  }\n  // Supplement method.\n  data.getLinkedData = getLinkedData;\n  data.getLinkedDataAll = getLinkedDataAll;\n}\nexport default linkSeriesData;"], "names": [], "mappings": "AACA;;;;;;;;;;;;;;;;;AAiBA,GAGA;;CAEC,GAED;;;;;;;;;;;;;;;;;AAiBA,GACA;;CAEC;;;AACD;AACA;;;AACA,IAAI,QAAQ,CAAA,GAAA,+IAAA,CAAA,YAAS,AAAD;AACpB,SAAS,eAAe,GAAG;IACzB,IAAI,WAAW,IAAI,QAAQ;IAC3B,IAAI,QAAQ,IAAI,KAAK;IACrB,IAAI,CAAC,OAAO;QACV,QAAQ;YACN,MAAM;QACR;QACA,IAAI,SAAS,GAAG;YACd,MAAM;QACR;IACF;IACA,IAAI,KAAK,GAAG,IAAI,QAAQ,GAAG;IAC3B,QAAQ,UAAU,OAAO;IACzB,+BAA+B;IAC/B,CAAA,GAAA,8IAAA,CAAA,OAAI,AAAD,EAAE,OAAO,SAAU,IAAI;QACxB,CAAA,GAAA,8IAAA,CAAA,OAAI,AAAD,EAAE,SAAS,oBAAoB,EAAE,SAAU,UAAU;YACtD,KAAK,UAAU,CAAC,YAAY,CAAA,GAAA,8IAAA,CAAA,QAAK,AAAD,EAAE,mBAAmB;QACvD;IACF;IACA,0EAA0E;IAC1E,SAAS,UAAU,CAAC,gBAAgB,CAAA,GAAA,8IAAA,CAAA,QAAK,AAAD,EAAE,uBAAuB;IACjE,kEAAkE;IAClE,8DAA8D;IAC9D,CAAA,GAAA,8IAAA,CAAA,OAAI,AAAD,EAAE,SAAS,iBAAiB,EAAE,SAAU,UAAU;QACnD,SAAS,UAAU,CAAC,YAAY,CAAA,GAAA,8IAAA,CAAA,QAAK,AAAD,EAAE,iBAAiB;IACzD;IACA,qCAAqC;IACrC,CAAA,GAAA,8IAAA,CAAA,SAAM,AAAD,EAAE,KAAK,CAAC,SAAS,QAAQ,CAAC,KAAK;AACtC;AACA,SAAS,kBAAkB,GAAG,EAAE,GAAG;IACjC,IAAI,WAAW,IAAI,GAAG;QACpB,mCAAmC;QACnC,IAAI,QAAQ,CAAA,GAAA,8IAAA,CAAA,SAAM,AAAD,EAAE,CAAC,GAAG,MAAM,IAAI,EAAE,KAAK;QACxC,KAAK,CAAC,IAAI,CAAC,QAAQ,CAAC,GAAG;QACvB,QAAQ,KAAK,OAAO;IACtB,OAAO;QACL,sDAAsD;QACtD,WAAW,KAAK,IAAI,CAAC,QAAQ,EAAE,MAAM,IAAI,EAAE,QAAQ,EAAE;IACvD;IACA,OAAO;AACT;AACA,SAAS,gBAAgB,GAAG,EAAE,GAAG;IAC/B,IAAI,MAAM,IAAI,IAAI,MAAM,CAAC,MAAM;IAC/B,OAAO;AACT;AACA,SAAS,sBAAsB,GAAG,EAAE,GAAG;IACrC,0EAA0E;IAC1E,wEAAwE;IACxE,0EAA0E;IAC1E,qCAAqC;IACrC,CAAA,GAAA,8IAAA,CAAA,OAAI,AAAD,EAAE,MAAM,KAAK,KAAK,EAAE,SAAU,IAAI,EAAE,QAAQ;QAC7C,SAAS,OAAO,WAAW,KAAK,YAAY,IAAI,UAAU,KAAK;IACjE;IACA,OAAO;AACT;AACA;;;;;CAKC,GACD,SAAS,cAAc,QAAQ;IAC7B,IAAI,WAAW,MAAM,IAAI,EAAE,QAAQ;IACnC,OAAO,YAAY,QAAQ,YAAY,OAAO,WAAW,MAAM,UAAU,KAAK,CAAC,SAAS;AAC1F;AACA;;CAEC,GACD,SAAS;IACP,IAAI,WAAW,MAAM,IAAI,EAAE,QAAQ;IACnC,OAAO,YAAY,OAAO;QAAC;YACzB,MAAM;QACR;KAAE,GAAG,CAAA,GAAA,8IAAA,CAAA,MAAG,AAAD,EAAE,CAAA,GAAA,8IAAA,CAAA,OAAI,AAAD,EAAE,MAAM,UAAU,KAAK,GAAG,SAAU,IAAI;QAClD,OAAO;YACL,MAAM;YACN,MAAM,MAAM,UAAU,KAAK,CAAC,KAAK;QACnC;IACF;AACF;AACA,SAAS,WAAW,IAAI;IACtB,OAAO,MAAM,MAAM,QAAQ,KAAK;AAClC;AACA,SAAS,QAAQ,QAAQ,EAAE,KAAK,EAAE,GAAG;IACnC,MAAM,UAAU,KAAK,GAAG,CAAC;IACzB,CAAA,GAAA,8IAAA,CAAA,OAAI,AAAD,EAAE,OAAO,SAAU,IAAI,EAAE,QAAQ;QAClC,WAAW,MAAM,UAAU,UAAU;IACvC;AACF;AACA,SAAS,WAAW,IAAI,EAAE,QAAQ,EAAE,QAAQ,EAAE,GAAG;IAC/C,MAAM,UAAU,KAAK,CAAC,SAAS,GAAG;IAClC,MAAM,MAAM,QAAQ,GAAG;IACvB,KAAK,QAAQ,GAAG;IAChB,IAAI,IAAI,MAAM,EAAE;QACd,IAAI,CAAC,IAAI,UAAU,CAAC,GAAG,IAAI,MAAM;QACjC,IAAI,MAAM,CAAC,IAAI,SAAS,CAAC,SAAS,CAAC,GAAG;IACxC;IACA,qBAAqB;IACrB,KAAK,aAAa,GAAG;IACrB,KAAK,gBAAgB,GAAG;AAC1B;uCACe", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 5531, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/tindahan/sari-sari-admin/node_modules/echarts/lib/data/Tree.js"], "sourcesContent": ["\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\r\n* Licensed to the Apache Software Foundation (ASF) under one\r\n* or more contributor license agreements.  See the NOTICE file\r\n* distributed with this work for additional information\r\n* regarding copyright ownership.  The ASF licenses this file\r\n* to you under the Apache License, Version 2.0 (the\r\n* \"License\"); you may not use this file except in compliance\r\n* with the License.  You may obtain a copy of the License at\r\n*\r\n*   http://www.apache.org/licenses/LICENSE-2.0\r\n*\r\n* Unless required by applicable law or agreed to in writing,\r\n* software distributed under the License is distributed on an\r\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\r\n* KIND, either express or implied.  See the License for the\r\n* specific language governing permissions and limitations\r\n* under the License.\r\n*/\n/**\r\n * Tree data structure\r\n */\nimport * as zrUtil from 'zrender/lib/core/util.js';\nimport linkSeriesData from './helper/linkSeriesData.js';\nimport SeriesData from './SeriesData.js';\nimport prepareSeriesDataSchema from './helper/createDimensions.js';\nimport { convertOptionIdName } from '../util/model.js';\nvar TreeNode = /** @class */function () {\n  function TreeNode(name, hostTree) {\n    this.depth = 0;\n    this.height = 0;\n    /**\r\n     * Reference to list item.\r\n     * Do not persistent dataIndex outside,\r\n     * besause it may be changed by list.\r\n     * If dataIndex -1,\r\n     * this node is logical deleted (filtered) in list.\r\n     */\n    this.dataIndex = -1;\n    this.children = [];\n    this.viewChildren = [];\n    this.isExpand = false;\n    this.name = name || '';\n    this.hostTree = hostTree;\n  }\n  /**\r\n   * The node is removed.\r\n   */\n  TreeNode.prototype.isRemoved = function () {\n    return this.dataIndex < 0;\n  };\n  TreeNode.prototype.eachNode = function (options, cb, context) {\n    if (zrUtil.isFunction(options)) {\n      context = cb;\n      cb = options;\n      options = null;\n    }\n    options = options || {};\n    if (zrUtil.isString(options)) {\n      options = {\n        order: options\n      };\n    }\n    var order = options.order || 'preorder';\n    var children = this[options.attr || 'children'];\n    var suppressVisitSub;\n    order === 'preorder' && (suppressVisitSub = cb.call(context, this));\n    for (var i = 0; !suppressVisitSub && i < children.length; i++) {\n      children[i].eachNode(options, cb, context);\n    }\n    order === 'postorder' && cb.call(context, this);\n  };\n  /**\r\n   * Update depth and height of this subtree.\r\n   */\n  TreeNode.prototype.updateDepthAndHeight = function (depth) {\n    var height = 0;\n    this.depth = depth;\n    for (var i = 0; i < this.children.length; i++) {\n      var child = this.children[i];\n      child.updateDepthAndHeight(depth + 1);\n      if (child.height > height) {\n        height = child.height;\n      }\n    }\n    this.height = height + 1;\n  };\n  TreeNode.prototype.getNodeById = function (id) {\n    if (this.getId() === id) {\n      return this;\n    }\n    for (var i = 0, children = this.children, len = children.length; i < len; i++) {\n      var res = children[i].getNodeById(id);\n      if (res) {\n        return res;\n      }\n    }\n  };\n  TreeNode.prototype.contains = function (node) {\n    if (node === this) {\n      return true;\n    }\n    for (var i = 0, children = this.children, len = children.length; i < len; i++) {\n      var res = children[i].contains(node);\n      if (res) {\n        return res;\n      }\n    }\n  };\n  /**\r\n   * @param includeSelf Default false.\r\n   * @return order: [root, child, grandchild, ...]\r\n   */\n  TreeNode.prototype.getAncestors = function (includeSelf) {\n    var ancestors = [];\n    var node = includeSelf ? this : this.parentNode;\n    while (node) {\n      ancestors.push(node);\n      node = node.parentNode;\n    }\n    ancestors.reverse();\n    return ancestors;\n  };\n  TreeNode.prototype.getAncestorsIndices = function () {\n    var indices = [];\n    var currNode = this;\n    while (currNode) {\n      indices.push(currNode.dataIndex);\n      currNode = currNode.parentNode;\n    }\n    indices.reverse();\n    return indices;\n  };\n  TreeNode.prototype.getDescendantIndices = function () {\n    var indices = [];\n    this.eachNode(function (childNode) {\n      indices.push(childNode.dataIndex);\n    });\n    return indices;\n  };\n  TreeNode.prototype.getValue = function (dimension) {\n    var data = this.hostTree.data;\n    return data.getStore().get(data.getDimensionIndex(dimension || 'value'), this.dataIndex);\n  };\n  TreeNode.prototype.setLayout = function (layout, merge) {\n    this.dataIndex >= 0 && this.hostTree.data.setItemLayout(this.dataIndex, layout, merge);\n  };\n  /**\r\n   * @return {Object} layout\r\n   */\n  TreeNode.prototype.getLayout = function () {\n    return this.hostTree.data.getItemLayout(this.dataIndex);\n  };\n  // @depcrecated\n  // getModel<T = unknown, S extends keyof T = keyof T>(path: S): Model<T[S]>\n  // eslint-disable-next-line @typescript-eslint/no-unused-vars\n  TreeNode.prototype.getModel = function (path) {\n    if (this.dataIndex < 0) {\n      return;\n    }\n    var hostTree = this.hostTree;\n    var itemModel = hostTree.data.getItemModel(this.dataIndex);\n    return itemModel.getModel(path);\n  };\n  // TODO: TYPE More specific model\n  TreeNode.prototype.getLevelModel = function () {\n    return (this.hostTree.levelModels || [])[this.depth];\n  };\n  TreeNode.prototype.setVisual = function (key, value) {\n    this.dataIndex >= 0 && this.hostTree.data.setItemVisual(this.dataIndex, key, value);\n  };\n  /**\r\n   * Get item visual\r\n   * FIXME: make return type better\r\n   */\n  TreeNode.prototype.getVisual = function (key) {\n    return this.hostTree.data.getItemVisual(this.dataIndex, key);\n  };\n  TreeNode.prototype.getRawIndex = function () {\n    return this.hostTree.data.getRawIndex(this.dataIndex);\n  };\n  TreeNode.prototype.getId = function () {\n    return this.hostTree.data.getId(this.dataIndex);\n  };\n  /**\r\n   * index in parent's children\r\n   */\n  TreeNode.prototype.getChildIndex = function () {\n    if (this.parentNode) {\n      var children = this.parentNode.children;\n      for (var i = 0; i < children.length; ++i) {\n        if (children[i] === this) {\n          return i;\n        }\n      }\n      return -1;\n    }\n    return -1;\n  };\n  /**\r\n   * if this is an ancestor of another node\r\n   *\r\n   * @param node another node\r\n   * @return if is ancestor\r\n   */\n  TreeNode.prototype.isAncestorOf = function (node) {\n    var parent = node.parentNode;\n    while (parent) {\n      if (parent === this) {\n        return true;\n      }\n      parent = parent.parentNode;\n    }\n    return false;\n  };\n  /**\r\n   * if this is an descendant of another node\r\n   *\r\n   * @param node another node\r\n   * @return if is descendant\r\n   */\n  TreeNode.prototype.isDescendantOf = function (node) {\n    return node !== this && node.isAncestorOf(this);\n  };\n  return TreeNode;\n}();\nexport { TreeNode };\n;\nvar Tree = /** @class */function () {\n  function Tree(hostModel) {\n    this.type = 'tree';\n    this._nodes = [];\n    this.hostModel = hostModel;\n  }\n  Tree.prototype.eachNode = function (options, cb, context) {\n    this.root.eachNode(options, cb, context);\n  };\n  Tree.prototype.getNodeByDataIndex = function (dataIndex) {\n    var rawIndex = this.data.getRawIndex(dataIndex);\n    return this._nodes[rawIndex];\n  };\n  Tree.prototype.getNodeById = function (name) {\n    return this.root.getNodeById(name);\n  };\n  /**\r\n   * Update item available by list,\r\n   * when list has been performed options like 'filterSelf' or 'map'.\r\n   */\n  Tree.prototype.update = function () {\n    var data = this.data;\n    var nodes = this._nodes;\n    for (var i = 0, len = nodes.length; i < len; i++) {\n      nodes[i].dataIndex = -1;\n    }\n    for (var i = 0, len = data.count(); i < len; i++) {\n      nodes[data.getRawIndex(i)].dataIndex = i;\n    }\n  };\n  /**\r\n   * Clear all layouts\r\n   */\n  Tree.prototype.clearLayouts = function () {\n    this.data.clearItemLayouts();\n  };\n  /**\r\n   * data node format:\r\n   * {\r\n   *     name: ...\r\n   *     value: ...\r\n   *     children: [\r\n   *         {\r\n   *             name: ...\r\n   *             value: ...\r\n   *             children: ...\r\n   *         },\r\n   *         ...\r\n   *     ]\r\n   * }\r\n   */\n  Tree.createTree = function (dataRoot, hostModel, beforeLink) {\n    var tree = new Tree(hostModel);\n    var listData = [];\n    var dimMax = 1;\n    buildHierarchy(dataRoot);\n    function buildHierarchy(dataNode, parentNode) {\n      var value = dataNode.value;\n      dimMax = Math.max(dimMax, zrUtil.isArray(value) ? value.length : 1);\n      listData.push(dataNode);\n      var node = new TreeNode(convertOptionIdName(dataNode.name, ''), tree);\n      parentNode ? addChild(node, parentNode) : tree.root = node;\n      tree._nodes.push(node);\n      var children = dataNode.children;\n      if (children) {\n        for (var i = 0; i < children.length; i++) {\n          buildHierarchy(children[i], node);\n        }\n      }\n    }\n    tree.root.updateDepthAndHeight(0);\n    var dimensions = prepareSeriesDataSchema(listData, {\n      coordDimensions: ['value'],\n      dimensionsCount: dimMax\n    }).dimensions;\n    var list = new SeriesData(dimensions, hostModel);\n    list.initData(listData);\n    beforeLink && beforeLink(list);\n    linkSeriesData({\n      mainData: list,\n      struct: tree,\n      structAttr: 'tree'\n    });\n    tree.update();\n    return tree;\n  };\n  return Tree;\n}();\n/**\r\n * It is needed to consider the mess of 'list', 'hostModel' when creating a TreeNote,\r\n * so this function is not ready and not necessary to be public.\r\n */\nfunction addChild(child, node) {\n  var children = node.children;\n  if (child.parentNode === node) {\n    return;\n  }\n  children.push(child);\n  child.parentNode = node;\n}\nexport default Tree;"], "names": [], "mappings": "AACA;;;;;;;;;;;;;;;;;AAiBA,GAGA;;CAEC,GAED;;;;;;;;;;;;;;;;;AAiBA,GACA;;CAEC;;;;AACD;AACA;AACA;AACA;AACA;;;;;;AACA,IAAI,WAAW,WAAW,GAAE;IAC1B,SAAS,SAAS,IAAI,EAAE,QAAQ;QAC9B,IAAI,CAAC,KAAK,GAAG;QACb,IAAI,CAAC,MAAM,GAAG;QACd;;;;;;KAMC,GACD,IAAI,CAAC,SAAS,GAAG,CAAC;QAClB,IAAI,CAAC,QAAQ,GAAG,EAAE;QAClB,IAAI,CAAC,YAAY,GAAG,EAAE;QACtB,IAAI,CAAC,QAAQ,GAAG;QAChB,IAAI,CAAC,IAAI,GAAG,QAAQ;QACpB,IAAI,CAAC,QAAQ,GAAG;IAClB;IACA;;GAEC,GACD,SAAS,SAAS,CAAC,SAAS,GAAG;QAC7B,OAAO,IAAI,CAAC,SAAS,GAAG;IAC1B;IACA,SAAS,SAAS,CAAC,QAAQ,GAAG,SAAU,OAAO,EAAE,EAAE,EAAE,OAAO;QAC1D,IAAI,CAAA,GAAA,8IAAA,CAAA,aAAiB,AAAD,EAAE,UAAU;YAC9B,UAAU;YACV,KAAK;YACL,UAAU;QACZ;QACA,UAAU,WAAW,CAAC;QACtB,IAAI,CAAA,GAAA,8IAAA,CAAA,WAAe,AAAD,EAAE,UAAU;YAC5B,UAAU;gBACR,OAAO;YACT;QACF;QACA,IAAI,QAAQ,QAAQ,KAAK,IAAI;QAC7B,IAAI,WAAW,IAAI,CAAC,QAAQ,IAAI,IAAI,WAAW;QAC/C,IAAI;QACJ,UAAU,cAAc,CAAC,mBAAmB,GAAG,IAAI,CAAC,SAAS,IAAI,CAAC;QAClE,IAAK,IAAI,IAAI,GAAG,CAAC,oBAAoB,IAAI,SAAS,MAAM,EAAE,IAAK;YAC7D,QAAQ,CAAC,EAAE,CAAC,QAAQ,CAAC,SAAS,IAAI;QACpC;QACA,UAAU,eAAe,GAAG,IAAI,CAAC,SAAS,IAAI;IAChD;IACA;;GAEC,GACD,SAAS,SAAS,CAAC,oBAAoB,GAAG,SAAU,KAAK;QACvD,IAAI,SAAS;QACb,IAAI,CAAC,KAAK,GAAG;QACb,IAAK,IAAI,IAAI,GAAG,IAAI,IAAI,CAAC,QAAQ,CAAC,MAAM,EAAE,IAAK;YAC7C,IAAI,QAAQ,IAAI,CAAC,QAAQ,CAAC,EAAE;YAC5B,MAAM,oBAAoB,CAAC,QAAQ;YACnC,IAAI,MAAM,MAAM,GAAG,QAAQ;gBACzB,SAAS,MAAM,MAAM;YACvB;QACF;QACA,IAAI,CAAC,MAAM,GAAG,SAAS;IACzB;IACA,SAAS,SAAS,CAAC,WAAW,GAAG,SAAU,EAAE;QAC3C,IAAI,IAAI,CAAC,KAAK,OAAO,IAAI;YACvB,OAAO,IAAI;QACb;QACA,IAAK,IAAI,IAAI,GAAG,WAAW,IAAI,CAAC,QAAQ,EAAE,MAAM,SAAS,MAAM,EAAE,IAAI,KAAK,IAAK;YAC7E,IAAI,MAAM,QAAQ,CAAC,EAAE,CAAC,WAAW,CAAC;YAClC,IAAI,KAAK;gBACP,OAAO;YACT;QACF;IACF;IACA,SAAS,SAAS,CAAC,QAAQ,GAAG,SAAU,IAAI;QAC1C,IAAI,SAAS,IAAI,EAAE;YACjB,OAAO;QACT;QACA,IAAK,IAAI,IAAI,GAAG,WAAW,IAAI,CAAC,QAAQ,EAAE,MAAM,SAAS,MAAM,EAAE,IAAI,KAAK,IAAK;YAC7E,IAAI,MAAM,QAAQ,CAAC,EAAE,CAAC,QAAQ,CAAC;YAC/B,IAAI,KAAK;gBACP,OAAO;YACT;QACF;IACF;IACA;;;GAGC,GACD,SAAS,SAAS,CAAC,YAAY,GAAG,SAAU,WAAW;QACrD,IAAI,YAAY,EAAE;QAClB,IAAI,OAAO,cAAc,IAAI,GAAG,IAAI,CAAC,UAAU;QAC/C,MAAO,KAAM;YACX,UAAU,IAAI,CAAC;YACf,OAAO,KAAK,UAAU;QACxB;QACA,UAAU,OAAO;QACjB,OAAO;IACT;IACA,SAAS,SAAS,CAAC,mBAAmB,GAAG;QACvC,IAAI,UAAU,EAAE;QAChB,IAAI,WAAW,IAAI;QACnB,MAAO,SAAU;YACf,QAAQ,IAAI,CAAC,SAAS,SAAS;YAC/B,WAAW,SAAS,UAAU;QAChC;QACA,QAAQ,OAAO;QACf,OAAO;IACT;IACA,SAAS,SAAS,CAAC,oBAAoB,GAAG;QACxC,IAAI,UAAU,EAAE;QAChB,IAAI,CAAC,QAAQ,CAAC,SAAU,SAAS;YAC/B,QAAQ,IAAI,CAAC,UAAU,SAAS;QAClC;QACA,OAAO;IACT;IACA,SAAS,SAAS,CAAC,QAAQ,GAAG,SAAU,SAAS;QAC/C,IAAI,OAAO,IAAI,CAAC,QAAQ,CAAC,IAAI;QAC7B,OAAO,KAAK,QAAQ,GAAG,GAAG,CAAC,KAAK,iBAAiB,CAAC,aAAa,UAAU,IAAI,CAAC,SAAS;IACzF;IACA,SAAS,SAAS,CAAC,SAAS,GAAG,SAAU,MAAM,EAAE,KAAK;QACpD,IAAI,CAAC,SAAS,IAAI,KAAK,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,SAAS,EAAE,QAAQ;IAClF;IACA;;GAEC,GACD,SAAS,SAAS,CAAC,SAAS,GAAG;QAC7B,OAAO,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,SAAS;IACxD;IACA,eAAe;IACf,2EAA2E;IAC3E,6DAA6D;IAC7D,SAAS,SAAS,CAAC,QAAQ,GAAG,SAAU,IAAI;QAC1C,IAAI,IAAI,CAAC,SAAS,GAAG,GAAG;YACtB;QACF;QACA,IAAI,WAAW,IAAI,CAAC,QAAQ;QAC5B,IAAI,YAAY,SAAS,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,SAAS;QACzD,OAAO,UAAU,QAAQ,CAAC;IAC5B;IACA,iCAAiC;IACjC,SAAS,SAAS,CAAC,aAAa,GAAG;QACjC,OAAO,CAAC,IAAI,CAAC,QAAQ,CAAC,WAAW,IAAI,EAAE,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC;IACtD;IACA,SAAS,SAAS,CAAC,SAAS,GAAG,SAAU,GAAG,EAAE,KAAK;QACjD,IAAI,CAAC,SAAS,IAAI,KAAK,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,SAAS,EAAE,KAAK;IAC/E;IACA;;;GAGC,GACD,SAAS,SAAS,CAAC,SAAS,GAAG,SAAU,GAAG;QAC1C,OAAO,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,SAAS,EAAE;IAC1D;IACA,SAAS,SAAS,CAAC,WAAW,GAAG;QAC/B,OAAO,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,SAAS;IACtD;IACA,SAAS,SAAS,CAAC,KAAK,GAAG;QACzB,OAAO,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,SAAS;IAChD;IACA;;GAEC,GACD,SAAS,SAAS,CAAC,aAAa,GAAG;QACjC,IAAI,IAAI,CAAC,UAAU,EAAE;YACnB,IAAI,WAAW,IAAI,CAAC,UAAU,CAAC,QAAQ;YACvC,IAAK,IAAI,IAAI,GAAG,IAAI,SAAS,MAAM,EAAE,EAAE,EAAG;gBACxC,IAAI,QAAQ,CAAC,EAAE,KAAK,IAAI,EAAE;oBACxB,OAAO;gBACT;YACF;YACA,OAAO,CAAC;QACV;QACA,OAAO,CAAC;IACV;IACA;;;;;GAKC,GACD,SAAS,SAAS,CAAC,YAAY,GAAG,SAAU,IAAI;QAC9C,IAAI,SAAS,KAAK,UAAU;QAC5B,MAAO,OAAQ;YACb,IAAI,WAAW,IAAI,EAAE;gBACnB,OAAO;YACT;YACA,SAAS,OAAO,UAAU;QAC5B;QACA,OAAO;IACT;IACA;;;;;GAKC,GACD,SAAS,SAAS,CAAC,cAAc,GAAG,SAAU,IAAI;QAChD,OAAO,SAAS,IAAI,IAAI,KAAK,YAAY,CAAC,IAAI;IAChD;IACA,OAAO;AACT;;;AAGA,IAAI,OAAO,WAAW,GAAE;IACtB,SAAS,KAAK,SAAS;QACrB,IAAI,CAAC,IAAI,GAAG;QACZ,IAAI,CAAC,MAAM,GAAG,EAAE;QAChB,IAAI,CAAC,SAAS,GAAG;IACnB;IACA,KAAK,SAAS,CAAC,QAAQ,GAAG,SAAU,OAAO,EAAE,EAAE,EAAE,OAAO;QACtD,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,SAAS,IAAI;IAClC;IACA,KAAK,SAAS,CAAC,kBAAkB,GAAG,SAAU,SAAS;QACrD,IAAI,WAAW,IAAI,CAAC,IAAI,CAAC,WAAW,CAAC;QACrC,OAAO,IAAI,CAAC,MAAM,CAAC,SAAS;IAC9B;IACA,KAAK,SAAS,CAAC,WAAW,GAAG,SAAU,IAAI;QACzC,OAAO,IAAI,CAAC,IAAI,CAAC,WAAW,CAAC;IAC/B;IACA;;;GAGC,GACD,KAAK,SAAS,CAAC,MAAM,GAAG;QACtB,IAAI,OAAO,IAAI,CAAC,IAAI;QACpB,IAAI,QAAQ,IAAI,CAAC,MAAM;QACvB,IAAK,IAAI,IAAI,GAAG,MAAM,MAAM,MAAM,EAAE,IAAI,KAAK,IAAK;YAChD,KAAK,CAAC,EAAE,CAAC,SAAS,GAAG,CAAC;QACxB;QACA,IAAK,IAAI,IAAI,GAAG,MAAM,KAAK,KAAK,IAAI,IAAI,KAAK,IAAK;YAChD,KAAK,CAAC,KAAK,WAAW,CAAC,GAAG,CAAC,SAAS,GAAG;QACzC;IACF;IACA;;GAEC,GACD,KAAK,SAAS,CAAC,YAAY,GAAG;QAC5B,IAAI,CAAC,IAAI,CAAC,gBAAgB;IAC5B;IACA;;;;;;;;;;;;;;GAcC,GACD,KAAK,UAAU,GAAG,SAAU,QAAQ,EAAE,SAAS,EAAE,UAAU;QACzD,IAAI,OAAO,IAAI,KAAK;QACpB,IAAI,WAAW,EAAE;QACjB,IAAI,SAAS;QACb,eAAe;QACf,SAAS,eAAe,QAAQ,EAAE,UAAU;YAC1C,IAAI,QAAQ,SAAS,KAAK;YAC1B,SAAS,KAAK,GAAG,CAAC,QAAQ,CAAA,GAAA,8IAAA,CAAA,UAAc,AAAD,EAAE,SAAS,MAAM,MAAM,GAAG;YACjE,SAAS,IAAI,CAAC;YACd,IAAI,OAAO,IAAI,SAAS,CAAA,GAAA,+IAAA,CAAA,sBAAmB,AAAD,EAAE,SAAS,IAAI,EAAE,KAAK;YAChE,aAAa,SAAS,MAAM,cAAc,KAAK,IAAI,GAAG;YACtD,KAAK,MAAM,CAAC,IAAI,CAAC;YACjB,IAAI,WAAW,SAAS,QAAQ;YAChC,IAAI,UAAU;gBACZ,IAAK,IAAI,IAAI,GAAG,IAAI,SAAS,MAAM,EAAE,IAAK;oBACxC,eAAe,QAAQ,CAAC,EAAE,EAAE;gBAC9B;YACF;QACF;QACA,KAAK,IAAI,CAAC,oBAAoB,CAAC;QAC/B,IAAI,aAAa,CAAA,GAAA,oKAAA,CAAA,UAAuB,AAAD,EAAE,UAAU;YACjD,iBAAiB;gBAAC;aAAQ;YAC1B,iBAAiB;QACnB,GAAG,UAAU;QACb,IAAI,OAAO,IAAI,oJAAA,CAAA,UAAU,CAAC,YAAY;QACtC,KAAK,QAAQ,CAAC;QACd,cAAc,WAAW;QACzB,CAAA,GAAA,kKAAA,CAAA,UAAc,AAAD,EAAE;YACb,UAAU;YACV,QAAQ;YACR,YAAY;QACd;QACA,KAAK,MAAM;QACX,OAAO;IACT;IACA,OAAO;AACT;AACA;;;CAGC,GACD,SAAS,SAAS,KAAK,EAAE,IAAI;IAC3B,IAAI,WAAW,KAAK,QAAQ;IAC5B,IAAI,MAAM,UAAU,KAAK,MAAM;QAC7B;IACF;IACA,SAAS,IAAI,CAAC;IACd,MAAM,UAAU,GAAG;AACrB;uCACe", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 5880, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/tindahan/sari-sari-admin/node_modules/echarts/lib/data/Graph.js"], "sourcesContent": ["\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\r\n* Licensed to the Apache Software Foundation (ASF) under one\r\n* or more contributor license agreements.  See the NOTICE file\r\n* distributed with this work for additional information\r\n* regarding copyright ownership.  The ASF licenses this file\r\n* to you under the Apache License, Version 2.0 (the\r\n* \"License\"); you may not use this file except in compliance\r\n* with the License.  You may obtain a copy of the License at\r\n*\r\n*   http://www.apache.org/licenses/LICENSE-2.0\r\n*\r\n* Unless required by applicable law or agreed to in writing,\r\n* software distributed under the License is distributed on an\r\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\r\n* KIND, either express or implied.  See the License for the\r\n* specific language governing permissions and limitations\r\n* under the License.\r\n*/\nimport * as zrUtil from 'zrender/lib/core/util.js';\n// id may be function name of Object, add a prefix to avoid this problem.\nfunction generateNodeKey(id) {\n  return '_EC_' + id;\n}\nvar Graph = /** @class */function () {\n  function Graph(directed) {\n    this.type = 'graph';\n    this.nodes = [];\n    this.edges = [];\n    this._nodesMap = {};\n    /**\r\n     * @type {Object.<string, module:echarts/data/Graph.Edge>}\r\n     * @private\r\n     */\n    this._edgesMap = {};\n    this._directed = directed || false;\n  }\n  /**\r\n   * If is directed graph\r\n   */\n  Graph.prototype.isDirected = function () {\n    return this._directed;\n  };\n  ;\n  /**\r\n   * Add a new node\r\n   */\n  Graph.prototype.addNode = function (id, dataIndex) {\n    id = id == null ? '' + dataIndex : '' + id;\n    var nodesMap = this._nodesMap;\n    if (nodesMap[generateNodeKey(id)]) {\n      if (process.env.NODE_ENV !== 'production') {\n        console.error('Graph nodes have duplicate name or id');\n      }\n      return;\n    }\n    var node = new GraphNode(id, dataIndex);\n    node.hostGraph = this;\n    this.nodes.push(node);\n    nodesMap[generateNodeKey(id)] = node;\n    return node;\n  };\n  ;\n  /**\r\n   * Get node by data index\r\n   */\n  Graph.prototype.getNodeByIndex = function (dataIndex) {\n    var rawIdx = this.data.getRawIndex(dataIndex);\n    return this.nodes[rawIdx];\n  };\n  ;\n  /**\r\n   * Get node by id\r\n   */\n  Graph.prototype.getNodeById = function (id) {\n    return this._nodesMap[generateNodeKey(id)];\n  };\n  ;\n  /**\r\n   * Add a new edge\r\n   */\n  Graph.prototype.addEdge = function (n1, n2, dataIndex) {\n    var nodesMap = this._nodesMap;\n    var edgesMap = this._edgesMap;\n    // PENDING\n    if (zrUtil.isNumber(n1)) {\n      n1 = this.nodes[n1];\n    }\n    if (zrUtil.isNumber(n2)) {\n      n2 = this.nodes[n2];\n    }\n    if (!(n1 instanceof GraphNode)) {\n      n1 = nodesMap[generateNodeKey(n1)];\n    }\n    if (!(n2 instanceof GraphNode)) {\n      n2 = nodesMap[generateNodeKey(n2)];\n    }\n    if (!n1 || !n2) {\n      return;\n    }\n    var key = n1.id + '-' + n2.id;\n    var edge = new GraphEdge(n1, n2, dataIndex);\n    edge.hostGraph = this;\n    if (this._directed) {\n      n1.outEdges.push(edge);\n      n2.inEdges.push(edge);\n    }\n    n1.edges.push(edge);\n    if (n1 !== n2) {\n      n2.edges.push(edge);\n    }\n    this.edges.push(edge);\n    edgesMap[key] = edge;\n    return edge;\n  };\n  ;\n  /**\r\n   * Get edge by data index\r\n   */\n  Graph.prototype.getEdgeByIndex = function (dataIndex) {\n    var rawIdx = this.edgeData.getRawIndex(dataIndex);\n    return this.edges[rawIdx];\n  };\n  ;\n  /**\r\n   * Get edge by two linked nodes\r\n   */\n  Graph.prototype.getEdge = function (n1, n2) {\n    if (n1 instanceof GraphNode) {\n      n1 = n1.id;\n    }\n    if (n2 instanceof GraphNode) {\n      n2 = n2.id;\n    }\n    var edgesMap = this._edgesMap;\n    if (this._directed) {\n      return edgesMap[n1 + '-' + n2];\n    } else {\n      return edgesMap[n1 + '-' + n2] || edgesMap[n2 + '-' + n1];\n    }\n  };\n  ;\n  /**\r\n   * Iterate all nodes\r\n   */\n  Graph.prototype.eachNode = function (cb, context) {\n    var nodes = this.nodes;\n    var len = nodes.length;\n    for (var i = 0; i < len; i++) {\n      if (nodes[i].dataIndex >= 0) {\n        cb.call(context, nodes[i], i);\n      }\n    }\n  };\n  ;\n  /**\r\n   * Iterate all edges\r\n   */\n  Graph.prototype.eachEdge = function (cb, context) {\n    var edges = this.edges;\n    var len = edges.length;\n    for (var i = 0; i < len; i++) {\n      if (edges[i].dataIndex >= 0 && edges[i].node1.dataIndex >= 0 && edges[i].node2.dataIndex >= 0) {\n        cb.call(context, edges[i], i);\n      }\n    }\n  };\n  ;\n  /**\r\n   * Breadth first traverse\r\n   * Return true to stop traversing\r\n   */\n  Graph.prototype.breadthFirstTraverse = function (cb, startNode, direction, context) {\n    if (!(startNode instanceof GraphNode)) {\n      startNode = this._nodesMap[generateNodeKey(startNode)];\n    }\n    if (!startNode) {\n      return;\n    }\n    var edgeType = direction === 'out' ? 'outEdges' : direction === 'in' ? 'inEdges' : 'edges';\n    for (var i = 0; i < this.nodes.length; i++) {\n      this.nodes[i].__visited = false;\n    }\n    if (cb.call(context, startNode, null)) {\n      return;\n    }\n    var queue = [startNode];\n    while (queue.length) {\n      var currentNode = queue.shift();\n      var edges = currentNode[edgeType];\n      for (var i = 0; i < edges.length; i++) {\n        var e = edges[i];\n        var otherNode = e.node1 === currentNode ? e.node2 : e.node1;\n        if (!otherNode.__visited) {\n          if (cb.call(context, otherNode, currentNode)) {\n            // Stop traversing\n            return;\n          }\n          queue.push(otherNode);\n          otherNode.__visited = true;\n        }\n      }\n    }\n  };\n  ;\n  // TODO\n  // depthFirstTraverse(\n  //     cb, startNode, direction, context\n  // ) {\n  // };\n  // Filter update\n  Graph.prototype.update = function () {\n    var data = this.data;\n    var edgeData = this.edgeData;\n    var nodes = this.nodes;\n    var edges = this.edges;\n    for (var i = 0, len = nodes.length; i < len; i++) {\n      nodes[i].dataIndex = -1;\n    }\n    for (var i = 0, len = data.count(); i < len; i++) {\n      nodes[data.getRawIndex(i)].dataIndex = i;\n    }\n    edgeData.filterSelf(function (idx) {\n      var edge = edges[edgeData.getRawIndex(idx)];\n      return edge.node1.dataIndex >= 0 && edge.node2.dataIndex >= 0;\n    });\n    // Update edge\n    for (var i = 0, len = edges.length; i < len; i++) {\n      edges[i].dataIndex = -1;\n    }\n    for (var i = 0, len = edgeData.count(); i < len; i++) {\n      edges[edgeData.getRawIndex(i)].dataIndex = i;\n    }\n  };\n  ;\n  /**\r\n   * @return {module:echarts/data/Graph}\r\n   */\n  Graph.prototype.clone = function () {\n    var graph = new Graph(this._directed);\n    var nodes = this.nodes;\n    var edges = this.edges;\n    for (var i = 0; i < nodes.length; i++) {\n      graph.addNode(nodes[i].id, nodes[i].dataIndex);\n    }\n    for (var i = 0; i < edges.length; i++) {\n      var e = edges[i];\n      graph.addEdge(e.node1.id, e.node2.id, e.dataIndex);\n    }\n    return graph;\n  };\n  ;\n  return Graph;\n}();\nvar GraphNode = /** @class */function () {\n  function GraphNode(id, dataIndex) {\n    this.inEdges = [];\n    this.outEdges = [];\n    this.edges = [];\n    this.dataIndex = -1;\n    this.id = id == null ? '' : id;\n    this.dataIndex = dataIndex == null ? -1 : dataIndex;\n  }\n  /**\r\n   * @return {number}\r\n   */\n  GraphNode.prototype.degree = function () {\n    return this.edges.length;\n  };\n  /**\r\n   * @return {number}\r\n   */\n  GraphNode.prototype.inDegree = function () {\n    return this.inEdges.length;\n  };\n  /**\r\n  * @return {number}\r\n  */\n  GraphNode.prototype.outDegree = function () {\n    return this.outEdges.length;\n  };\n  GraphNode.prototype.getModel = function (path) {\n    if (this.dataIndex < 0) {\n      return;\n    }\n    var graph = this.hostGraph;\n    var itemModel = graph.data.getItemModel(this.dataIndex);\n    return itemModel.getModel(path);\n  };\n  GraphNode.prototype.getAdjacentDataIndices = function () {\n    var dataIndices = {\n      edge: [],\n      node: []\n    };\n    for (var i = 0; i < this.edges.length; i++) {\n      var adjacentEdge = this.edges[i];\n      if (adjacentEdge.dataIndex < 0) {\n        continue;\n      }\n      dataIndices.edge.push(adjacentEdge.dataIndex);\n      dataIndices.node.push(adjacentEdge.node1.dataIndex, adjacentEdge.node2.dataIndex);\n    }\n    return dataIndices;\n  };\n  GraphNode.prototype.getTrajectoryDataIndices = function () {\n    var connectedEdgesMap = zrUtil.createHashMap();\n    var connectedNodesMap = zrUtil.createHashMap();\n    for (var i = 0; i < this.edges.length; i++) {\n      var adjacentEdge = this.edges[i];\n      if (adjacentEdge.dataIndex < 0) {\n        continue;\n      }\n      connectedEdgesMap.set(adjacentEdge.dataIndex, true);\n      var sourceNodesQueue = [adjacentEdge.node1];\n      var targetNodesQueue = [adjacentEdge.node2];\n      var nodeIteratorIndex = 0;\n      while (nodeIteratorIndex < sourceNodesQueue.length) {\n        var sourceNode = sourceNodesQueue[nodeIteratorIndex];\n        nodeIteratorIndex++;\n        connectedNodesMap.set(sourceNode.dataIndex, true);\n        for (var j = 0; j < sourceNode.inEdges.length; j++) {\n          connectedEdgesMap.set(sourceNode.inEdges[j].dataIndex, true);\n          sourceNodesQueue.push(sourceNode.inEdges[j].node1);\n        }\n      }\n      nodeIteratorIndex = 0;\n      while (nodeIteratorIndex < targetNodesQueue.length) {\n        var targetNode = targetNodesQueue[nodeIteratorIndex];\n        nodeIteratorIndex++;\n        connectedNodesMap.set(targetNode.dataIndex, true);\n        for (var j = 0; j < targetNode.outEdges.length; j++) {\n          connectedEdgesMap.set(targetNode.outEdges[j].dataIndex, true);\n          targetNodesQueue.push(targetNode.outEdges[j].node2);\n        }\n      }\n    }\n    return {\n      edge: connectedEdgesMap.keys(),\n      node: connectedNodesMap.keys()\n    };\n  };\n  return GraphNode;\n}();\nvar GraphEdge = /** @class */function () {\n  function GraphEdge(n1, n2, dataIndex) {\n    this.dataIndex = -1;\n    this.node1 = n1;\n    this.node2 = n2;\n    this.dataIndex = dataIndex == null ? -1 : dataIndex;\n  }\n  // eslint-disable-next-line @typescript-eslint/no-unused-vars\n  GraphEdge.prototype.getModel = function (path) {\n    if (this.dataIndex < 0) {\n      return;\n    }\n    var graph = this.hostGraph;\n    var itemModel = graph.edgeData.getItemModel(this.dataIndex);\n    return itemModel.getModel(path);\n  };\n  GraphEdge.prototype.getAdjacentDataIndices = function () {\n    return {\n      edge: [this.dataIndex],\n      node: [this.node1.dataIndex, this.node2.dataIndex]\n    };\n  };\n  GraphEdge.prototype.getTrajectoryDataIndices = function () {\n    var connectedEdgesMap = zrUtil.createHashMap();\n    var connectedNodesMap = zrUtil.createHashMap();\n    connectedEdgesMap.set(this.dataIndex, true);\n    var sourceNodes = [this.node1];\n    var targetNodes = [this.node2];\n    var nodeIteratorIndex = 0;\n    while (nodeIteratorIndex < sourceNodes.length) {\n      var sourceNode = sourceNodes[nodeIteratorIndex];\n      nodeIteratorIndex++;\n      connectedNodesMap.set(sourceNode.dataIndex, true);\n      for (var j = 0; j < sourceNode.inEdges.length; j++) {\n        connectedEdgesMap.set(sourceNode.inEdges[j].dataIndex, true);\n        sourceNodes.push(sourceNode.inEdges[j].node1);\n      }\n    }\n    nodeIteratorIndex = 0;\n    while (nodeIteratorIndex < targetNodes.length) {\n      var targetNode = targetNodes[nodeIteratorIndex];\n      nodeIteratorIndex++;\n      connectedNodesMap.set(targetNode.dataIndex, true);\n      for (var j = 0; j < targetNode.outEdges.length; j++) {\n        connectedEdgesMap.set(targetNode.outEdges[j].dataIndex, true);\n        targetNodes.push(targetNode.outEdges[j].node2);\n      }\n    }\n    return {\n      edge: connectedEdgesMap.keys(),\n      node: connectedNodesMap.keys()\n    };\n  };\n  return GraphEdge;\n}();\nfunction createGraphDataProxyMixin(hostName, dataName) {\n  return {\n    /**\r\n     * @param Default 'value'. can be 'a', 'b', 'c', 'd', 'e'.\r\n     */\n    getValue: function (dimension) {\n      var data = this[hostName][dataName];\n      return data.getStore().get(data.getDimensionIndex(dimension || 'value'), this.dataIndex);\n    },\n    // TODO: TYPE stricter type.\n    setVisual: function (key, value) {\n      this.dataIndex >= 0 && this[hostName][dataName].setItemVisual(this.dataIndex, key, value);\n    },\n    getVisual: function (key) {\n      return this[hostName][dataName].getItemVisual(this.dataIndex, key);\n    },\n    setLayout: function (layout, merge) {\n      this.dataIndex >= 0 && this[hostName][dataName].setItemLayout(this.dataIndex, layout, merge);\n    },\n    getLayout: function () {\n      return this[hostName][dataName].getItemLayout(this.dataIndex);\n    },\n    getGraphicEl: function () {\n      return this[hostName][dataName].getItemGraphicEl(this.dataIndex);\n    },\n    getRawIndex: function () {\n      return this[hostName][dataName].getRawIndex(this.dataIndex);\n    }\n  };\n}\n;\n;\n;\nzrUtil.mixin(GraphNode, createGraphDataProxyMixin('hostGraph', 'data'));\nzrUtil.mixin(GraphEdge, createGraphDataProxyMixin('hostGraph', 'edgeData'));\nexport default Graph;\nexport { GraphNode, GraphEdge };"], "names": [], "mappings": "AACA;;;;;;;;;;;;;;;;;AAiBA,GAGA;;CAEC,GAED;;;;;;;;;;;;;;;;;AAiBA;;;;;AACA;;AACA,yEAAyE;AACzE,SAAS,gBAAgB,EAAE;IACzB,OAAO,SAAS;AAClB;AACA,IAAI,QAAQ,WAAW,GAAE;IACvB,SAAS,MAAM,QAAQ;QACrB,IAAI,CAAC,IAAI,GAAG;QACZ,IAAI,CAAC,KAAK,GAAG,EAAE;QACf,IAAI,CAAC,KAAK,GAAG,EAAE;QACf,IAAI,CAAC,SAAS,GAAG,CAAC;QAClB;;;KAGC,GACD,IAAI,CAAC,SAAS,GAAG,CAAC;QAClB,IAAI,CAAC,SAAS,GAAG,YAAY;IAC/B;IACA;;GAEC,GACD,MAAM,SAAS,CAAC,UAAU,GAAG;QAC3B,OAAO,IAAI,CAAC,SAAS;IACvB;;IAEA;;GAEC,GACD,MAAM,SAAS,CAAC,OAAO,GAAG,SAAU,EAAE,EAAE,SAAS;QAC/C,KAAK,MAAM,OAAO,KAAK,YAAY,KAAK;QACxC,IAAI,WAAW,IAAI,CAAC,SAAS;QAC7B,IAAI,QAAQ,CAAC,gBAAgB,IAAI,EAAE;YACjC,wCAA2C;gBACzC,QAAQ,KAAK,CAAC;YAChB;YACA;QACF;QACA,IAAI,OAAO,IAAI,UAAU,IAAI;QAC7B,KAAK,SAAS,GAAG,IAAI;QACrB,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC;QAChB,QAAQ,CAAC,gBAAgB,IAAI,GAAG;QAChC,OAAO;IACT;;IAEA;;GAEC,GACD,MAAM,SAAS,CAAC,cAAc,GAAG,SAAU,SAAS;QAClD,IAAI,SAAS,IAAI,CAAC,IAAI,CAAC,WAAW,CAAC;QACnC,OAAO,IAAI,CAAC,KAAK,CAAC,OAAO;IAC3B;;IAEA;;GAEC,GACD,MAAM,SAAS,CAAC,WAAW,GAAG,SAAU,EAAE;QACxC,OAAO,IAAI,CAAC,SAAS,CAAC,gBAAgB,IAAI;IAC5C;;IAEA;;GAEC,GACD,MAAM,SAAS,CAAC,OAAO,GAAG,SAAU,EAAE,EAAE,EAAE,EAAE,SAAS;QACnD,IAAI,WAAW,IAAI,CAAC,SAAS;QAC7B,IAAI,WAAW,IAAI,CAAC,SAAS;QAC7B,UAAU;QACV,IAAI,CAAA,GAAA,8IAAA,CAAA,WAAe,AAAD,EAAE,KAAK;YACvB,KAAK,IAAI,CAAC,KAAK,CAAC,GAAG;QACrB;QACA,IAAI,CAAA,GAAA,8IAAA,CAAA,WAAe,AAAD,EAAE,KAAK;YACvB,KAAK,IAAI,CAAC,KAAK,CAAC,GAAG;QACrB;QACA,IAAI,CAAC,CAAC,cAAc,SAAS,GAAG;YAC9B,KAAK,QAAQ,CAAC,gBAAgB,IAAI;QACpC;QACA,IAAI,CAAC,CAAC,cAAc,SAAS,GAAG;YAC9B,KAAK,QAAQ,CAAC,gBAAgB,IAAI;QACpC;QACA,IAAI,CAAC,MAAM,CAAC,IAAI;YACd;QACF;QACA,IAAI,MAAM,GAAG,EAAE,GAAG,MAAM,GAAG,EAAE;QAC7B,IAAI,OAAO,IAAI,UAAU,IAAI,IAAI;QACjC,KAAK,SAAS,GAAG,IAAI;QACrB,IAAI,IAAI,CAAC,SAAS,EAAE;YAClB,GAAG,QAAQ,CAAC,IAAI,CAAC;YACjB,GAAG,OAAO,CAAC,IAAI,CAAC;QAClB;QACA,GAAG,KAAK,CAAC,IAAI,CAAC;QACd,IAAI,OAAO,IAAI;YACb,GAAG,KAAK,CAAC,IAAI,CAAC;QAChB;QACA,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC;QAChB,QAAQ,CAAC,IAAI,GAAG;QAChB,OAAO;IACT;;IAEA;;GAEC,GACD,MAAM,SAAS,CAAC,cAAc,GAAG,SAAU,SAAS;QAClD,IAAI,SAAS,IAAI,CAAC,QAAQ,CAAC,WAAW,CAAC;QACvC,OAAO,IAAI,CAAC,KAAK,CAAC,OAAO;IAC3B;;IAEA;;GAEC,GACD,MAAM,SAAS,CAAC,OAAO,GAAG,SAAU,EAAE,EAAE,EAAE;QACxC,IAAI,cAAc,WAAW;YAC3B,KAAK,GAAG,EAAE;QACZ;QACA,IAAI,cAAc,WAAW;YAC3B,KAAK,GAAG,EAAE;QACZ;QACA,IAAI,WAAW,IAAI,CAAC,SAAS;QAC7B,IAAI,IAAI,CAAC,SAAS,EAAE;YAClB,OAAO,QAAQ,CAAC,KAAK,MAAM,GAAG;QAChC,OAAO;YACL,OAAO,QAAQ,CAAC,KAAK,MAAM,GAAG,IAAI,QAAQ,CAAC,KAAK,MAAM,GAAG;QAC3D;IACF;;IAEA;;GAEC,GACD,MAAM,SAAS,CAAC,QAAQ,GAAG,SAAU,EAAE,EAAE,OAAO;QAC9C,IAAI,QAAQ,IAAI,CAAC,KAAK;QACtB,IAAI,MAAM,MAAM,MAAM;QACtB,IAAK,IAAI,IAAI,GAAG,IAAI,KAAK,IAAK;YAC5B,IAAI,KAAK,CAAC,EAAE,CAAC,SAAS,IAAI,GAAG;gBAC3B,GAAG,IAAI,CAAC,SAAS,KAAK,CAAC,EAAE,EAAE;YAC7B;QACF;IACF;;IAEA;;GAEC,GACD,MAAM,SAAS,CAAC,QAAQ,GAAG,SAAU,EAAE,EAAE,OAAO;QAC9C,IAAI,QAAQ,IAAI,CAAC,KAAK;QACtB,IAAI,MAAM,MAAM,MAAM;QACtB,IAAK,IAAI,IAAI,GAAG,IAAI,KAAK,IAAK;YAC5B,IAAI,KAAK,CAAC,EAAE,CAAC,SAAS,IAAI,KAAK,KAAK,CAAC,EAAE,CAAC,KAAK,CAAC,SAAS,IAAI,KAAK,KAAK,CAAC,EAAE,CAAC,KAAK,CAAC,SAAS,IAAI,GAAG;gBAC7F,GAAG,IAAI,CAAC,SAAS,KAAK,CAAC,EAAE,EAAE;YAC7B;QACF;IACF;;IAEA;;;GAGC,GACD,MAAM,SAAS,CAAC,oBAAoB,GAAG,SAAU,EAAE,EAAE,SAAS,EAAE,SAAS,EAAE,OAAO;QAChF,IAAI,CAAC,CAAC,qBAAqB,SAAS,GAAG;YACrC,YAAY,IAAI,CAAC,SAAS,CAAC,gBAAgB,WAAW;QACxD;QACA,IAAI,CAAC,WAAW;YACd;QACF;QACA,IAAI,WAAW,cAAc,QAAQ,aAAa,cAAc,OAAO,YAAY;QACnF,IAAK,IAAI,IAAI,GAAG,IAAI,IAAI,CAAC,KAAK,CAAC,MAAM,EAAE,IAAK;YAC1C,IAAI,CAAC,KAAK,CAAC,EAAE,CAAC,SAAS,GAAG;QAC5B;QACA,IAAI,GAAG,IAAI,CAAC,SAAS,WAAW,OAAO;YACrC;QACF;QACA,IAAI,QAAQ;YAAC;SAAU;QACvB,MAAO,MAAM,MAAM,CAAE;YACnB,IAAI,cAAc,MAAM,KAAK;YAC7B,IAAI,QAAQ,WAAW,CAAC,SAAS;YACjC,IAAK,IAAI,IAAI,GAAG,IAAI,MAAM,MAAM,EAAE,IAAK;gBACrC,IAAI,IAAI,KAAK,CAAC,EAAE;gBAChB,IAAI,YAAY,EAAE,KAAK,KAAK,cAAc,EAAE,KAAK,GAAG,EAAE,KAAK;gBAC3D,IAAI,CAAC,UAAU,SAAS,EAAE;oBACxB,IAAI,GAAG,IAAI,CAAC,SAAS,WAAW,cAAc;wBAC5C,kBAAkB;wBAClB;oBACF;oBACA,MAAM,IAAI,CAAC;oBACX,UAAU,SAAS,GAAG;gBACxB;YACF;QACF;IACF;;IAEA,OAAO;IACP,sBAAsB;IACtB,wCAAwC;IACxC,MAAM;IACN,KAAK;IACL,gBAAgB;IAChB,MAAM,SAAS,CAAC,MAAM,GAAG;QACvB,IAAI,OAAO,IAAI,CAAC,IAAI;QACpB,IAAI,WAAW,IAAI,CAAC,QAAQ;QAC5B,IAAI,QAAQ,IAAI,CAAC,KAAK;QACtB,IAAI,QAAQ,IAAI,CAAC,KAAK;QACtB,IAAK,IAAI,IAAI,GAAG,MAAM,MAAM,MAAM,EAAE,IAAI,KAAK,IAAK;YAChD,KAAK,CAAC,EAAE,CAAC,SAAS,GAAG,CAAC;QACxB;QACA,IAAK,IAAI,IAAI,GAAG,MAAM,KAAK,KAAK,IAAI,IAAI,KAAK,IAAK;YAChD,KAAK,CAAC,KAAK,WAAW,CAAC,GAAG,CAAC,SAAS,GAAG;QACzC;QACA,SAAS,UAAU,CAAC,SAAU,GAAG;YAC/B,IAAI,OAAO,KAAK,CAAC,SAAS,WAAW,CAAC,KAAK;YAC3C,OAAO,KAAK,KAAK,CAAC,SAAS,IAAI,KAAK,KAAK,KAAK,CAAC,SAAS,IAAI;QAC9D;QACA,cAAc;QACd,IAAK,IAAI,IAAI,GAAG,MAAM,MAAM,MAAM,EAAE,IAAI,KAAK,IAAK;YAChD,KAAK,CAAC,EAAE,CAAC,SAAS,GAAG,CAAC;QACxB;QACA,IAAK,IAAI,IAAI,GAAG,MAAM,SAAS,KAAK,IAAI,IAAI,KAAK,IAAK;YACpD,KAAK,CAAC,SAAS,WAAW,CAAC,GAAG,CAAC,SAAS,GAAG;QAC7C;IACF;;IAEA;;GAEC,GACD,MAAM,SAAS,CAAC,KAAK,GAAG;QACtB,IAAI,QAAQ,IAAI,MAAM,IAAI,CAAC,SAAS;QACpC,IAAI,QAAQ,IAAI,CAAC,KAAK;QACtB,IAAI,QAAQ,IAAI,CAAC,KAAK;QACtB,IAAK,IAAI,IAAI,GAAG,IAAI,MAAM,MAAM,EAAE,IAAK;YACrC,MAAM,OAAO,CAAC,KAAK,CAAC,EAAE,CAAC,EAAE,EAAE,KAAK,CAAC,EAAE,CAAC,SAAS;QAC/C;QACA,IAAK,IAAI,IAAI,GAAG,IAAI,MAAM,MAAM,EAAE,IAAK;YACrC,IAAI,IAAI,KAAK,CAAC,EAAE;YAChB,MAAM,OAAO,CAAC,EAAE,KAAK,CAAC,EAAE,EAAE,EAAE,KAAK,CAAC,EAAE,EAAE,EAAE,SAAS;QACnD;QACA,OAAO;IACT;;IAEA,OAAO;AACT;AACA,IAAI,YAAY,WAAW,GAAE;IAC3B,SAAS,UAAU,EAAE,EAAE,SAAS;QAC9B,IAAI,CAAC,OAAO,GAAG,EAAE;QACjB,IAAI,CAAC,QAAQ,GAAG,EAAE;QAClB,IAAI,CAAC,KAAK,GAAG,EAAE;QACf,IAAI,CAAC,SAAS,GAAG,CAAC;QAClB,IAAI,CAAC,EAAE,GAAG,MAAM,OAAO,KAAK;QAC5B,IAAI,CAAC,SAAS,GAAG,aAAa,OAAO,CAAC,IAAI;IAC5C;IACA;;GAEC,GACD,UAAU,SAAS,CAAC,MAAM,GAAG;QAC3B,OAAO,IAAI,CAAC,KAAK,CAAC,MAAM;IAC1B;IACA;;GAEC,GACD,UAAU,SAAS,CAAC,QAAQ,GAAG;QAC7B,OAAO,IAAI,CAAC,OAAO,CAAC,MAAM;IAC5B;IACA;;EAEA,GACA,UAAU,SAAS,CAAC,SAAS,GAAG;QAC9B,OAAO,IAAI,CAAC,QAAQ,CAAC,MAAM;IAC7B;IACA,UAAU,SAAS,CAAC,QAAQ,GAAG,SAAU,IAAI;QAC3C,IAAI,IAAI,CAAC,SAAS,GAAG,GAAG;YACtB;QACF;QACA,IAAI,QAAQ,IAAI,CAAC,SAAS;QAC1B,IAAI,YAAY,MAAM,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,SAAS;QACtD,OAAO,UAAU,QAAQ,CAAC;IAC5B;IACA,UAAU,SAAS,CAAC,sBAAsB,GAAG;QAC3C,IAAI,cAAc;YAChB,MAAM,EAAE;YACR,MAAM,EAAE;QACV;QACA,IAAK,IAAI,IAAI,GAAG,IAAI,IAAI,CAAC,KAAK,CAAC,MAAM,EAAE,IAAK;YAC1C,IAAI,eAAe,IAAI,CAAC,KAAK,CAAC,EAAE;YAChC,IAAI,aAAa,SAAS,GAAG,GAAG;gBAC9B;YACF;YACA,YAAY,IAAI,CAAC,IAAI,CAAC,aAAa,SAAS;YAC5C,YAAY,IAAI,CAAC,IAAI,CAAC,aAAa,KAAK,CAAC,SAAS,EAAE,aAAa,KAAK,CAAC,SAAS;QAClF;QACA,OAAO;IACT;IACA,UAAU,SAAS,CAAC,wBAAwB,GAAG;QAC7C,IAAI,oBAAoB,CAAA,GAAA,8IAAA,CAAA,gBAAoB,AAAD;QAC3C,IAAI,oBAAoB,CAAA,GAAA,8IAAA,CAAA,gBAAoB,AAAD;QAC3C,IAAK,IAAI,IAAI,GAAG,IAAI,IAAI,CAAC,KAAK,CAAC,MAAM,EAAE,IAAK;YAC1C,IAAI,eAAe,IAAI,CAAC,KAAK,CAAC,EAAE;YAChC,IAAI,aAAa,SAAS,GAAG,GAAG;gBAC9B;YACF;YACA,kBAAkB,GAAG,CAAC,aAAa,SAAS,EAAE;YAC9C,IAAI,mBAAmB;gBAAC,aAAa,KAAK;aAAC;YAC3C,IAAI,mBAAmB;gBAAC,aAAa,KAAK;aAAC;YAC3C,IAAI,oBAAoB;YACxB,MAAO,oBAAoB,iBAAiB,MAAM,CAAE;gBAClD,IAAI,aAAa,gBAAgB,CAAC,kBAAkB;gBACpD;gBACA,kBAAkB,GAAG,CAAC,WAAW,SAAS,EAAE;gBAC5C,IAAK,IAAI,IAAI,GAAG,IAAI,WAAW,OAAO,CAAC,MAAM,EAAE,IAAK;oBAClD,kBAAkB,GAAG,CAAC,WAAW,OAAO,CAAC,EAAE,CAAC,SAAS,EAAE;oBACvD,iBAAiB,IAAI,CAAC,WAAW,OAAO,CAAC,EAAE,CAAC,KAAK;gBACnD;YACF;YACA,oBAAoB;YACpB,MAAO,oBAAoB,iBAAiB,MAAM,CAAE;gBAClD,IAAI,aAAa,gBAAgB,CAAC,kBAAkB;gBACpD;gBACA,kBAAkB,GAAG,CAAC,WAAW,SAAS,EAAE;gBAC5C,IAAK,IAAI,IAAI,GAAG,IAAI,WAAW,QAAQ,CAAC,MAAM,EAAE,IAAK;oBACnD,kBAAkB,GAAG,CAAC,WAAW,QAAQ,CAAC,EAAE,CAAC,SAAS,EAAE;oBACxD,iBAAiB,IAAI,CAAC,WAAW,QAAQ,CAAC,EAAE,CAAC,KAAK;gBACpD;YACF;QACF;QACA,OAAO;YACL,MAAM,kBAAkB,IAAI;YAC5B,MAAM,kBAAkB,IAAI;QAC9B;IACF;IACA,OAAO;AACT;AACA,IAAI,YAAY,WAAW,GAAE;IAC3B,SAAS,UAAU,EAAE,EAAE,EAAE,EAAE,SAAS;QAClC,IAAI,CAAC,SAAS,GAAG,CAAC;QAClB,IAAI,CAAC,KAAK,GAAG;QACb,IAAI,CAAC,KAAK,GAAG;QACb,IAAI,CAAC,SAAS,GAAG,aAAa,OAAO,CAAC,IAAI;IAC5C;IACA,6DAA6D;IAC7D,UAAU,SAAS,CAAC,QAAQ,GAAG,SAAU,IAAI;QAC3C,IAAI,IAAI,CAAC,SAAS,GAAG,GAAG;YACtB;QACF;QACA,IAAI,QAAQ,IAAI,CAAC,SAAS;QAC1B,IAAI,YAAY,MAAM,QAAQ,CAAC,YAAY,CAAC,IAAI,CAAC,SAAS;QAC1D,OAAO,UAAU,QAAQ,CAAC;IAC5B;IACA,UAAU,SAAS,CAAC,sBAAsB,GAAG;QAC3C,OAAO;YACL,MAAM;gBAAC,IAAI,CAAC,SAAS;aAAC;YACtB,MAAM;gBAAC,IAAI,CAAC,KAAK,CAAC,SAAS;gBAAE,IAAI,CAAC,KAAK,CAAC,SAAS;aAAC;QACpD;IACF;IACA,UAAU,SAAS,CAAC,wBAAwB,GAAG;QAC7C,IAAI,oBAAoB,CAAA,GAAA,8IAAA,CAAA,gBAAoB,AAAD;QAC3C,IAAI,oBAAoB,CAAA,GAAA,8IAAA,CAAA,gBAAoB,AAAD;QAC3C,kBAAkB,GAAG,CAAC,IAAI,CAAC,SAAS,EAAE;QACtC,IAAI,cAAc;YAAC,IAAI,CAAC,KAAK;SAAC;QAC9B,IAAI,cAAc;YAAC,IAAI,CAAC,KAAK;SAAC;QAC9B,IAAI,oBAAoB;QACxB,MAAO,oBAAoB,YAAY,MAAM,CAAE;YAC7C,IAAI,aAAa,WAAW,CAAC,kBAAkB;YAC/C;YACA,kBAAkB,GAAG,CAAC,WAAW,SAAS,EAAE;YAC5C,IAAK,IAAI,IAAI,GAAG,IAAI,WAAW,OAAO,CAAC,MAAM,EAAE,IAAK;gBAClD,kBAAkB,GAAG,CAAC,WAAW,OAAO,CAAC,EAAE,CAAC,SAAS,EAAE;gBACvD,YAAY,IAAI,CAAC,WAAW,OAAO,CAAC,EAAE,CAAC,KAAK;YAC9C;QACF;QACA,oBAAoB;QACpB,MAAO,oBAAoB,YAAY,MAAM,CAAE;YAC7C,IAAI,aAAa,WAAW,CAAC,kBAAkB;YAC/C;YACA,kBAAkB,GAAG,CAAC,WAAW,SAAS,EAAE;YAC5C,IAAK,IAAI,IAAI,GAAG,IAAI,WAAW,QAAQ,CAAC,MAAM,EAAE,IAAK;gBACnD,kBAAkB,GAAG,CAAC,WAAW,QAAQ,CAAC,EAAE,CAAC,SAAS,EAAE;gBACxD,YAAY,IAAI,CAAC,WAAW,QAAQ,CAAC,EAAE,CAAC,KAAK;YAC/C;QACF;QACA,OAAO;YACL,MAAM,kBAAkB,IAAI;YAC5B,MAAM,kBAAkB,IAAI;QAC9B;IACF;IACA,OAAO;AACT;AACA,SAAS,0BAA0B,QAAQ,EAAE,QAAQ;IACnD,OAAO;QACL;;KAEC,GACD,UAAU,SAAU,SAAS;YAC3B,IAAI,OAAO,IAAI,CAAC,SAAS,CAAC,SAAS;YACnC,OAAO,KAAK,QAAQ,GAAG,GAAG,CAAC,KAAK,iBAAiB,CAAC,aAAa,UAAU,IAAI,CAAC,SAAS;QACzF;QACA,4BAA4B;QAC5B,WAAW,SAAU,GAAG,EAAE,KAAK;YAC7B,IAAI,CAAC,SAAS,IAAI,KAAK,IAAI,CAAC,SAAS,CAAC,SAAS,CAAC,aAAa,CAAC,IAAI,CAAC,SAAS,EAAE,KAAK;QACrF;QACA,WAAW,SAAU,GAAG;YACtB,OAAO,IAAI,CAAC,SAAS,CAAC,SAAS,CAAC,aAAa,CAAC,IAAI,CAAC,SAAS,EAAE;QAChE;QACA,WAAW,SAAU,MAAM,EAAE,KAAK;YAChC,IAAI,CAAC,SAAS,IAAI,KAAK,IAAI,CAAC,SAAS,CAAC,SAAS,CAAC,aAAa,CAAC,IAAI,CAAC,SAAS,EAAE,QAAQ;QACxF;QACA,WAAW;YACT,OAAO,IAAI,CAAC,SAAS,CAAC,SAAS,CAAC,aAAa,CAAC,IAAI,CAAC,SAAS;QAC9D;QACA,cAAc;YACZ,OAAO,IAAI,CAAC,SAAS,CAAC,SAAS,CAAC,gBAAgB,CAAC,IAAI,CAAC,SAAS;QACjE;QACA,aAAa;YACX,OAAO,IAAI,CAAC,SAAS,CAAC,SAAS,CAAC,WAAW,CAAC,IAAI,CAAC,SAAS;QAC5D;IACF;AACF;;;;AAIA,CAAA,GAAA,8IAAA,CAAA,QAAY,AAAD,EAAE,WAAW,0BAA0B,aAAa;AAC/D,CAAA,GAAA,8IAAA,CAAA,QAAY,AAAD,EAAE,WAAW,0BAA0B,aAAa;uCAChD", "ignoreList": [0], "debugId": null}}]}