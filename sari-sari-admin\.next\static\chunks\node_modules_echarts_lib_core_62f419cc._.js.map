{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/tindahan/sari-sari-admin/node_modules/echarts/lib/core/locale.js"], "sourcesContent": ["\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\r\n* Licensed to the Apache Software Foundation (ASF) under one\r\n* or more contributor license agreements.  See the NOTICE file\r\n* distributed with this work for additional information\r\n* regarding copyright ownership.  The ASF licenses this file\r\n* to you under the Apache License, Version 2.0 (the\r\n* \"License\"); you may not use this file except in compliance\r\n* with the License.  You may obtain a copy of the License at\r\n*\r\n*   http://www.apache.org/licenses/LICENSE-2.0\r\n*\r\n* Unless required by applicable law or agreed to in writing,\r\n* software distributed under the License is distributed on an\r\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\r\n* KIND, either express or implied.  See the License for the\r\n* specific language governing permissions and limitations\r\n* under the License.\r\n*/\nimport Model from '../model/Model.js';\nimport env from 'zrender/lib/core/env.js';\n// default import ZH and EN lang\nimport langEN from '../i18n/langEN.js';\nimport langZH from '../i18n/langZH.js';\nimport { isString, clone, merge } from 'zrender/lib/core/util.js';\nvar LOCALE_ZH = 'ZH';\nvar LOCALE_EN = 'EN';\nvar DEFAULT_LOCALE = LOCALE_EN;\nvar localeStorage = {};\nvar localeModels = {};\nexport var SYSTEM_LANG = !env.domSupported ? DEFAULT_LOCALE : function () {\n  var langStr = (/* eslint-disable-next-line */\n  document.documentElement.lang || navigator.language || navigator.browserLanguage || DEFAULT_LOCALE).toUpperCase();\n  return langStr.indexOf(LOCALE_ZH) > -1 ? LOCALE_ZH : DEFAULT_LOCALE;\n}();\nexport function registerLocale(locale, localeObj) {\n  locale = locale.toUpperCase();\n  localeModels[locale] = new Model(localeObj);\n  localeStorage[locale] = localeObj;\n}\n// export function getLocale(locale: string) {\n//     return localeStorage[locale];\n// }\nexport function createLocaleObject(locale) {\n  if (isString(locale)) {\n    var localeObj = localeStorage[locale.toUpperCase()] || {};\n    if (locale === LOCALE_ZH || locale === LOCALE_EN) {\n      return clone(localeObj);\n    } else {\n      return merge(clone(localeObj), clone(localeStorage[DEFAULT_LOCALE]), false);\n    }\n  } else {\n    return merge(clone(locale), clone(localeStorage[DEFAULT_LOCALE]), false);\n  }\n}\nexport function getLocaleModel(lang) {\n  return localeModels[lang];\n}\nexport function getDefaultLocaleModel() {\n  return localeModels[DEFAULT_LOCALE];\n}\n// Default locale\nregisterLocale(LOCALE_EN, langEN);\nregisterLocale(LOCALE_ZH, langZH);"], "names": [], "mappings": "AACA;;;;;;;;;;;;;;;;;AAiBA,GAGA;;CAEC,GAED;;;;;;;;;;;;;;;;;AAiBA;;;;;;;AACA;AACA;AACA,gCAAgC;AAChC;AACA;AACA;;;;;;AACA,IAAI,YAAY;AAChB,IAAI,YAAY;AAChB,IAAI,iBAAiB;AACrB,IAAI,gBAAgB,CAAC;AACrB,IAAI,eAAe,CAAC;AACb,IAAI,cAAc,CAAC,gJAAA,CAAA,UAAG,CAAC,YAAY,GAAG,iBAAiB;IAC5D,IAAI,UAAU,CAAC,4BAA4B,GAC3C,SAAS,eAAe,CAAC,IAAI,IAAI,UAAU,QAAQ,IAAI,UAAU,eAAe,IAAI,cAAc,EAAE,WAAW;IAC/G,OAAO,QAAQ,OAAO,CAAC,aAAa,CAAC,IAAI,YAAY;AACvD;AACO,SAAS,eAAe,MAAM,EAAE,SAAS;IAC9C,SAAS,OAAO,WAAW;IAC3B,YAAY,CAAC,OAAO,GAAG,IAAI,mJAAA,CAAA,UAAK,CAAC;IACjC,aAAa,CAAC,OAAO,GAAG;AAC1B;AAIO,SAAS,mBAAmB,MAAM;IACvC,IAAI,CAAA,GAAA,iJAAA,CAAA,WAAQ,AAAD,EAAE,SAAS;QACpB,IAAI,YAAY,aAAa,CAAC,OAAO,WAAW,GAAG,IAAI,CAAC;QACxD,IAAI,WAAW,aAAa,WAAW,WAAW;YAChD,OAAO,CAAA,GAAA,iJAAA,CAAA,QAAK,AAAD,EAAE;QACf,OAAO;YACL,OAAO,CAAA,GAAA,iJAAA,CAAA,QAAK,AAAD,EAAE,CAAA,GAAA,iJAAA,CAAA,QAAK,AAAD,EAAE,YAAY,CAAA,GAAA,iJAAA,CAAA,QAAK,AAAD,EAAE,aAAa,CAAC,eAAe,GAAG;QACvE;IACF,OAAO;QACL,OAAO,CAAA,GAAA,iJAAA,CAAA,QAAK,AAAD,EAAE,CAAA,GAAA,iJAAA,CAAA,QAAK,AAAD,EAAE,SAAS,CAAA,GAAA,iJAAA,CAAA,QAAK,AAAD,EAAE,aAAa,CAAC,eAAe,GAAG;IACpE;AACF;AACO,SAAS,eAAe,IAAI;IACjC,OAAO,YAAY,CAAC,KAAK;AAC3B;AACO,SAAS;IACd,OAAO,YAAY,CAAC,eAAe;AACrC;AACA,iBAAiB;AACjB,eAAe,WAAW,mJAAA,CAAA,UAAM;AAChC,eAAe,WAAW,mJAAA,CAAA,UAAM", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 102, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/tindahan/sari-sari-admin/node_modules/echarts/lib/core/ExtensionAPI.js"], "sourcesContent": ["\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\r\n* Licensed to the Apache Software Foundation (ASF) under one\r\n* or more contributor license agreements.  See the NOTICE file\r\n* distributed with this work for additional information\r\n* regarding copyright ownership.  The ASF licenses this file\r\n* to you under the Apache License, Version 2.0 (the\r\n* \"License\"); you may not use this file except in compliance\r\n* with the License.  You may obtain a copy of the License at\r\n*\r\n*   http://www.apache.org/licenses/LICENSE-2.0\r\n*\r\n* Unless required by applicable law or agreed to in writing,\r\n* software distributed under the License is distributed on an\r\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\r\n* KIND, either express or implied.  See the License for the\r\n* specific language governing permissions and limitations\r\n* under the License.\r\n*/\nimport * as zrUtil from 'zrender/lib/core/util.js';\nvar availableMethods = ['getDom', 'getZr', 'getWidth', 'getHeight', 'getDevicePixelRatio', 'dispatchAction', 'isSSR', 'isDisposed', 'on', 'off', 'getDataURL', 'getConnectedDataURL',\n// 'getModel',\n'getOption',\n// 'getViewOfComponentModel',\n// 'getViewOfSeriesModel',\n'getId', 'updateLabelLayout'];\nvar ExtensionAPI = /** @class */function () {\n  function ExtensionAPI(ecInstance) {\n    zrUtil.each(availableMethods, function (methodName) {\n      this[methodName] = zrUtil.bind(ecInstance[methodName], ecInstance);\n    }, this);\n  }\n  return ExtensionAPI;\n}();\nexport default ExtensionAPI;"], "names": [], "mappings": "AACA;;;;;;;;;;;;;;;;;AAiBA,GAGA;;CAEC,GAED;;;;;;;;;;;;;;;;;AAiBA;;;AACA;;AACA,IAAI,mBAAmB;IAAC;IAAU;IAAS;IAAY;IAAa;IAAuB;IAAkB;IAAS;IAAc;IAAM;IAAO;IAAc;IAC/J,cAAc;IACd;IACA,6BAA6B;IAC7B,0BAA0B;IAC1B;IAAS;CAAoB;AAC7B,IAAI,eAAe,WAAW,GAAE;IAC9B,SAAS,aAAa,UAAU;QAC9B,CAAA,GAAA,iJAAA,CAAA,OAAW,AAAD,EAAE,kBAAkB,SAAU,UAAU;YAChD,IAAI,CAAC,WAAW,GAAG,CAAA,GAAA,iJAAA,CAAA,OAAW,AAAD,EAAE,UAAU,CAAC,WAAW,EAAE;QACzD,GAAG,IAAI;IACT;IACA,OAAO;AACT;uCACe", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 178, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/tindahan/sari-sari-admin/node_modules/echarts/lib/core/CoordinateSystem.js"], "sourcesContent": ["\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\r\n* Licensed to the Apache Software Foundation (ASF) under one\r\n* or more contributor license agreements.  See the NOTICE file\r\n* distributed with this work for additional information\r\n* regarding copyright ownership.  The ASF licenses this file\r\n* to you under the Apache License, Version 2.0 (the\r\n* \"License\"); you may not use this file except in compliance\r\n* with the License.  You may obtain a copy of the License at\r\n*\r\n*   http://www.apache.org/licenses/LICENSE-2.0\r\n*\r\n* Unless required by applicable law or agreed to in writing,\r\n* software distributed under the License is distributed on an\r\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\r\n* KIND, either express or implied.  See the License for the\r\n* specific language governing permissions and limitations\r\n* under the License.\r\n*/\nimport * as zrUtil from 'zrender/lib/core/util.js';\nvar coordinateSystemCreators = {};\nvar CoordinateSystemManager = /** @class */function () {\n  function CoordinateSystemManager() {\n    this._coordinateSystems = [];\n  }\n  CoordinateSystemManager.prototype.create = function (ecModel, api) {\n    var coordinateSystems = [];\n    zrUtil.each(coordinateSystemCreators, function (creator, type) {\n      var list = creator.create(ecModel, api);\n      coordinateSystems = coordinateSystems.concat(list || []);\n    });\n    this._coordinateSystems = coordinateSystems;\n  };\n  CoordinateSystemManager.prototype.update = function (ecModel, api) {\n    zrUtil.each(this._coordinateSystems, function (coordSys) {\n      coordSys.update && coordSys.update(ecModel, api);\n    });\n  };\n  CoordinateSystemManager.prototype.getCoordinateSystems = function () {\n    return this._coordinateSystems.slice();\n  };\n  CoordinateSystemManager.register = function (type, creator) {\n    coordinateSystemCreators[type] = creator;\n  };\n  CoordinateSystemManager.get = function (type) {\n    return coordinateSystemCreators[type];\n  };\n  return CoordinateSystemManager;\n}();\nexport default CoordinateSystemManager;"], "names": [], "mappings": "AACA;;;;;;;;;;;;;;;;;AAiBA,GAGA;;CAEC,GAED;;;;;;;;;;;;;;;;;AAiBA;;;AACA;;AACA,IAAI,2BAA2B,CAAC;AAChC,IAAI,0BAA0B,WAAW,GAAE;IACzC,SAAS;QACP,IAAI,CAAC,kBAAkB,GAAG,EAAE;IAC9B;IACA,wBAAwB,SAAS,CAAC,MAAM,GAAG,SAAU,OAAO,EAAE,GAAG;QAC/D,IAAI,oBAAoB,EAAE;QAC1B,CAAA,GAAA,iJAAA,CAAA,OAAW,AAAD,EAAE,0BAA0B,SAAU,OAAO,EAAE,IAAI;YAC3D,IAAI,OAAO,QAAQ,MAAM,CAAC,SAAS;YACnC,oBAAoB,kBAAkB,MAAM,CAAC,QAAQ,EAAE;QACzD;QACA,IAAI,CAAC,kBAAkB,GAAG;IAC5B;IACA,wBAAwB,SAAS,CAAC,MAAM,GAAG,SAAU,OAAO,EAAE,GAAG;QAC/D,CAAA,GAAA,iJAAA,CAAA,OAAW,AAAD,EAAE,IAAI,CAAC,kBAAkB,EAAE,SAAU,QAAQ;YACrD,SAAS,MAAM,IAAI,SAAS,MAAM,CAAC,SAAS;QAC9C;IACF;IACA,wBAAwB,SAAS,CAAC,oBAAoB,GAAG;QACvD,OAAO,IAAI,CAAC,kBAAkB,CAAC,KAAK;IACtC;IACA,wBAAwB,QAAQ,GAAG,SAAU,IAAI,EAAE,OAAO;QACxD,wBAAwB,CAAC,KAAK,GAAG;IACnC;IACA,wBAAwB,GAAG,GAAG,SAAU,IAAI;QAC1C,OAAO,wBAAwB,CAAC,KAAK;IACvC;IACA,OAAO;AACT;uCACe", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 255, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/tindahan/sari-sari-admin/node_modules/echarts/lib/core/task.js"], "sourcesContent": ["\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\r\n* Licensed to the Apache Software Foundation (ASF) under one\r\n* or more contributor license agreements.  See the NOTICE file\r\n* distributed with this work for additional information\r\n* regarding copyright ownership.  The ASF licenses this file\r\n* to you under the Apache License, Version 2.0 (the\r\n* \"License\"); you may not use this file except in compliance\r\n* with the License.  You may obtain a copy of the License at\r\n*\r\n*   http://www.apache.org/licenses/LICENSE-2.0\r\n*\r\n* Unless required by applicable law or agreed to in writing,\r\n* software distributed under the License is distributed on an\r\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\r\n* KIND, either express or implied.  See the License for the\r\n* specific language governing permissions and limitations\r\n* under the License.\r\n*/\nimport { assert, isArray } from 'zrender/lib/core/util.js';\n;\n/**\r\n * @param {Object} define\r\n * @return See the return of `createTask`.\r\n */\nexport function createTask(define) {\n  return new Task(define);\n}\nvar Task = /** @class */function () {\n  function Task(define) {\n    define = define || {};\n    this._reset = define.reset;\n    this._plan = define.plan;\n    this._count = define.count;\n    this._onDirty = define.onDirty;\n    this._dirty = true;\n  }\n  /**\r\n   * @param step Specified step.\r\n   * @param skip Skip customer perform call.\r\n   * @param modBy Sampling window size.\r\n   * @param modDataCount Sampling count.\r\n   * @return whether unfinished.\r\n   */\n  Task.prototype.perform = function (performArgs) {\n    var upTask = this._upstream;\n    var skip = performArgs && performArgs.skip;\n    // TODO some refactor.\n    // Pull data. Must pull data each time, because context.data\n    // may be updated by Series.setData.\n    if (this._dirty && upTask) {\n      var context = this.context;\n      context.data = context.outputData = upTask.context.outputData;\n    }\n    if (this.__pipeline) {\n      this.__pipeline.currentTask = this;\n    }\n    var planResult;\n    if (this._plan && !skip) {\n      planResult = this._plan(this.context);\n    }\n    // Support sharding by mod, which changes the render sequence and makes the rendered graphic\n    // elements uniformed distributed when progress, especially when moving or zooming.\n    var lastModBy = normalizeModBy(this._modBy);\n    var lastModDataCount = this._modDataCount || 0;\n    var modBy = normalizeModBy(performArgs && performArgs.modBy);\n    var modDataCount = performArgs && performArgs.modDataCount || 0;\n    if (lastModBy !== modBy || lastModDataCount !== modDataCount) {\n      planResult = 'reset';\n    }\n    function normalizeModBy(val) {\n      !(val >= 1) && (val = 1); // jshint ignore:line\n      return val;\n    }\n    var forceFirstProgress;\n    if (this._dirty || planResult === 'reset') {\n      this._dirty = false;\n      forceFirstProgress = this._doReset(skip);\n    }\n    this._modBy = modBy;\n    this._modDataCount = modDataCount;\n    var step = performArgs && performArgs.step;\n    if (upTask) {\n      if (process.env.NODE_ENV !== 'production') {\n        assert(upTask._outputDueEnd != null);\n      }\n      this._dueEnd = upTask._outputDueEnd;\n    }\n    // DataTask or overallTask\n    else {\n      if (process.env.NODE_ENV !== 'production') {\n        assert(!this._progress || this._count);\n      }\n      this._dueEnd = this._count ? this._count(this.context) : Infinity;\n    }\n    // Note: Stubs, that its host overall task let it has progress, has progress.\n    // If no progress, pass index from upstream to downstream each time plan called.\n    if (this._progress) {\n      var start = this._dueIndex;\n      var end = Math.min(step != null ? this._dueIndex + step : Infinity, this._dueEnd);\n      if (!skip && (forceFirstProgress || start < end)) {\n        var progress = this._progress;\n        if (isArray(progress)) {\n          for (var i = 0; i < progress.length; i++) {\n            this._doProgress(progress[i], start, end, modBy, modDataCount);\n          }\n        } else {\n          this._doProgress(progress, start, end, modBy, modDataCount);\n        }\n      }\n      this._dueIndex = end;\n      // If no `outputDueEnd`, assume that output data and\n      // input data is the same, so use `dueIndex` as `outputDueEnd`.\n      var outputDueEnd = this._settedOutputEnd != null ? this._settedOutputEnd : end;\n      if (process.env.NODE_ENV !== 'production') {\n        // ??? Can not rollback.\n        assert(outputDueEnd >= this._outputDueEnd);\n      }\n      this._outputDueEnd = outputDueEnd;\n    } else {\n      // (1) Some overall task has no progress.\n      // (2) Stubs, that its host overall task do not let it has progress, has no progress.\n      // This should always be performed so it can be passed to downstream.\n      this._dueIndex = this._outputDueEnd = this._settedOutputEnd != null ? this._settedOutputEnd : this._dueEnd;\n    }\n    return this.unfinished();\n  };\n  Task.prototype.dirty = function () {\n    this._dirty = true;\n    this._onDirty && this._onDirty(this.context);\n  };\n  Task.prototype._doProgress = function (progress, start, end, modBy, modDataCount) {\n    iterator.reset(start, end, modBy, modDataCount);\n    this._callingProgress = progress;\n    this._callingProgress({\n      start: start,\n      end: end,\n      count: end - start,\n      next: iterator.next\n    }, this.context);\n  };\n  Task.prototype._doReset = function (skip) {\n    this._dueIndex = this._outputDueEnd = this._dueEnd = 0;\n    this._settedOutputEnd = null;\n    var progress;\n    var forceFirstProgress;\n    if (!skip && this._reset) {\n      progress = this._reset(this.context);\n      if (progress && progress.progress) {\n        forceFirstProgress = progress.forceFirstProgress;\n        progress = progress.progress;\n      }\n      // To simplify no progress checking, array must has item.\n      if (isArray(progress) && !progress.length) {\n        progress = null;\n      }\n    }\n    this._progress = progress;\n    this._modBy = this._modDataCount = null;\n    var downstream = this._downstream;\n    downstream && downstream.dirty();\n    return forceFirstProgress;\n  };\n  Task.prototype.unfinished = function () {\n    return this._progress && this._dueIndex < this._dueEnd;\n  };\n  /**\r\n   * @param downTask The downstream task.\r\n   * @return The downstream task.\r\n   */\n  Task.prototype.pipe = function (downTask) {\n    if (process.env.NODE_ENV !== 'production') {\n      assert(downTask && !downTask._disposed && downTask !== this);\n    }\n    // If already downstream, do not dirty downTask.\n    if (this._downstream !== downTask || this._dirty) {\n      this._downstream = downTask;\n      downTask._upstream = this;\n      downTask.dirty();\n    }\n  };\n  Task.prototype.dispose = function () {\n    if (this._disposed) {\n      return;\n    }\n    this._upstream && (this._upstream._downstream = null);\n    this._downstream && (this._downstream._upstream = null);\n    this._dirty = false;\n    this._disposed = true;\n  };\n  Task.prototype.getUpstream = function () {\n    return this._upstream;\n  };\n  Task.prototype.getDownstream = function () {\n    return this._downstream;\n  };\n  Task.prototype.setOutputEnd = function (end) {\n    // This only happens in dataTask, dataZoom, map, currently.\n    // where dataZoom do not set end each time, but only set\n    // when reset. So we should record the set end, in case\n    // that the stub of dataZoom perform again and earse the\n    // set end by upstream.\n    this._outputDueEnd = this._settedOutputEnd = end;\n  };\n  return Task;\n}();\nexport { Task };\nvar iterator = function () {\n  var end;\n  var current;\n  var modBy;\n  var modDataCount;\n  var winCount;\n  var it = {\n    reset: function (s, e, sStep, sCount) {\n      current = s;\n      end = e;\n      modBy = sStep;\n      modDataCount = sCount;\n      winCount = Math.ceil(modDataCount / modBy);\n      it.next = modBy > 1 && modDataCount > 0 ? modNext : sequentialNext;\n    }\n  };\n  return it;\n  function sequentialNext() {\n    return current < end ? current++ : null;\n  }\n  function modNext() {\n    var dataIndex = current % winCount * modBy + Math.ceil(current / winCount);\n    var result = current >= end ? null : dataIndex < modDataCount ? dataIndex\n    // If modDataCount is smaller than data.count() (consider `appendData` case),\n    // Use normal linear rendering mode.\n    : current;\n    current++;\n    return result;\n  }\n}();\n// -----------------------------------------------------------------------------\n// For stream debug (Should be commented out after used!)\n// @usage: printTask(this, 'begin');\n// @usage: printTask(this, null, {someExtraProp});\n// @usage: Use `__idxInPipeline` as conditional breakpiont.\n//\n// window.printTask = function (task: any, prefix: string, extra: { [key: string]: unknown }): void {\n//     window.ecTaskUID == null && (window.ecTaskUID = 0);\n//     task.uidDebug == null && (task.uidDebug = `task_${window.ecTaskUID++}`);\n//     task.agent && task.agent.uidDebug == null && (task.agent.uidDebug = `task_${window.ecTaskUID++}`);\n//     let props = [];\n//     if (task.__pipeline) {\n//         let val = `${task.__idxInPipeline}/${task.__pipeline.tail.__idxInPipeline} ${task.agent ? '(stub)' : ''}`;\n//         props.push({text: '__idxInPipeline/total', value: val});\n//     } else {\n//         let stubCount = 0;\n//         task.agentStubMap.each(() => stubCount++);\n//         props.push({text: 'idx', value: `overall (stubs: ${stubCount})`});\n//     }\n//     props.push({text: 'uid', value: task.uidDebug});\n//     if (task.__pipeline) {\n//         props.push({text: 'pipelineId', value: task.__pipeline.id});\n//         task.agent && props.push(\n//             {text: 'stubFor', value: task.agent.uidDebug}\n//         );\n//     }\n//     props.push(\n//         {text: 'dirty', value: task._dirty},\n//         {text: 'dueIndex', value: task._dueIndex},\n//         {text: 'dueEnd', value: task._dueEnd},\n//         {text: 'outputDueEnd', value: task._outputDueEnd}\n//     );\n//     if (extra) {\n//         Object.keys(extra).forEach(key => {\n//             props.push({text: key, value: extra[key]});\n//         });\n//     }\n//     let args = ['color: blue'];\n//     let msg = `%c[${prefix || 'T'}] %c` + props.map(item => (\n//         args.push('color: green', 'color: red'),\n//         `${item.text}: %c${item.value}`\n//     )).join('%c, ');\n//     console.log.apply(console, [msg].concat(args));\n//     // console.log(this);\n// };\n// window.printPipeline = function (task: any, prefix: string) {\n//     const pipeline = task.__pipeline;\n//     let currTask = pipeline.head;\n//     while (currTask) {\n//         window.printTask(currTask, prefix);\n//         currTask = currTask._downstream;\n//     }\n// };\n// window.showChain = function (chainHeadTask) {\n//     var chain = [];\n//     var task = chainHeadTask;\n//     while (task) {\n//         chain.push({\n//             task: task,\n//             up: task._upstream,\n//             down: task._downstream,\n//             idxInPipeline: task.__idxInPipeline\n//         });\n//         task = task._downstream;\n//     }\n//     return chain;\n// };\n// window.findTaskInChain = function (task, chainHeadTask) {\n//     let chain = window.showChain(chainHeadTask);\n//     let result = [];\n//     for (let i = 0; i < chain.length; i++) {\n//         let chainItem = chain[i];\n//         if (chainItem.task === task) {\n//             result.push(i);\n//         }\n//     }\n//     return result;\n// };\n// window.printChainAEachInChainB = function (chainHeadTaskA, chainHeadTaskB) {\n//     let chainA = window.showChain(chainHeadTaskA);\n//     for (let i = 0; i < chainA.length; i++) {\n//         console.log('chainAIdx:', i, 'inChainB:', window.findTaskInChain(chainA[i].task, chainHeadTaskB));\n//     }\n// };"], "names": [], "mappings": "AACA;;;;;;;;;;;;;;;;;AAiBA,GAGA;;CAEC,GAED;;;;;;;;;;;;;;;;;AAiBA;;;;AAwEU;AAvEV;;;AAMO,SAAS,WAAW,MAAM;IAC/B,OAAO,IAAI,KAAK;AAClB;AACA,IAAI,OAAO,WAAW,GAAE;IACtB,SAAS,KAAK,MAAM;QAClB,SAAS,UAAU,CAAC;QACpB,IAAI,CAAC,MAAM,GAAG,OAAO,KAAK;QAC1B,IAAI,CAAC,KAAK,GAAG,OAAO,IAAI;QACxB,IAAI,CAAC,MAAM,GAAG,OAAO,KAAK;QAC1B,IAAI,CAAC,QAAQ,GAAG,OAAO,OAAO;QAC9B,IAAI,CAAC,MAAM,GAAG;IAChB;IACA;;;;;;GAMC,GACD,KAAK,SAAS,CAAC,OAAO,GAAG,SAAU,WAAW;QAC5C,IAAI,SAAS,IAAI,CAAC,SAAS;QAC3B,IAAI,OAAO,eAAe,YAAY,IAAI;QAC1C,sBAAsB;QACtB,4DAA4D;QAC5D,oCAAoC;QACpC,IAAI,IAAI,CAAC,MAAM,IAAI,QAAQ;YACzB,IAAI,UAAU,IAAI,CAAC,OAAO;YAC1B,QAAQ,IAAI,GAAG,QAAQ,UAAU,GAAG,OAAO,OAAO,CAAC,UAAU;QAC/D;QACA,IAAI,IAAI,CAAC,UAAU,EAAE;YACnB,IAAI,CAAC,UAAU,CAAC,WAAW,GAAG,IAAI;QACpC;QACA,IAAI;QACJ,IAAI,IAAI,CAAC,KAAK,IAAI,CAAC,MAAM;YACvB,aAAa,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,OAAO;QACtC;QACA,4FAA4F;QAC5F,mFAAmF;QACnF,IAAI,YAAY,eAAe,IAAI,CAAC,MAAM;QAC1C,IAAI,mBAAmB,IAAI,CAAC,aAAa,IAAI;QAC7C,IAAI,QAAQ,eAAe,eAAe,YAAY,KAAK;QAC3D,IAAI,eAAe,eAAe,YAAY,YAAY,IAAI;QAC9D,IAAI,cAAc,SAAS,qBAAqB,cAAc;YAC5D,aAAa;QACf;QACA,SAAS,eAAe,GAAG;YACzB,CAAC,CAAC,OAAO,CAAC,KAAK,CAAC,MAAM,CAAC,GAAG,qBAAqB;YAC/C,OAAO;QACT;QACA,IAAI;QACJ,IAAI,IAAI,CAAC,MAAM,IAAI,eAAe,SAAS;YACzC,IAAI,CAAC,MAAM,GAAG;YACd,qBAAqB,IAAI,CAAC,QAAQ,CAAC;QACrC;QACA,IAAI,CAAC,MAAM,GAAG;QACd,IAAI,CAAC,aAAa,GAAG;QACrB,IAAI,OAAO,eAAe,YAAY,IAAI;QAC1C,IAAI,QAAQ;YACV,wCAA2C;gBACzC,CAAA,GAAA,iJAAA,CAAA,SAAM,AAAD,EAAE,OAAO,aAAa,IAAI;YACjC;YACA,IAAI,CAAC,OAAO,GAAG,OAAO,aAAa;QACrC,OAEK;YACH,wCAA2C;gBACzC,CAAA,GAAA,iJAAA,CAAA,SAAM,AAAD,EAAE,CAAC,IAAI,CAAC,SAAS,IAAI,IAAI,CAAC,MAAM;YACvC;YACA,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,OAAO,IAAI;QAC3D;QACA,6EAA6E;QAC7E,gFAAgF;QAChF,IAAI,IAAI,CAAC,SAAS,EAAE;YAClB,IAAI,QAAQ,IAAI,CAAC,SAAS;YAC1B,IAAI,MAAM,KAAK,GAAG,CAAC,QAAQ,OAAO,IAAI,CAAC,SAAS,GAAG,OAAO,UAAU,IAAI,CAAC,OAAO;YAChF,IAAI,CAAC,QAAQ,CAAC,sBAAsB,QAAQ,GAAG,GAAG;gBAChD,IAAI,WAAW,IAAI,CAAC,SAAS;gBAC7B,IAAI,CAAA,GAAA,iJAAA,CAAA,UAAO,AAAD,EAAE,WAAW;oBACrB,IAAK,IAAI,IAAI,GAAG,IAAI,SAAS,MAAM,EAAE,IAAK;wBACxC,IAAI,CAAC,WAAW,CAAC,QAAQ,CAAC,EAAE,EAAE,OAAO,KAAK,OAAO;oBACnD;gBACF,OAAO;oBACL,IAAI,CAAC,WAAW,CAAC,UAAU,OAAO,KAAK,OAAO;gBAChD;YACF;YACA,IAAI,CAAC,SAAS,GAAG;YACjB,oDAAoD;YACpD,+DAA+D;YAC/D,IAAI,eAAe,IAAI,CAAC,gBAAgB,IAAI,OAAO,IAAI,CAAC,gBAAgB,GAAG;YAC3E,wCAA2C;gBACzC,wBAAwB;gBACxB,CAAA,GAAA,iJAAA,CAAA,SAAM,AAAD,EAAE,gBAAgB,IAAI,CAAC,aAAa;YAC3C;YACA,IAAI,CAAC,aAAa,GAAG;QACvB,OAAO;YACL,yCAAyC;YACzC,qFAAqF;YACrF,qEAAqE;YACrE,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC,aAAa,GAAG,IAAI,CAAC,gBAAgB,IAAI,OAAO,IAAI,CAAC,gBAAgB,GAAG,IAAI,CAAC,OAAO;QAC5G;QACA,OAAO,IAAI,CAAC,UAAU;IACxB;IACA,KAAK,SAAS,CAAC,KAAK,GAAG;QACrB,IAAI,CAAC,MAAM,GAAG;QACd,IAAI,CAAC,QAAQ,IAAI,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,OAAO;IAC7C;IACA,KAAK,SAAS,CAAC,WAAW,GAAG,SAAU,QAAQ,EAAE,KAAK,EAAE,GAAG,EAAE,KAAK,EAAE,YAAY;QAC9E,SAAS,KAAK,CAAC,OAAO,KAAK,OAAO;QAClC,IAAI,CAAC,gBAAgB,GAAG;QACxB,IAAI,CAAC,gBAAgB,CAAC;YACpB,OAAO;YACP,KAAK;YACL,OAAO,MAAM;YACb,MAAM,SAAS,IAAI;QACrB,GAAG,IAAI,CAAC,OAAO;IACjB;IACA,KAAK,SAAS,CAAC,QAAQ,GAAG,SAAU,IAAI;QACtC,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC,aAAa,GAAG,IAAI,CAAC,OAAO,GAAG;QACrD,IAAI,CAAC,gBAAgB,GAAG;QACxB,IAAI;QACJ,IAAI;QACJ,IAAI,CAAC,QAAQ,IAAI,CAAC,MAAM,EAAE;YACxB,WAAW,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,OAAO;YACnC,IAAI,YAAY,SAAS,QAAQ,EAAE;gBACjC,qBAAqB,SAAS,kBAAkB;gBAChD,WAAW,SAAS,QAAQ;YAC9B;YACA,yDAAyD;YACzD,IAAI,CAAA,GAAA,iJAAA,CAAA,UAAO,AAAD,EAAE,aAAa,CAAC,SAAS,MAAM,EAAE;gBACzC,WAAW;YACb;QACF;QACA,IAAI,CAAC,SAAS,GAAG;QACjB,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC,aAAa,GAAG;QACnC,IAAI,aAAa,IAAI,CAAC,WAAW;QACjC,cAAc,WAAW,KAAK;QAC9B,OAAO;IACT;IACA,KAAK,SAAS,CAAC,UAAU,GAAG;QAC1B,OAAO,IAAI,CAAC,SAAS,IAAI,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC,OAAO;IACxD;IACA;;;GAGC,GACD,KAAK,SAAS,CAAC,IAAI,GAAG,SAAU,QAAQ;QACtC,wCAA2C;YACzC,CAAA,GAAA,iJAAA,CAAA,SAAM,AAAD,EAAE,YAAY,CAAC,SAAS,SAAS,IAAI,aAAa,IAAI;QAC7D;QACA,gDAAgD;QAChD,IAAI,IAAI,CAAC,WAAW,KAAK,YAAY,IAAI,CAAC,MAAM,EAAE;YAChD,IAAI,CAAC,WAAW,GAAG;YACnB,SAAS,SAAS,GAAG,IAAI;YACzB,SAAS,KAAK;QAChB;IACF;IACA,KAAK,SAAS,CAAC,OAAO,GAAG;QACvB,IAAI,IAAI,CAAC,SAAS,EAAE;YAClB;QACF;QACA,IAAI,CAAC,SAAS,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC,WAAW,GAAG,IAAI;QACpD,IAAI,CAAC,WAAW,IAAI,CAAC,IAAI,CAAC,WAAW,CAAC,SAAS,GAAG,IAAI;QACtD,IAAI,CAAC,MAAM,GAAG;QACd,IAAI,CAAC,SAAS,GAAG;IACnB;IACA,KAAK,SAAS,CAAC,WAAW,GAAG;QAC3B,OAAO,IAAI,CAAC,SAAS;IACvB;IACA,KAAK,SAAS,CAAC,aAAa,GAAG;QAC7B,OAAO,IAAI,CAAC,WAAW;IACzB;IACA,KAAK,SAAS,CAAC,YAAY,GAAG,SAAU,GAAG;QACzC,2DAA2D;QAC3D,wDAAwD;QACxD,uDAAuD;QACvD,wDAAwD;QACxD,uBAAuB;QACvB,IAAI,CAAC,aAAa,GAAG,IAAI,CAAC,gBAAgB,GAAG;IAC/C;IACA,OAAO;AACT;;AAEA,IAAI,WAAW;IACb,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,IAAI,KAAK;QACP,OAAO,SAAU,CAAC,EAAE,CAAC,EAAE,KAAK,EAAE,MAAM;YAClC,UAAU;YACV,MAAM;YACN,QAAQ;YACR,eAAe;YACf,WAAW,KAAK,IAAI,CAAC,eAAe;YACpC,GAAG,IAAI,GAAG,QAAQ,KAAK,eAAe,IAAI,UAAU;QACtD;IACF;IACA,OAAO;;IACP,SAAS;QACP,OAAO,UAAU,MAAM,YAAY;IACrC;IACA,SAAS;QACP,IAAI,YAAY,UAAU,WAAW,QAAQ,KAAK,IAAI,CAAC,UAAU;QACjE,IAAI,SAAS,WAAW,MAAM,OAAO,YAAY,eAAe,YAG9D;QACF;QACA,OAAO;IACT;AACF,KACA,gFAAgF;CAChF,yDAAyD;CACzD,oCAAoC;CACpC,kDAAkD;CAClD,2DAA2D;CAC3D,EAAE;CACF,qGAAqG;CACrG,0DAA0D;CAC1D,+EAA+E;CAC/E,yGAAyG;CACzG,sBAAsB;CACtB,6BAA6B;CAC7B,qHAAqH;CACrH,mEAAmE;CACnE,eAAe;CACf,6BAA6B;CAC7B,qDAAqD;CACrD,6EAA6E;CAC7E,QAAQ;CACR,uDAAuD;CACvD,6BAA6B;CAC7B,uEAAuE;CACvE,oCAAoC;CACpC,4DAA4D;CAC5D,aAAa;CACb,QAAQ;CACR,kBAAkB;CAClB,+CAA+C;CAC/C,qDAAqD;CACrD,iDAAiD;CACjD,4DAA4D;CAC5D,SAAS;CACT,mBAAmB;CACnB,8CAA8C;CAC9C,0DAA0D;CAC1D,cAAc;CACd,QAAQ;CACR,kCAAkC;CAClC,gEAAgE;CAChE,mDAAmD;CACnD,0CAA0C;CAC1C,uBAAuB;CACvB,sDAAsD;CACtD,4BAA4B;CAC5B,KAAK;CACL,gEAAgE;CAChE,wCAAwC;CACxC,oCAAoC;CACpC,yBAAyB;CACzB,8CAA8C;CAC9C,2CAA2C;CAC3C,QAAQ;CACR,KAAK;CACL,gDAAgD;CAChD,sBAAsB;CACtB,gCAAgC;CAChC,qBAAqB;CACrB,uBAAuB;CACvB,0BAA0B;CAC1B,kCAAkC;CAClC,sCAAsC;CACtC,kDAAkD;CAClD,cAAc;CACd,mCAAmC;CACnC,QAAQ;CACR,oBAAoB;CACpB,KAAK;CACL,4DAA4D;CAC5D,mDAAmD;CACnD,uBAAuB;CACvB,+CAA+C;CAC/C,oCAAoC;CACpC,yCAAyC;CACzC,8BAA8B;CAC9B,YAAY;CACZ,QAAQ;CACR,qBAAqB;CACrB,KAAK;CACL,+EAA+E;CAC/E,qDAAqD;CACrD,gDAAgD;CAChD,6GAA6G;CAC7G,QAAQ;CACR,KAAK", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 594, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/tindahan/sari-sari-admin/node_modules/echarts/lib/core/Scheduler.js"], "sourcesContent": ["\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\r\n* Licensed to the Apache Software Foundation (ASF) under one\r\n* or more contributor license agreements.  See the NOTICE file\r\n* distributed with this work for additional information\r\n* regarding copyright ownership.  The ASF licenses this file\r\n* to you under the Apache License, Version 2.0 (the\r\n* \"License\"); you may not use this file except in compliance\r\n* with the License.  You may obtain a copy of the License at\r\n*\r\n*   http://www.apache.org/licenses/LICENSE-2.0\r\n*\r\n* Unless required by applicable law or agreed to in writing,\r\n* software distributed under the License is distributed on an\r\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\r\n* KIND, either express or implied.  See the License for the\r\n* specific language governing permissions and limitations\r\n* under the License.\r\n*/\nimport { each, map, isFunction, createHashMap, noop, assert } from 'zrender/lib/core/util.js';\nimport { createTask } from './task.js';\nimport { getUID } from '../util/component.js';\nimport GlobalModel from '../model/Global.js';\nimport ExtensionAPI from './ExtensionAPI.js';\nimport { normalizeToArray } from '../util/model.js';\n;\nvar Scheduler = /** @class */function () {\n  function Scheduler(ecInstance, api, dataProcessorHandlers, visualHandlers) {\n    // key: handlerUID\n    this._stageTaskMap = createHashMap();\n    this.ecInstance = ecInstance;\n    this.api = api;\n    // Fix current processors in case that in some rear cases that\n    // processors might be registered after echarts instance created.\n    // Register processors incrementally for a echarts instance is\n    // not supported by this stream architecture.\n    dataProcessorHandlers = this._dataProcessorHandlers = dataProcessorHandlers.slice();\n    visualHandlers = this._visualHandlers = visualHandlers.slice();\n    this._allHandlers = dataProcessorHandlers.concat(visualHandlers);\n  }\n  Scheduler.prototype.restoreData = function (ecModel, payload) {\n    // TODO: Only restore needed series and components, but not all components.\n    // Currently `restoreData` of all of the series and component will be called.\n    // But some independent components like `title`, `legend`, `graphic`, `toolbox`,\n    // `tooltip`, `axisPointer`, etc, do not need series refresh when `setOption`,\n    // and some components like coordinate system, axes, dataZoom, visualMap only\n    // need their target series refresh.\n    // (1) If we are implementing this feature some day, we should consider these cases:\n    // if a data processor depends on a component (e.g., dataZoomProcessor depends\n    // on the settings of `dataZoom`), it should be re-performed if the component\n    // is modified by `setOption`.\n    // (2) If a processor depends on sevral series, speicified by its `getTargetSeries`,\n    // it should be re-performed when the result array of `getTargetSeries` changed.\n    // We use `dependencies` to cover these issues.\n    // (3) How to update target series when coordinate system related components modified.\n    // TODO: simply the dirty mechanism? Check whether only the case here can set tasks dirty,\n    // and this case all of the tasks will be set as dirty.\n    ecModel.restoreData(payload);\n    // Theoretically an overall task not only depends on each of its target series, but also\n    // depends on all of the series.\n    // The overall task is not in pipeline, and `ecModel.restoreData` only set pipeline tasks\n    // dirty. If `getTargetSeries` of an overall task returns nothing, we should also ensure\n    // that the overall task is set as dirty and to be performed, otherwise it probably cause\n    // state chaos. So we have to set dirty of all of the overall tasks manually, otherwise it\n    // probably cause state chaos (consider `dataZoomProcessor`).\n    this._stageTaskMap.each(function (taskRecord) {\n      var overallTask = taskRecord.overallTask;\n      overallTask && overallTask.dirty();\n    });\n  };\n  // If seriesModel provided, incremental threshold is check by series data.\n  Scheduler.prototype.getPerformArgs = function (task, isBlock) {\n    // For overall task\n    if (!task.__pipeline) {\n      return;\n    }\n    var pipeline = this._pipelineMap.get(task.__pipeline.id);\n    var pCtx = pipeline.context;\n    var incremental = !isBlock && pipeline.progressiveEnabled && (!pCtx || pCtx.progressiveRender) && task.__idxInPipeline > pipeline.blockIndex;\n    var step = incremental ? pipeline.step : null;\n    var modDataCount = pCtx && pCtx.modDataCount;\n    var modBy = modDataCount != null ? Math.ceil(modDataCount / step) : null;\n    return {\n      step: step,\n      modBy: modBy,\n      modDataCount: modDataCount\n    };\n  };\n  Scheduler.prototype.getPipeline = function (pipelineId) {\n    return this._pipelineMap.get(pipelineId);\n  };\n  /**\r\n   * Current, progressive rendering starts from visual and layout.\r\n   * Always detect render mode in the same stage, avoiding that incorrect\r\n   * detection caused by data filtering.\r\n   * Caution:\r\n   * `updateStreamModes` use `seriesModel.getData()`.\r\n   */\n  Scheduler.prototype.updateStreamModes = function (seriesModel, view) {\n    var pipeline = this._pipelineMap.get(seriesModel.uid);\n    var data = seriesModel.getData();\n    var dataLen = data.count();\n    // `progressiveRender` means that can render progressively in each\n    // animation frame. Note that some types of series do not provide\n    // `view.incrementalPrepareRender` but support `chart.appendData`. We\n    // use the term `incremental` but not `progressive` to describe the\n    // case that `chart.appendData`.\n    var progressiveRender = pipeline.progressiveEnabled && view.incrementalPrepareRender && dataLen >= pipeline.threshold;\n    var large = seriesModel.get('large') && dataLen >= seriesModel.get('largeThreshold');\n    // TODO: modDataCount should not updated if `appendData`, otherwise cause whole repaint.\n    // see `test/candlestick-large3.html`\n    var modDataCount = seriesModel.get('progressiveChunkMode') === 'mod' ? dataLen : null;\n    seriesModel.pipelineContext = pipeline.context = {\n      progressiveRender: progressiveRender,\n      modDataCount: modDataCount,\n      large: large\n    };\n  };\n  Scheduler.prototype.restorePipelines = function (ecModel) {\n    var scheduler = this;\n    var pipelineMap = scheduler._pipelineMap = createHashMap();\n    ecModel.eachSeries(function (seriesModel) {\n      var progressive = seriesModel.getProgressive();\n      var pipelineId = seriesModel.uid;\n      pipelineMap.set(pipelineId, {\n        id: pipelineId,\n        head: null,\n        tail: null,\n        threshold: seriesModel.getProgressiveThreshold(),\n        progressiveEnabled: progressive && !(seriesModel.preventIncremental && seriesModel.preventIncremental()),\n        blockIndex: -1,\n        step: Math.round(progressive || 700),\n        count: 0\n      });\n      scheduler._pipe(seriesModel, seriesModel.dataTask);\n    });\n  };\n  Scheduler.prototype.prepareStageTasks = function () {\n    var stageTaskMap = this._stageTaskMap;\n    var ecModel = this.api.getModel();\n    var api = this.api;\n    each(this._allHandlers, function (handler) {\n      var record = stageTaskMap.get(handler.uid) || stageTaskMap.set(handler.uid, {});\n      var errMsg = '';\n      if (process.env.NODE_ENV !== 'production') {\n        // Currently do not need to support to sepecify them both.\n        errMsg = '\"reset\" and \"overallReset\" must not be both specified.';\n      }\n      assert(!(handler.reset && handler.overallReset), errMsg);\n      handler.reset && this._createSeriesStageTask(handler, record, ecModel, api);\n      handler.overallReset && this._createOverallStageTask(handler, record, ecModel, api);\n    }, this);\n  };\n  Scheduler.prototype.prepareView = function (view, model, ecModel, api) {\n    var renderTask = view.renderTask;\n    var context = renderTask.context;\n    context.model = model;\n    context.ecModel = ecModel;\n    context.api = api;\n    renderTask.__block = !view.incrementalPrepareRender;\n    this._pipe(model, renderTask);\n  };\n  Scheduler.prototype.performDataProcessorTasks = function (ecModel, payload) {\n    // If we do not use `block` here, it should be considered when to update modes.\n    this._performStageTasks(this._dataProcessorHandlers, ecModel, payload, {\n      block: true\n    });\n  };\n  Scheduler.prototype.performVisualTasks = function (ecModel, payload, opt) {\n    this._performStageTasks(this._visualHandlers, ecModel, payload, opt);\n  };\n  Scheduler.prototype._performStageTasks = function (stageHandlers, ecModel, payload, opt) {\n    opt = opt || {};\n    var unfinished = false;\n    var scheduler = this;\n    each(stageHandlers, function (stageHandler, idx) {\n      if (opt.visualType && opt.visualType !== stageHandler.visualType) {\n        return;\n      }\n      var stageHandlerRecord = scheduler._stageTaskMap.get(stageHandler.uid);\n      var seriesTaskMap = stageHandlerRecord.seriesTaskMap;\n      var overallTask = stageHandlerRecord.overallTask;\n      if (overallTask) {\n        var overallNeedDirty_1;\n        var agentStubMap = overallTask.agentStubMap;\n        agentStubMap.each(function (stub) {\n          if (needSetDirty(opt, stub)) {\n            stub.dirty();\n            overallNeedDirty_1 = true;\n          }\n        });\n        overallNeedDirty_1 && overallTask.dirty();\n        scheduler.updatePayload(overallTask, payload);\n        var performArgs_1 = scheduler.getPerformArgs(overallTask, opt.block);\n        // Execute stubs firstly, which may set the overall task dirty,\n        // then execute the overall task. And stub will call seriesModel.setData,\n        // which ensures that in the overallTask seriesModel.getData() will not\n        // return incorrect data.\n        agentStubMap.each(function (stub) {\n          stub.perform(performArgs_1);\n        });\n        if (overallTask.perform(performArgs_1)) {\n          unfinished = true;\n        }\n      } else if (seriesTaskMap) {\n        seriesTaskMap.each(function (task, pipelineId) {\n          if (needSetDirty(opt, task)) {\n            task.dirty();\n          }\n          var performArgs = scheduler.getPerformArgs(task, opt.block);\n          // FIXME\n          // if intending to declare `performRawSeries` in handlers, only\n          // stream-independent (specifically, data item independent) operations can be\n          // performed. Because if a series is filtered, most of the tasks will not\n          // be performed. A stream-dependent operation probably cause wrong biz logic.\n          // Perhaps we should not provide a separate callback for this case instead\n          // of providing the config `performRawSeries`. The stream-dependent operations\n          // and stream-independent operations should better not be mixed.\n          performArgs.skip = !stageHandler.performRawSeries && ecModel.isSeriesFiltered(task.context.model);\n          scheduler.updatePayload(task, payload);\n          if (task.perform(performArgs)) {\n            unfinished = true;\n          }\n        });\n      }\n    });\n    function needSetDirty(opt, task) {\n      return opt.setDirty && (!opt.dirtyMap || opt.dirtyMap.get(task.__pipeline.id));\n    }\n    this.unfinished = unfinished || this.unfinished;\n  };\n  Scheduler.prototype.performSeriesTasks = function (ecModel) {\n    var unfinished;\n    ecModel.eachSeries(function (seriesModel) {\n      // Progress to the end for dataInit and dataRestore.\n      unfinished = seriesModel.dataTask.perform() || unfinished;\n    });\n    this.unfinished = unfinished || this.unfinished;\n  };\n  Scheduler.prototype.plan = function () {\n    // Travel pipelines, check block.\n    this._pipelineMap.each(function (pipeline) {\n      var task = pipeline.tail;\n      do {\n        if (task.__block) {\n          pipeline.blockIndex = task.__idxInPipeline;\n          break;\n        }\n        task = task.getUpstream();\n      } while (task);\n    });\n  };\n  Scheduler.prototype.updatePayload = function (task, payload) {\n    payload !== 'remain' && (task.context.payload = payload);\n  };\n  Scheduler.prototype._createSeriesStageTask = function (stageHandler, stageHandlerRecord, ecModel, api) {\n    var scheduler = this;\n    var oldSeriesTaskMap = stageHandlerRecord.seriesTaskMap;\n    // The count of stages are totally about only several dozen, so\n    // do not need to reuse the map.\n    var newSeriesTaskMap = stageHandlerRecord.seriesTaskMap = createHashMap();\n    var seriesType = stageHandler.seriesType;\n    var getTargetSeries = stageHandler.getTargetSeries;\n    // If a stageHandler should cover all series, `createOnAllSeries` should be declared mandatorily,\n    // to avoid some typo or abuse. Otherwise if an extension do not specify a `seriesType`,\n    // it works but it may cause other irrelevant charts blocked.\n    if (stageHandler.createOnAllSeries) {\n      ecModel.eachRawSeries(create);\n    } else if (seriesType) {\n      ecModel.eachRawSeriesByType(seriesType, create);\n    } else if (getTargetSeries) {\n      getTargetSeries(ecModel, api).each(create);\n    }\n    function create(seriesModel) {\n      var pipelineId = seriesModel.uid;\n      // Init tasks for each seriesModel only once.\n      // Reuse original task instance.\n      var task = newSeriesTaskMap.set(pipelineId, oldSeriesTaskMap && oldSeriesTaskMap.get(pipelineId) || createTask({\n        plan: seriesTaskPlan,\n        reset: seriesTaskReset,\n        count: seriesTaskCount\n      }));\n      task.context = {\n        model: seriesModel,\n        ecModel: ecModel,\n        api: api,\n        // PENDING: `useClearVisual` not used?\n        useClearVisual: stageHandler.isVisual && !stageHandler.isLayout,\n        plan: stageHandler.plan,\n        reset: stageHandler.reset,\n        scheduler: scheduler\n      };\n      scheduler._pipe(seriesModel, task);\n    }\n  };\n  Scheduler.prototype._createOverallStageTask = function (stageHandler, stageHandlerRecord, ecModel, api) {\n    var scheduler = this;\n    var overallTask = stageHandlerRecord.overallTask = stageHandlerRecord.overallTask\n    // For overall task, the function only be called on reset stage.\n    || createTask({\n      reset: overallTaskReset\n    });\n    overallTask.context = {\n      ecModel: ecModel,\n      api: api,\n      overallReset: stageHandler.overallReset,\n      scheduler: scheduler\n    };\n    var oldAgentStubMap = overallTask.agentStubMap;\n    // The count of stages are totally about only several dozen, so\n    // do not need to reuse the map.\n    var newAgentStubMap = overallTask.agentStubMap = createHashMap();\n    var seriesType = stageHandler.seriesType;\n    var getTargetSeries = stageHandler.getTargetSeries;\n    var overallProgress = true;\n    var shouldOverallTaskDirty = false;\n    // FIXME:TS never used, so comment it\n    // let modifyOutputEnd = stageHandler.modifyOutputEnd;\n    // An overall task with seriesType detected or has `getTargetSeries`, we add\n    // stub in each pipelines, it will set the overall task dirty when the pipeline\n    // progress. Moreover, to avoid call the overall task each frame (too frequent),\n    // we set the pipeline block.\n    var errMsg = '';\n    if (process.env.NODE_ENV !== 'production') {\n      errMsg = '\"createOnAllSeries\" is not supported for \"overallReset\", ' + 'because it will block all streams.';\n    }\n    assert(!stageHandler.createOnAllSeries, errMsg);\n    if (seriesType) {\n      ecModel.eachRawSeriesByType(seriesType, createStub);\n    } else if (getTargetSeries) {\n      getTargetSeries(ecModel, api).each(createStub);\n    }\n    // Otherwise, (usually it is legacy case), the overall task will only be\n    // executed when upstream is dirty. Otherwise the progressive rendering of all\n    // pipelines will be disabled unexpectedly. But it still needs stubs to receive\n    // dirty info from upstream.\n    else {\n      overallProgress = false;\n      each(ecModel.getSeries(), createStub);\n    }\n    function createStub(seriesModel) {\n      var pipelineId = seriesModel.uid;\n      var stub = newAgentStubMap.set(pipelineId, oldAgentStubMap && oldAgentStubMap.get(pipelineId) || (\n      // When the result of `getTargetSeries` changed, the overallTask\n      // should be set as dirty and re-performed.\n      shouldOverallTaskDirty = true, createTask({\n        reset: stubReset,\n        onDirty: stubOnDirty\n      })));\n      stub.context = {\n        model: seriesModel,\n        overallProgress: overallProgress\n        // FIXME:TS never used, so comment it\n        // modifyOutputEnd: modifyOutputEnd\n      };\n      stub.agent = overallTask;\n      stub.__block = overallProgress;\n      scheduler._pipe(seriesModel, stub);\n    }\n    if (shouldOverallTaskDirty) {\n      overallTask.dirty();\n    }\n  };\n  Scheduler.prototype._pipe = function (seriesModel, task) {\n    var pipelineId = seriesModel.uid;\n    var pipeline = this._pipelineMap.get(pipelineId);\n    !pipeline.head && (pipeline.head = task);\n    pipeline.tail && pipeline.tail.pipe(task);\n    pipeline.tail = task;\n    task.__idxInPipeline = pipeline.count++;\n    task.__pipeline = pipeline;\n  };\n  Scheduler.wrapStageHandler = function (stageHandler, visualType) {\n    if (isFunction(stageHandler)) {\n      stageHandler = {\n        overallReset: stageHandler,\n        seriesType: detectSeriseType(stageHandler)\n      };\n    }\n    stageHandler.uid = getUID('stageHandler');\n    visualType && (stageHandler.visualType = visualType);\n    return stageHandler;\n  };\n  ;\n  return Scheduler;\n}();\nfunction overallTaskReset(context) {\n  context.overallReset(context.ecModel, context.api, context.payload);\n}\nfunction stubReset(context) {\n  return context.overallProgress && stubProgress;\n}\nfunction stubProgress() {\n  this.agent.dirty();\n  this.getDownstream().dirty();\n}\nfunction stubOnDirty() {\n  this.agent && this.agent.dirty();\n}\nfunction seriesTaskPlan(context) {\n  return context.plan ? context.plan(context.model, context.ecModel, context.api, context.payload) : null;\n}\nfunction seriesTaskReset(context) {\n  if (context.useClearVisual) {\n    context.data.clearAllVisual();\n  }\n  var resetDefines = context.resetDefines = normalizeToArray(context.reset(context.model, context.ecModel, context.api, context.payload));\n  return resetDefines.length > 1 ? map(resetDefines, function (v, idx) {\n    return makeSeriesTaskProgress(idx);\n  }) : singleSeriesTaskProgress;\n}\nvar singleSeriesTaskProgress = makeSeriesTaskProgress(0);\nfunction makeSeriesTaskProgress(resetDefineIdx) {\n  return function (params, context) {\n    var data = context.data;\n    var resetDefine = context.resetDefines[resetDefineIdx];\n    if (resetDefine && resetDefine.dataEach) {\n      for (var i = params.start; i < params.end; i++) {\n        resetDefine.dataEach(data, i);\n      }\n    } else if (resetDefine && resetDefine.progress) {\n      resetDefine.progress(params, data);\n    }\n  };\n}\nfunction seriesTaskCount(context) {\n  return context.data.count();\n}\n/**\r\n * Only some legacy stage handlers (usually in echarts extensions) are pure function.\r\n * To ensure that they can work normally, they should work in block mode, that is,\r\n * they should not be started util the previous tasks finished. So they cause the\r\n * progressive rendering disabled. We try to detect the series type, to narrow down\r\n * the block range to only the series type they concern, but not all series.\r\n */\nfunction detectSeriseType(legacyFunc) {\n  seriesType = null;\n  try {\n    // Assume there is no async when calling `eachSeriesByType`.\n    legacyFunc(ecModelMock, apiMock);\n  } catch (e) {}\n  return seriesType;\n}\nvar ecModelMock = {};\nvar apiMock = {};\nvar seriesType;\nmockMethods(ecModelMock, GlobalModel);\nmockMethods(apiMock, ExtensionAPI);\necModelMock.eachSeriesByType = ecModelMock.eachRawSeriesByType = function (type) {\n  seriesType = type;\n};\necModelMock.eachComponent = function (cond) {\n  if (cond.mainType === 'series' && cond.subType) {\n    seriesType = cond.subType;\n  }\n};\nfunction mockMethods(target, Clz) {\n  /* eslint-disable */\n  for (var name_1 in Clz.prototype) {\n    // Do not use hasOwnProperty\n    target[name_1] = noop;\n  }\n  /* eslint-enable */\n}\nexport default Scheduler;"], "names": [], "mappings": "AACA;;;;;;;;;;;;;;;;;AAiBA,GAGA;;CAEC,GAED;;;;;;;;;;;;;;;;;AAiBA;;;AA8HU;AA7HV;AACA;AACA;AACA;AACA;AACA;;;;;;;;AAEA,IAAI,YAAY,WAAW,GAAE;IAC3B,SAAS,UAAU,UAAU,EAAE,GAAG,EAAE,qBAAqB,EAAE,cAAc;QACvE,kBAAkB;QAClB,IAAI,CAAC,aAAa,GAAG,CAAA,GAAA,iJAAA,CAAA,gBAAa,AAAD;QACjC,IAAI,CAAC,UAAU,GAAG;QAClB,IAAI,CAAC,GAAG,GAAG;QACX,8DAA8D;QAC9D,iEAAiE;QACjE,8DAA8D;QAC9D,6CAA6C;QAC7C,wBAAwB,IAAI,CAAC,sBAAsB,GAAG,sBAAsB,KAAK;QACjF,iBAAiB,IAAI,CAAC,eAAe,GAAG,eAAe,KAAK;QAC5D,IAAI,CAAC,YAAY,GAAG,sBAAsB,MAAM,CAAC;IACnD;IACA,UAAU,SAAS,CAAC,WAAW,GAAG,SAAU,OAAO,EAAE,OAAO;QAC1D,2EAA2E;QAC3E,6EAA6E;QAC7E,gFAAgF;QAChF,8EAA8E;QAC9E,6EAA6E;QAC7E,oCAAoC;QACpC,oFAAoF;QACpF,8EAA8E;QAC9E,6EAA6E;QAC7E,8BAA8B;QAC9B,oFAAoF;QACpF,gFAAgF;QAChF,+CAA+C;QAC/C,sFAAsF;QACtF,0FAA0F;QAC1F,uDAAuD;QACvD,QAAQ,WAAW,CAAC;QACpB,wFAAwF;QACxF,gCAAgC;QAChC,yFAAyF;QACzF,wFAAwF;QACxF,yFAAyF;QACzF,0FAA0F;QAC1F,6DAA6D;QAC7D,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,SAAU,UAAU;YAC1C,IAAI,cAAc,WAAW,WAAW;YACxC,eAAe,YAAY,KAAK;QAClC;IACF;IACA,0EAA0E;IAC1E,UAAU,SAAS,CAAC,cAAc,GAAG,SAAU,IAAI,EAAE,OAAO;QAC1D,mBAAmB;QACnB,IAAI,CAAC,KAAK,UAAU,EAAE;YACpB;QACF;QACA,IAAI,WAAW,IAAI,CAAC,YAAY,CAAC,GAAG,CAAC,KAAK,UAAU,CAAC,EAAE;QACvD,IAAI,OAAO,SAAS,OAAO;QAC3B,IAAI,cAAc,CAAC,WAAW,SAAS,kBAAkB,IAAI,CAAC,CAAC,QAAQ,KAAK,iBAAiB,KAAK,KAAK,eAAe,GAAG,SAAS,UAAU;QAC5I,IAAI,OAAO,cAAc,SAAS,IAAI,GAAG;QACzC,IAAI,eAAe,QAAQ,KAAK,YAAY;QAC5C,IAAI,QAAQ,gBAAgB,OAAO,KAAK,IAAI,CAAC,eAAe,QAAQ;QACpE,OAAO;YACL,MAAM;YACN,OAAO;YACP,cAAc;QAChB;IACF;IACA,UAAU,SAAS,CAAC,WAAW,GAAG,SAAU,UAAU;QACpD,OAAO,IAAI,CAAC,YAAY,CAAC,GAAG,CAAC;IAC/B;IACA;;;;;;GAMC,GACD,UAAU,SAAS,CAAC,iBAAiB,GAAG,SAAU,WAAW,EAAE,IAAI;QACjE,IAAI,WAAW,IAAI,CAAC,YAAY,CAAC,GAAG,CAAC,YAAY,GAAG;QACpD,IAAI,OAAO,YAAY,OAAO;QAC9B,IAAI,UAAU,KAAK,KAAK;QACxB,kEAAkE;QAClE,iEAAiE;QACjE,qEAAqE;QACrE,mEAAmE;QACnE,gCAAgC;QAChC,IAAI,oBAAoB,SAAS,kBAAkB,IAAI,KAAK,wBAAwB,IAAI,WAAW,SAAS,SAAS;QACrH,IAAI,QAAQ,YAAY,GAAG,CAAC,YAAY,WAAW,YAAY,GAAG,CAAC;QACnE,wFAAwF;QACxF,qCAAqC;QACrC,IAAI,eAAe,YAAY,GAAG,CAAC,4BAA4B,QAAQ,UAAU;QACjF,YAAY,eAAe,GAAG,SAAS,OAAO,GAAG;YAC/C,mBAAmB;YACnB,cAAc;YACd,OAAO;QACT;IACF;IACA,UAAU,SAAS,CAAC,gBAAgB,GAAG,SAAU,OAAO;QACtD,IAAI,YAAY,IAAI;QACpB,IAAI,cAAc,UAAU,YAAY,GAAG,CAAA,GAAA,iJAAA,CAAA,gBAAa,AAAD;QACvD,QAAQ,UAAU,CAAC,SAAU,WAAW;YACtC,IAAI,cAAc,YAAY,cAAc;YAC5C,IAAI,aAAa,YAAY,GAAG;YAChC,YAAY,GAAG,CAAC,YAAY;gBAC1B,IAAI;gBACJ,MAAM;gBACN,MAAM;gBACN,WAAW,YAAY,uBAAuB;gBAC9C,oBAAoB,eAAe,CAAC,CAAC,YAAY,kBAAkB,IAAI,YAAY,kBAAkB,EAAE;gBACvG,YAAY,CAAC;gBACb,MAAM,KAAK,KAAK,CAAC,eAAe;gBAChC,OAAO;YACT;YACA,UAAU,KAAK,CAAC,aAAa,YAAY,QAAQ;QACnD;IACF;IACA,UAAU,SAAS,CAAC,iBAAiB,GAAG;QACtC,IAAI,eAAe,IAAI,CAAC,aAAa;QACrC,IAAI,UAAU,IAAI,CAAC,GAAG,CAAC,QAAQ;QAC/B,IAAI,MAAM,IAAI,CAAC,GAAG;QAClB,CAAA,GAAA,iJAAA,CAAA,OAAI,AAAD,EAAE,IAAI,CAAC,YAAY,EAAE,SAAU,OAAO;YACvC,IAAI,SAAS,aAAa,GAAG,CAAC,QAAQ,GAAG,KAAK,aAAa,GAAG,CAAC,QAAQ,GAAG,EAAE,CAAC;YAC7E,IAAI,SAAS;YACb,IAAI,oDAAyB,cAAc;gBACzC,0DAA0D;gBAC1D,SAAS;YACX;YACA,CAAA,GAAA,iJAAA,CAAA,SAAM,AAAD,EAAE,CAAC,CAAC,QAAQ,KAAK,IAAI,QAAQ,YAAY,GAAG;YACjD,QAAQ,KAAK,IAAI,IAAI,CAAC,sBAAsB,CAAC,SAAS,QAAQ,SAAS;YACvE,QAAQ,YAAY,IAAI,IAAI,CAAC,uBAAuB,CAAC,SAAS,QAAQ,SAAS;QACjF,GAAG,IAAI;IACT;IACA,UAAU,SAAS,CAAC,WAAW,GAAG,SAAU,IAAI,EAAE,KAAK,EAAE,OAAO,EAAE,GAAG;QACnE,IAAI,aAAa,KAAK,UAAU;QAChC,IAAI,UAAU,WAAW,OAAO;QAChC,QAAQ,KAAK,GAAG;QAChB,QAAQ,OAAO,GAAG;QAClB,QAAQ,GAAG,GAAG;QACd,WAAW,OAAO,GAAG,CAAC,KAAK,wBAAwB;QACnD,IAAI,CAAC,KAAK,CAAC,OAAO;IACpB;IACA,UAAU,SAAS,CAAC,yBAAyB,GAAG,SAAU,OAAO,EAAE,OAAO;QACxE,+EAA+E;QAC/E,IAAI,CAAC,kBAAkB,CAAC,IAAI,CAAC,sBAAsB,EAAE,SAAS,SAAS;YACrE,OAAO;QACT;IACF;IACA,UAAU,SAAS,CAAC,kBAAkB,GAAG,SAAU,OAAO,EAAE,OAAO,EAAE,GAAG;QACtE,IAAI,CAAC,kBAAkB,CAAC,IAAI,CAAC,eAAe,EAAE,SAAS,SAAS;IAClE;IACA,UAAU,SAAS,CAAC,kBAAkB,GAAG,SAAU,aAAa,EAAE,OAAO,EAAE,OAAO,EAAE,GAAG;QACrF,MAAM,OAAO,CAAC;QACd,IAAI,aAAa;QACjB,IAAI,YAAY,IAAI;QACpB,CAAA,GAAA,iJAAA,CAAA,OAAI,AAAD,EAAE,eAAe,SAAU,YAAY,EAAE,GAAG;YAC7C,IAAI,IAAI,UAAU,IAAI,IAAI,UAAU,KAAK,aAAa,UAAU,EAAE;gBAChE;YACF;YACA,IAAI,qBAAqB,UAAU,aAAa,CAAC,GAAG,CAAC,aAAa,GAAG;YACrE,IAAI,gBAAgB,mBAAmB,aAAa;YACpD,IAAI,cAAc,mBAAmB,WAAW;YAChD,IAAI,aAAa;gBACf,IAAI;gBACJ,IAAI,eAAe,YAAY,YAAY;gBAC3C,aAAa,IAAI,CAAC,SAAU,IAAI;oBAC9B,IAAI,aAAa,KAAK,OAAO;wBAC3B,KAAK,KAAK;wBACV,qBAAqB;oBACvB;gBACF;gBACA,sBAAsB,YAAY,KAAK;gBACvC,UAAU,aAAa,CAAC,aAAa;gBACrC,IAAI,gBAAgB,UAAU,cAAc,CAAC,aAAa,IAAI,KAAK;gBACnE,+DAA+D;gBAC/D,yEAAyE;gBACzE,uEAAuE;gBACvE,yBAAyB;gBACzB,aAAa,IAAI,CAAC,SAAU,IAAI;oBAC9B,KAAK,OAAO,CAAC;gBACf;gBACA,IAAI,YAAY,OAAO,CAAC,gBAAgB;oBACtC,aAAa;gBACf;YACF,OAAO,IAAI,eAAe;gBACxB,cAAc,IAAI,CAAC,SAAU,IAAI,EAAE,UAAU;oBAC3C,IAAI,aAAa,KAAK,OAAO;wBAC3B,KAAK,KAAK;oBACZ;oBACA,IAAI,cAAc,UAAU,cAAc,CAAC,MAAM,IAAI,KAAK;oBAC1D,QAAQ;oBACR,+DAA+D;oBAC/D,6EAA6E;oBAC7E,yEAAyE;oBACzE,6EAA6E;oBAC7E,0EAA0E;oBAC1E,8EAA8E;oBAC9E,gEAAgE;oBAChE,YAAY,IAAI,GAAG,CAAC,aAAa,gBAAgB,IAAI,QAAQ,gBAAgB,CAAC,KAAK,OAAO,CAAC,KAAK;oBAChG,UAAU,aAAa,CAAC,MAAM;oBAC9B,IAAI,KAAK,OAAO,CAAC,cAAc;wBAC7B,aAAa;oBACf;gBACF;YACF;QACF;QACA,SAAS,aAAa,GAAG,EAAE,IAAI;YAC7B,OAAO,IAAI,QAAQ,IAAI,CAAC,CAAC,IAAI,QAAQ,IAAI,IAAI,QAAQ,CAAC,GAAG,CAAC,KAAK,UAAU,CAAC,EAAE,CAAC;QAC/E;QACA,IAAI,CAAC,UAAU,GAAG,cAAc,IAAI,CAAC,UAAU;IACjD;IACA,UAAU,SAAS,CAAC,kBAAkB,GAAG,SAAU,OAAO;QACxD,IAAI;QACJ,QAAQ,UAAU,CAAC,SAAU,WAAW;YACtC,oDAAoD;YACpD,aAAa,YAAY,QAAQ,CAAC,OAAO,MAAM;QACjD;QACA,IAAI,CAAC,UAAU,GAAG,cAAc,IAAI,CAAC,UAAU;IACjD;IACA,UAAU,SAAS,CAAC,IAAI,GAAG;QACzB,iCAAiC;QACjC,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,SAAU,QAAQ;YACvC,IAAI,OAAO,SAAS,IAAI;YACxB,GAAG;gBACD,IAAI,KAAK,OAAO,EAAE;oBAChB,SAAS,UAAU,GAAG,KAAK,eAAe;oBAC1C;gBACF;gBACA,OAAO,KAAK,WAAW;YACzB,QAAS,KAAM;QACjB;IACF;IACA,UAAU,SAAS,CAAC,aAAa,GAAG,SAAU,IAAI,EAAE,OAAO;QACzD,YAAY,YAAY,CAAC,KAAK,OAAO,CAAC,OAAO,GAAG,OAAO;IACzD;IACA,UAAU,SAAS,CAAC,sBAAsB,GAAG,SAAU,YAAY,EAAE,kBAAkB,EAAE,OAAO,EAAE,GAAG;QACnG,IAAI,YAAY,IAAI;QACpB,IAAI,mBAAmB,mBAAmB,aAAa;QACvD,+DAA+D;QAC/D,gCAAgC;QAChC,IAAI,mBAAmB,mBAAmB,aAAa,GAAG,CAAA,GAAA,iJAAA,CAAA,gBAAa,AAAD;QACtE,IAAI,aAAa,aAAa,UAAU;QACxC,IAAI,kBAAkB,aAAa,eAAe;QAClD,iGAAiG;QACjG,wFAAwF;QACxF,6DAA6D;QAC7D,IAAI,aAAa,iBAAiB,EAAE;YAClC,QAAQ,aAAa,CAAC;QACxB,OAAO,IAAI,YAAY;YACrB,QAAQ,mBAAmB,CAAC,YAAY;QAC1C,OAAO,IAAI,iBAAiB;YAC1B,gBAAgB,SAAS,KAAK,IAAI,CAAC;QACrC;QACA,SAAS,OAAO,WAAW;YACzB,IAAI,aAAa,YAAY,GAAG;YAChC,6CAA6C;YAC7C,gCAAgC;YAChC,IAAI,OAAO,iBAAiB,GAAG,CAAC,YAAY,oBAAoB,iBAAiB,GAAG,CAAC,eAAe,CAAA,GAAA,iJAAA,CAAA,aAAU,AAAD,EAAE;gBAC7G,MAAM;gBACN,OAAO;gBACP,OAAO;YACT;YACA,KAAK,OAAO,GAAG;gBACb,OAAO;gBACP,SAAS;gBACT,KAAK;gBACL,sCAAsC;gBACtC,gBAAgB,aAAa,QAAQ,IAAI,CAAC,aAAa,QAAQ;gBAC/D,MAAM,aAAa,IAAI;gBACvB,OAAO,aAAa,KAAK;gBACzB,WAAW;YACb;YACA,UAAU,KAAK,CAAC,aAAa;QAC/B;IACF;IACA,UAAU,SAAS,CAAC,uBAAuB,GAAG,SAAU,YAAY,EAAE,kBAAkB,EAAE,OAAO,EAAE,GAAG;QACpG,IAAI,YAAY,IAAI;QACpB,IAAI,cAAc,mBAAmB,WAAW,GAAG,mBAAmB,WAAW,IAE9E,CAAA,GAAA,iJAAA,CAAA,aAAU,AAAD,EAAE;YACZ,OAAO;QACT;QACA,YAAY,OAAO,GAAG;YACpB,SAAS;YACT,KAAK;YACL,cAAc,aAAa,YAAY;YACvC,WAAW;QACb;QACA,IAAI,kBAAkB,YAAY,YAAY;QAC9C,+DAA+D;QAC/D,gCAAgC;QAChC,IAAI,kBAAkB,YAAY,YAAY,GAAG,CAAA,GAAA,iJAAA,CAAA,gBAAa,AAAD;QAC7D,IAAI,aAAa,aAAa,UAAU;QACxC,IAAI,kBAAkB,aAAa,eAAe;QAClD,IAAI,kBAAkB;QACtB,IAAI,yBAAyB;QAC7B,qCAAqC;QACrC,sDAAsD;QACtD,4EAA4E;QAC5E,+EAA+E;QAC/E,gFAAgF;QAChF,6BAA6B;QAC7B,IAAI,SAAS;QACb,IAAI,oDAAyB,cAAc;YACzC,SAAS,8DAA8D;QACzE;QACA,CAAA,GAAA,iJAAA,CAAA,SAAM,AAAD,EAAE,CAAC,aAAa,iBAAiB,EAAE;QACxC,IAAI,YAAY;YACd,QAAQ,mBAAmB,CAAC,YAAY;QAC1C,OAAO,IAAI,iBAAiB;YAC1B,gBAAgB,SAAS,KAAK,IAAI,CAAC;QACrC,OAKK;YACH,kBAAkB;YAClB,CAAA,GAAA,iJAAA,CAAA,OAAI,AAAD,EAAE,QAAQ,SAAS,IAAI;QAC5B;QACA,SAAS,WAAW,WAAW;YAC7B,IAAI,aAAa,YAAY,GAAG;YAChC,IAAI,OAAO,gBAAgB,GAAG,CAAC,YAAY,mBAAmB,gBAAgB,GAAG,CAAC,eAAe,CACjG,gEAAgE;YAChE,2CAA2C;YAC3C,yBAAyB,MAAM,CAAA,GAAA,iJAAA,CAAA,aAAU,AAAD,EAAE;gBACxC,OAAO;gBACP,SAAS;YACX,EAAE;YACF,KAAK,OAAO,GAAG;gBACb,OAAO;gBACP,iBAAiB;YAGnB;YACA,KAAK,KAAK,GAAG;YACb,KAAK,OAAO,GAAG;YACf,UAAU,KAAK,CAAC,aAAa;QAC/B;QACA,IAAI,wBAAwB;YAC1B,YAAY,KAAK;QACnB;IACF;IACA,UAAU,SAAS,CAAC,KAAK,GAAG,SAAU,WAAW,EAAE,IAAI;QACrD,IAAI,aAAa,YAAY,GAAG;QAChC,IAAI,WAAW,IAAI,CAAC,YAAY,CAAC,GAAG,CAAC;QACrC,CAAC,SAAS,IAAI,IAAI,CAAC,SAAS,IAAI,GAAG,IAAI;QACvC,SAAS,IAAI,IAAI,SAAS,IAAI,CAAC,IAAI,CAAC;QACpC,SAAS,IAAI,GAAG;QAChB,KAAK,eAAe,GAAG,SAAS,KAAK;QACrC,KAAK,UAAU,GAAG;IACpB;IACA,UAAU,gBAAgB,GAAG,SAAU,YAAY,EAAE,UAAU;QAC7D,IAAI,CAAA,GAAA,iJAAA,CAAA,aAAU,AAAD,EAAE,eAAe;YAC5B,eAAe;gBACb,cAAc;gBACd,YAAY,iBAAiB;YAC/B;QACF;QACA,aAAa,GAAG,GAAG,CAAA,GAAA,sJAAA,CAAA,SAAM,AAAD,EAAE;QAC1B,cAAc,CAAC,aAAa,UAAU,GAAG,UAAU;QACnD,OAAO;IACT;;IAEA,OAAO;AACT;AACA,SAAS,iBAAiB,OAAO;IAC/B,QAAQ,YAAY,CAAC,QAAQ,OAAO,EAAE,QAAQ,GAAG,EAAE,QAAQ,OAAO;AACpE;AACA,SAAS,UAAU,OAAO;IACxB,OAAO,QAAQ,eAAe,IAAI;AACpC;AACA,SAAS;IACP,IAAI,CAAC,KAAK,CAAC,KAAK;IAChB,IAAI,CAAC,aAAa,GAAG,KAAK;AAC5B;AACA,SAAS;IACP,IAAI,CAAC,KAAK,IAAI,IAAI,CAAC,KAAK,CAAC,KAAK;AAChC;AACA,SAAS,eAAe,OAAO;IAC7B,OAAO,QAAQ,IAAI,GAAG,QAAQ,IAAI,CAAC,QAAQ,KAAK,EAAE,QAAQ,OAAO,EAAE,QAAQ,GAAG,EAAE,QAAQ,OAAO,IAAI;AACrG;AACA,SAAS,gBAAgB,OAAO;IAC9B,IAAI,QAAQ,cAAc,EAAE;QAC1B,QAAQ,IAAI,CAAC,cAAc;IAC7B;IACA,IAAI,eAAe,QAAQ,YAAY,GAAG,CAAA,GAAA,kJAAA,CAAA,mBAAgB,AAAD,EAAE,QAAQ,KAAK,CAAC,QAAQ,KAAK,EAAE,QAAQ,OAAO,EAAE,QAAQ,GAAG,EAAE,QAAQ,OAAO;IACrI,OAAO,aAAa,MAAM,GAAG,IAAI,CAAA,GAAA,iJAAA,CAAA,MAAG,AAAD,EAAE,cAAc,SAAU,CAAC,EAAE,GAAG;QACjE,OAAO,uBAAuB;IAChC,KAAK;AACP;AACA,IAAI,2BAA2B,uBAAuB;AACtD,SAAS,uBAAuB,cAAc;IAC5C,OAAO,SAAU,MAAM,EAAE,OAAO;QAC9B,IAAI,OAAO,QAAQ,IAAI;QACvB,IAAI,cAAc,QAAQ,YAAY,CAAC,eAAe;QACtD,IAAI,eAAe,YAAY,QAAQ,EAAE;YACvC,IAAK,IAAI,IAAI,OAAO,KAAK,EAAE,IAAI,OAAO,GAAG,EAAE,IAAK;gBAC9C,YAAY,QAAQ,CAAC,MAAM;YAC7B;QACF,OAAO,IAAI,eAAe,YAAY,QAAQ,EAAE;YAC9C,YAAY,QAAQ,CAAC,QAAQ;QAC/B;IACF;AACF;AACA,SAAS,gBAAgB,OAAO;IAC9B,OAAO,QAAQ,IAAI,CAAC,KAAK;AAC3B;AACA;;;;;;CAMC,GACD,SAAS,iBAAiB,UAAU;IAClC,aAAa;IACb,IAAI;QACF,4DAA4D;QAC5D,WAAW,aAAa;IAC1B,EAAE,OAAO,GAAG,CAAC;IACb,OAAO;AACT;AACA,IAAI,cAAc,CAAC;AACnB,IAAI,UAAU,CAAC;AACf,IAAI;AACJ,YAAY,aAAa,oJAAA,CAAA,UAAW;AACpC,YAAY,SAAS,yJAAA,CAAA,UAAY;AACjC,YAAY,gBAAgB,GAAG,YAAY,mBAAmB,GAAG,SAAU,IAAI;IAC7E,aAAa;AACf;AACA,YAAY,aAAa,GAAG,SAAU,IAAI;IACxC,IAAI,KAAK,QAAQ,KAAK,YAAY,KAAK,OAAO,EAAE;QAC9C,aAAa,KAAK,OAAO;IAC3B;AACF;AACA,SAAS,YAAY,MAAM,EAAE,GAAG;IAC9B,kBAAkB,GAClB,IAAK,IAAI,UAAU,IAAI,SAAS,CAAE;QAChC,4BAA4B;QAC5B,MAAM,CAAC,OAAO,GAAG,iJAAA,CAAA,OAAI;IACvB;AACA,iBAAiB,GACnB;uCACe", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1078, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/tindahan/sari-sari-admin/node_modules/echarts/lib/core/lifecycle.js"], "sourcesContent": ["\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\r\n* Licensed to the Apache Software Foundation (ASF) under one\r\n* or more contributor license agreements.  See the NOTICE file\r\n* distributed with this work for additional information\r\n* regarding copyright ownership.  The ASF licenses this file\r\n* to you under the Apache License, Version 2.0 (the\r\n* \"License\"); you may not use this file except in compliance\r\n* with the License.  You may obtain a copy of the License at\r\n*\r\n*   http://www.apache.org/licenses/LICENSE-2.0\r\n*\r\n* Unless required by applicable law or agreed to in writing,\r\n* software distributed under the License is distributed on an\r\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\r\n* KIND, either express or implied.  See the License for the\r\n* specific language governing permissions and limitations\r\n* under the License.\r\n*/\nimport Eventful from 'zrender/lib/core/Eventful.js';\n;\nvar lifecycle = new Eventful();\nexport default lifecycle;"], "names": [], "mappings": "AACA;;;;;;;;;;;;;;;;;AAiBA,GAGA;;CAEC,GAED;;;;;;;;;;;;;;;;;AAiBA;;;AACA;;;AAEA,IAAI,YAAY,IAAI,qJAAA,CAAA,UAAQ;uCACb", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1128, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/tindahan/sari-sari-admin/node_modules/echarts/lib/core/impl.js"], "sourcesContent": ["\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\r\n* Licensed to the Apache Software Foundation (ASF) under one\r\n* or more contributor license agreements.  See the NOTICE file\r\n* distributed with this work for additional information\r\n* regarding copyright ownership.  The ASF licenses this file\r\n* to you under the Apache License, Version 2.0 (the\r\n* \"License\"); you may not use this file except in compliance\r\n* with the License.  You may obtain a copy of the License at\r\n*\r\n*   http://www.apache.org/licenses/LICENSE-2.0\r\n*\r\n* Unless required by applicable law or agreed to in writing,\r\n* software distributed under the License is distributed on an\r\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\r\n* KIND, either express or implied.  See the License for the\r\n* specific language governing permissions and limitations\r\n* under the License.\r\n*/\nimport { error } from '../util/log.js';\n// Implementation of exported APIs. For example registerMap, getMap.\n// The implementations will be registered when installing the component.\n// Avoid these code being bundled to the core module.\nvar implsStore = {};\n// TODO Type\nexport function registerImpl(name, impl) {\n  if (process.env.NODE_ENV !== 'production') {\n    if (implsStore[name]) {\n      error(\"Already has an implementation of \" + name + \".\");\n    }\n  }\n  implsStore[name] = impl;\n}\nexport function getImpl(name) {\n  if (process.env.NODE_ENV !== 'production') {\n    if (!implsStore[name]) {\n      error(\"Implementation of \" + name + \" doesn't exists.\");\n    }\n  }\n  return implsStore[name];\n}"], "names": [], "mappings": "AACA;;;;;;;;;;;;;;;;;AAiBA,GAGA;;CAEC,GAED;;;;;;;;;;;;;;;;;AAiBA;;;;AAQM;AAPN;;AACA,oEAAoE;AACpE,wEAAwE;AACxE,qDAAqD;AACrD,IAAI,aAAa,CAAC;AAEX,SAAS,aAAa,IAAI,EAAE,IAAI;IACrC,wCAA2C;QACzC,IAAI,UAAU,CAAC,KAAK,EAAE;YACpB,CAAA,GAAA,gJAAA,CAAA,QAAK,AAAD,EAAE,sCAAsC,OAAO;QACrD;IACF;IACA,UAAU,CAAC,KAAK,GAAG;AACrB;AACO,SAAS,QAAQ,IAAI;IAC1B,wCAA2C;QACzC,IAAI,CAAC,UAAU,CAAC,KAAK,EAAE;YACrB,CAAA,GAAA,gJAAA,CAAA,QAAK,AAAD,EAAE,uBAAuB,OAAO;QACtC;IACF;IACA,OAAO,UAAU,CAAC,KAAK;AACzB", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1197, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/tindahan/sari-sari-admin/node_modules/echarts/lib/core/echarts.js"], "sourcesContent": ["\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\nimport { __extends } from \"tslib\";\n/*\r\n* Licensed to the Apache Software Foundation (ASF) under one\r\n* or more contributor license agreements.  See the NOTICE file\r\n* distributed with this work for additional information\r\n* regarding copyright ownership.  The ASF licenses this file\r\n* to you under the Apache License, Version 2.0 (the\r\n* \"License\"); you may not use this file except in compliance\r\n* with the License.  You may obtain a copy of the License at\r\n*\r\n*   http://www.apache.org/licenses/LICENSE-2.0\r\n*\r\n* Unless required by applicable law or agreed to in writing,\r\n* software distributed under the License is distributed on an\r\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\r\n* KIND, either express or implied.  See the License for the\r\n* specific language governing permissions and limitations\r\n* under the License.\r\n*/\nimport * as zrender from 'zrender/lib/zrender.js';\nimport { assert, each, isFunction, isObject, indexOf, bind, clone, setAsPrimitive, extend, createHashMap, map, defaults, isDom, isArray, noop, isString, retrieve2 } from 'zrender/lib/core/util.js';\nimport env from 'zrender/lib/core/env.js';\nimport timsort from 'zrender/lib/core/timsort.js';\nimport Eventful from 'zrender/lib/core/Eventful.js';\nimport GlobalModel from '../model/Global.js';\nimport ExtensionAPI from './ExtensionAPI.js';\nimport CoordinateSystemManager from './CoordinateSystem.js';\nimport OptionManager from '../model/OptionManager.js';\nimport backwardCompat from '../preprocessor/backwardCompat.js';\nimport dataStack from '../processor/dataStack.js';\nimport SeriesModel from '../model/Series.js';\nimport ComponentView from '../view/Component.js';\nimport ChartView from '../view/Chart.js';\nimport * as graphic from '../util/graphic.js';\nimport { getECData } from '../util/innerStore.js';\nimport { isHighDownDispatcher, HOVER_STATE_EMPHASIS, HOVER_STATE_BLUR, blurSeriesFromHighlightPayload, toggleSelectionFromPayload, updateSeriesElementSelection, getAllSelectedIndices, isSelectChangePayload, isHighDownPayload, HIGHLIGHT_ACTION_TYPE, DOWNPLAY_ACTION_TYPE, SELECT_ACTION_TYPE, UNSELECT_ACTION_TYPE, TOGGLE_SELECT_ACTION_TYPE, savePathStates, enterEmphasis, leaveEmphasis, leaveBlur, enterSelect, leaveSelect, enterBlur, allLeaveBlur, findComponentHighDownDispatchers, blurComponent, handleGlobalMouseOverForHighDown, handleGlobalMouseOutForHighDown } from '../util/states.js';\nimport * as modelUtil from '../util/model.js';\nimport { throttle } from '../util/throttle.js';\nimport { seriesStyleTask, dataStyleTask, dataColorPaletteTask } from '../visual/style.js';\nimport loadingDefault from '../loading/default.js';\nimport Scheduler from './Scheduler.js';\nimport lightTheme from '../theme/light.js';\nimport darkTheme from '../theme/dark.js';\nimport { parseClassType } from '../util/clazz.js';\nimport { ECEventProcessor } from '../util/ECEventProcessor.js';\nimport { seriesSymbolTask, dataSymbolTask } from '../visual/symbol.js';\nimport { getVisualFromData, getItemVisualFromData } from '../visual/helper.js';\nimport { deprecateLog, deprecateReplaceLog, error, warn } from '../util/log.js';\nimport { handleLegacySelectEvents } from '../legacy/dataSelectAction.js';\nimport { registerExternalTransform } from '../data/helper/transform.js';\nimport { createLocaleObject, SYSTEM_LANG } from './locale.js';\nimport { findEventDispatcher } from '../util/event.js';\nimport decal from '../visual/decal.js';\nimport lifecycle from './lifecycle.js';\nimport { platformApi, setPlatformAPI } from 'zrender/lib/core/platform.js';\nimport { getImpl } from './impl.js';\nexport var version = '5.6.0';\nexport var dependencies = {\n  zrender: '5.6.1'\n};\nvar TEST_FRAME_REMAIN_TIME = 1;\nvar PRIORITY_PROCESSOR_SERIES_FILTER = 800;\n// Some data processors depends on the stack result dimension (to calculate data extent).\n// So data stack stage should be in front of data processing stage.\nvar PRIORITY_PROCESSOR_DATASTACK = 900;\n// \"Data filter\" will block the stream, so it should be\n// put at the beginning of data processing.\nvar PRIORITY_PROCESSOR_FILTER = 1000;\nvar PRIORITY_PROCESSOR_DEFAULT = 2000;\nvar PRIORITY_PROCESSOR_STATISTIC = 5000;\nvar PRIORITY_VISUAL_LAYOUT = 1000;\nvar PRIORITY_VISUAL_PROGRESSIVE_LAYOUT = 1100;\nvar PRIORITY_VISUAL_GLOBAL = 2000;\nvar PRIORITY_VISUAL_CHART = 3000;\nvar PRIORITY_VISUAL_COMPONENT = 4000;\n// Visual property in data. Greater than `PRIORITY_VISUAL_COMPONENT` to enable to\n// overwrite the viusal result of component (like `visualMap`)\n// using data item specific setting (like itemStyle.xxx on data item)\nvar PRIORITY_VISUAL_CHART_DATA_CUSTOM = 4500;\n// Greater than `PRIORITY_VISUAL_CHART_DATA_CUSTOM` to enable to layout based on\n// visual result like `symbolSize`.\nvar PRIORITY_VISUAL_POST_CHART_LAYOUT = 4600;\nvar PRIORITY_VISUAL_BRUSH = 5000;\nvar PRIORITY_VISUAL_ARIA = 6000;\nvar PRIORITY_VISUAL_DECAL = 7000;\nexport var PRIORITY = {\n  PROCESSOR: {\n    FILTER: PRIORITY_PROCESSOR_FILTER,\n    SERIES_FILTER: PRIORITY_PROCESSOR_SERIES_FILTER,\n    STATISTIC: PRIORITY_PROCESSOR_STATISTIC\n  },\n  VISUAL: {\n    LAYOUT: PRIORITY_VISUAL_LAYOUT,\n    PROGRESSIVE_LAYOUT: PRIORITY_VISUAL_PROGRESSIVE_LAYOUT,\n    GLOBAL: PRIORITY_VISUAL_GLOBAL,\n    CHART: PRIORITY_VISUAL_CHART,\n    POST_CHART_LAYOUT: PRIORITY_VISUAL_POST_CHART_LAYOUT,\n    COMPONENT: PRIORITY_VISUAL_COMPONENT,\n    BRUSH: PRIORITY_VISUAL_BRUSH,\n    CHART_ITEM: PRIORITY_VISUAL_CHART_DATA_CUSTOM,\n    ARIA: PRIORITY_VISUAL_ARIA,\n    DECAL: PRIORITY_VISUAL_DECAL\n  }\n};\n// Main process have three entries: `setOption`, `dispatchAction` and `resize`,\n// where they must not be invoked nestedly, except the only case: invoke\n// dispatchAction with updateMethod \"none\" in main process.\n// This flag is used to carry out this rule.\n// All events will be triggered out side main process (i.e. when !this[IN_MAIN_PROCESS]).\nvar IN_MAIN_PROCESS_KEY = '__flagInMainProcess';\nvar PENDING_UPDATE = '__pendingUpdate';\nvar STATUS_NEEDS_UPDATE_KEY = '__needsUpdateStatus';\nvar ACTION_REG = /^[a-zA-Z0-9_]+$/;\nvar CONNECT_STATUS_KEY = '__connectUpdateStatus';\nvar CONNECT_STATUS_PENDING = 0;\nvar CONNECT_STATUS_UPDATING = 1;\nvar CONNECT_STATUS_UPDATED = 2;\n;\n;\nfunction createRegisterEventWithLowercaseECharts(method) {\n  return function () {\n    var args = [];\n    for (var _i = 0; _i < arguments.length; _i++) {\n      args[_i] = arguments[_i];\n    }\n    if (this.isDisposed()) {\n      disposedWarning(this.id);\n      return;\n    }\n    return toLowercaseNameAndCallEventful(this, method, args);\n  };\n}\nfunction createRegisterEventWithLowercaseMessageCenter(method) {\n  return function () {\n    var args = [];\n    for (var _i = 0; _i < arguments.length; _i++) {\n      args[_i] = arguments[_i];\n    }\n    return toLowercaseNameAndCallEventful(this, method, args);\n  };\n}\nfunction toLowercaseNameAndCallEventful(host, method, args) {\n  // `args[0]` is event name. Event name is all lowercase.\n  args[0] = args[0] && args[0].toLowerCase();\n  return Eventful.prototype[method].apply(host, args);\n}\nvar MessageCenter = /** @class */function (_super) {\n  __extends(MessageCenter, _super);\n  function MessageCenter() {\n    return _super !== null && _super.apply(this, arguments) || this;\n  }\n  return MessageCenter;\n}(Eventful);\nvar messageCenterProto = MessageCenter.prototype;\nmessageCenterProto.on = createRegisterEventWithLowercaseMessageCenter('on');\nmessageCenterProto.off = createRegisterEventWithLowercaseMessageCenter('off');\n// ---------------------------------------\n// Internal method names for class ECharts\n// ---------------------------------------\nvar prepare;\nvar prepareView;\nvar updateDirectly;\nvar updateMethods;\nvar doConvertPixel;\nvar updateStreamModes;\nvar doDispatchAction;\nvar flushPendingActions;\nvar triggerUpdatedEvent;\nvar bindRenderedEvent;\nvar bindMouseEvent;\nvar render;\nvar renderComponents;\nvar renderSeries;\nvar createExtensionAPI;\nvar enableConnect;\nvar markStatusToUpdate;\nvar applyChangedStates;\nvar ECharts = /** @class */function (_super) {\n  __extends(ECharts, _super);\n  function ECharts(dom,\n  // Theme name or themeOption.\n  theme, opts) {\n    var _this = _super.call(this, new ECEventProcessor()) || this;\n    _this._chartsViews = [];\n    _this._chartsMap = {};\n    _this._componentsViews = [];\n    _this._componentsMap = {};\n    // Can't dispatch action during rendering procedure\n    _this._pendingActions = [];\n    opts = opts || {};\n    // Get theme by name\n    if (isString(theme)) {\n      theme = themeStorage[theme];\n    }\n    _this._dom = dom;\n    var defaultRenderer = 'canvas';\n    var defaultCoarsePointer = 'auto';\n    var defaultUseDirtyRect = false;\n    if (process.env.NODE_ENV !== 'production') {\n      var root = /* eslint-disable-next-line */\n      env.hasGlobalWindow ? window : global;\n      if (root) {\n        defaultRenderer = retrieve2(root.__ECHARTS__DEFAULT__RENDERER__, defaultRenderer);\n        defaultCoarsePointer = retrieve2(root.__ECHARTS__DEFAULT__COARSE_POINTER, defaultCoarsePointer);\n        defaultUseDirtyRect = retrieve2(root.__ECHARTS__DEFAULT__USE_DIRTY_RECT__, defaultUseDirtyRect);\n      }\n    }\n    if (opts.ssr) {\n      zrender.registerSSRDataGetter(function (el) {\n        var ecData = getECData(el);\n        var dataIndex = ecData.dataIndex;\n        if (dataIndex == null) {\n          return;\n        }\n        var hashMap = createHashMap();\n        hashMap.set('series_index', ecData.seriesIndex);\n        hashMap.set('data_index', dataIndex);\n        ecData.ssrType && hashMap.set('ssr_type', ecData.ssrType);\n        return hashMap;\n      });\n    }\n    var zr = _this._zr = zrender.init(dom, {\n      renderer: opts.renderer || defaultRenderer,\n      devicePixelRatio: opts.devicePixelRatio,\n      width: opts.width,\n      height: opts.height,\n      ssr: opts.ssr,\n      useDirtyRect: retrieve2(opts.useDirtyRect, defaultUseDirtyRect),\n      useCoarsePointer: retrieve2(opts.useCoarsePointer, defaultCoarsePointer),\n      pointerSize: opts.pointerSize\n    });\n    _this._ssr = opts.ssr;\n    // Expect 60 fps.\n    _this._throttledZrFlush = throttle(bind(zr.flush, zr), 17);\n    theme = clone(theme);\n    theme && backwardCompat(theme, true);\n    _this._theme = theme;\n    _this._locale = createLocaleObject(opts.locale || SYSTEM_LANG);\n    _this._coordSysMgr = new CoordinateSystemManager();\n    var api = _this._api = createExtensionAPI(_this);\n    // Sort on demand\n    function prioritySortFunc(a, b) {\n      return a.__prio - b.__prio;\n    }\n    timsort(visualFuncs, prioritySortFunc);\n    timsort(dataProcessorFuncs, prioritySortFunc);\n    _this._scheduler = new Scheduler(_this, api, dataProcessorFuncs, visualFuncs);\n    _this._messageCenter = new MessageCenter();\n    // Init mouse events\n    _this._initEvents();\n    // In case some people write `window.onresize = chart.resize`\n    _this.resize = bind(_this.resize, _this);\n    zr.animation.on('frame', _this._onframe, _this);\n    bindRenderedEvent(zr, _this);\n    bindMouseEvent(zr, _this);\n    // ECharts instance can be used as value.\n    setAsPrimitive(_this);\n    return _this;\n  }\n  ECharts.prototype._onframe = function () {\n    if (this._disposed) {\n      return;\n    }\n    applyChangedStates(this);\n    var scheduler = this._scheduler;\n    // Lazy update\n    if (this[PENDING_UPDATE]) {\n      var silent = this[PENDING_UPDATE].silent;\n      this[IN_MAIN_PROCESS_KEY] = true;\n      try {\n        prepare(this);\n        updateMethods.update.call(this, null, this[PENDING_UPDATE].updateParams);\n      } catch (e) {\n        this[IN_MAIN_PROCESS_KEY] = false;\n        this[PENDING_UPDATE] = null;\n        throw e;\n      }\n      // At present, in each frame, zrender performs:\n      //   (1) animation step forward.\n      //   (2) trigger('frame') (where this `_onframe` is called)\n      //   (3) zrender flush (render).\n      // If we do nothing here, since we use `setToFinal: true`, the step (3) above\n      // will render the final state of the elements before the real animation started.\n      this._zr.flush();\n      this[IN_MAIN_PROCESS_KEY] = false;\n      this[PENDING_UPDATE] = null;\n      flushPendingActions.call(this, silent);\n      triggerUpdatedEvent.call(this, silent);\n    }\n    // Avoid do both lazy update and progress in one frame.\n    else if (scheduler.unfinished) {\n      // Stream progress.\n      var remainTime = TEST_FRAME_REMAIN_TIME;\n      var ecModel = this._model;\n      var api = this._api;\n      scheduler.unfinished = false;\n      do {\n        var startTime = +new Date();\n        scheduler.performSeriesTasks(ecModel);\n        // Currently dataProcessorFuncs do not check threshold.\n        scheduler.performDataProcessorTasks(ecModel);\n        updateStreamModes(this, ecModel);\n        // Do not update coordinate system here. Because that coord system update in\n        // each frame is not a good user experience. So we follow the rule that\n        // the extent of the coordinate system is determined in the first frame (the\n        // frame is executed immediately after task reset.\n        // this._coordSysMgr.update(ecModel, api);\n        // console.log('--- ec frame visual ---', remainTime);\n        scheduler.performVisualTasks(ecModel);\n        renderSeries(this, this._model, api, 'remain', {});\n        remainTime -= +new Date() - startTime;\n      } while (remainTime > 0 && scheduler.unfinished);\n      // Call flush explicitly for trigger finished event.\n      if (!scheduler.unfinished) {\n        this._zr.flush();\n      }\n      // Else, zr flushing be ensue within the same frame,\n      // because zr flushing is after onframe event.\n    }\n  };\n  ECharts.prototype.getDom = function () {\n    return this._dom;\n  };\n  ECharts.prototype.getId = function () {\n    return this.id;\n  };\n  ECharts.prototype.getZr = function () {\n    return this._zr;\n  };\n  ECharts.prototype.isSSR = function () {\n    return this._ssr;\n  };\n  /* eslint-disable-next-line */\n  ECharts.prototype.setOption = function (option, notMerge, lazyUpdate) {\n    if (this[IN_MAIN_PROCESS_KEY]) {\n      if (process.env.NODE_ENV !== 'production') {\n        error('`setOption` should not be called during main process.');\n      }\n      return;\n    }\n    if (this._disposed) {\n      disposedWarning(this.id);\n      return;\n    }\n    var silent;\n    var replaceMerge;\n    var transitionOpt;\n    if (isObject(notMerge)) {\n      lazyUpdate = notMerge.lazyUpdate;\n      silent = notMerge.silent;\n      replaceMerge = notMerge.replaceMerge;\n      transitionOpt = notMerge.transition;\n      notMerge = notMerge.notMerge;\n    }\n    this[IN_MAIN_PROCESS_KEY] = true;\n    if (!this._model || notMerge) {\n      var optionManager = new OptionManager(this._api);\n      var theme = this._theme;\n      var ecModel = this._model = new GlobalModel();\n      ecModel.scheduler = this._scheduler;\n      ecModel.ssr = this._ssr;\n      ecModel.init(null, null, null, theme, this._locale, optionManager);\n    }\n    this._model.setOption(option, {\n      replaceMerge: replaceMerge\n    }, optionPreprocessorFuncs);\n    var updateParams = {\n      seriesTransition: transitionOpt,\n      optionChanged: true\n    };\n    if (lazyUpdate) {\n      this[PENDING_UPDATE] = {\n        silent: silent,\n        updateParams: updateParams\n      };\n      this[IN_MAIN_PROCESS_KEY] = false;\n      // `setOption(option, {lazyMode: true})` may be called when zrender has been slept.\n      // It should wake it up to make sure zrender start to render at the next frame.\n      this.getZr().wakeUp();\n    } else {\n      try {\n        prepare(this);\n        updateMethods.update.call(this, null, updateParams);\n      } catch (e) {\n        this[PENDING_UPDATE] = null;\n        this[IN_MAIN_PROCESS_KEY] = false;\n        throw e;\n      }\n      // Ensure zr refresh sychronously, and then pixel in canvas can be\n      // fetched after `setOption`.\n      if (!this._ssr) {\n        // not use flush when using ssr mode.\n        this._zr.flush();\n      }\n      this[PENDING_UPDATE] = null;\n      this[IN_MAIN_PROCESS_KEY] = false;\n      flushPendingActions.call(this, silent);\n      triggerUpdatedEvent.call(this, silent);\n    }\n  };\n  /**\r\n   * @deprecated\r\n   */\n  ECharts.prototype.setTheme = function () {\n    deprecateLog('ECharts#setTheme() is DEPRECATED in ECharts 3.0');\n  };\n  // We don't want developers to use getModel directly.\n  ECharts.prototype.getModel = function () {\n    return this._model;\n  };\n  ECharts.prototype.getOption = function () {\n    return this._model && this._model.getOption();\n  };\n  ECharts.prototype.getWidth = function () {\n    return this._zr.getWidth();\n  };\n  ECharts.prototype.getHeight = function () {\n    return this._zr.getHeight();\n  };\n  ECharts.prototype.getDevicePixelRatio = function () {\n    return this._zr.painter.dpr\n    /* eslint-disable-next-line */ || env.hasGlobalWindow && window.devicePixelRatio || 1;\n  };\n  /**\r\n   * Get canvas which has all thing rendered\r\n   * @deprecated Use renderToCanvas instead.\r\n   */\n  ECharts.prototype.getRenderedCanvas = function (opts) {\n    if (process.env.NODE_ENV !== 'production') {\n      deprecateReplaceLog('getRenderedCanvas', 'renderToCanvas');\n    }\n    return this.renderToCanvas(opts);\n  };\n  ECharts.prototype.renderToCanvas = function (opts) {\n    opts = opts || {};\n    var painter = this._zr.painter;\n    if (process.env.NODE_ENV !== 'production') {\n      if (painter.type !== 'canvas') {\n        throw new Error('renderToCanvas can only be used in the canvas renderer.');\n      }\n    }\n    return painter.getRenderedCanvas({\n      backgroundColor: opts.backgroundColor || this._model.get('backgroundColor'),\n      pixelRatio: opts.pixelRatio || this.getDevicePixelRatio()\n    });\n  };\n  ECharts.prototype.renderToSVGString = function (opts) {\n    opts = opts || {};\n    var painter = this._zr.painter;\n    if (process.env.NODE_ENV !== 'production') {\n      if (painter.type !== 'svg') {\n        throw new Error('renderToSVGString can only be used in the svg renderer.');\n      }\n    }\n    return painter.renderToString({\n      useViewBox: opts.useViewBox\n    });\n  };\n  /**\r\n   * Get svg data url\r\n   */\n  ECharts.prototype.getSvgDataURL = function () {\n    if (!env.svgSupported) {\n      return;\n    }\n    var zr = this._zr;\n    var list = zr.storage.getDisplayList();\n    // Stop animations\n    each(list, function (el) {\n      el.stopAnimation(null, true);\n    });\n    return zr.painter.toDataURL();\n  };\n  ECharts.prototype.getDataURL = function (opts) {\n    if (this._disposed) {\n      disposedWarning(this.id);\n      return;\n    }\n    opts = opts || {};\n    var excludeComponents = opts.excludeComponents;\n    var ecModel = this._model;\n    var excludesComponentViews = [];\n    var self = this;\n    each(excludeComponents, function (componentType) {\n      ecModel.eachComponent({\n        mainType: componentType\n      }, function (component) {\n        var view = self._componentsMap[component.__viewId];\n        if (!view.group.ignore) {\n          excludesComponentViews.push(view);\n          view.group.ignore = true;\n        }\n      });\n    });\n    var url = this._zr.painter.getType() === 'svg' ? this.getSvgDataURL() : this.renderToCanvas(opts).toDataURL('image/' + (opts && opts.type || 'png'));\n    each(excludesComponentViews, function (view) {\n      view.group.ignore = false;\n    });\n    return url;\n  };\n  ECharts.prototype.getConnectedDataURL = function (opts) {\n    if (this._disposed) {\n      disposedWarning(this.id);\n      return;\n    }\n    var isSvg = opts.type === 'svg';\n    var groupId = this.group;\n    var mathMin = Math.min;\n    var mathMax = Math.max;\n    var MAX_NUMBER = Infinity;\n    if (connectedGroups[groupId]) {\n      var left_1 = MAX_NUMBER;\n      var top_1 = MAX_NUMBER;\n      var right_1 = -MAX_NUMBER;\n      var bottom_1 = -MAX_NUMBER;\n      var canvasList_1 = [];\n      var dpr_1 = opts && opts.pixelRatio || this.getDevicePixelRatio();\n      each(instances, function (chart, id) {\n        if (chart.group === groupId) {\n          var canvas = isSvg ? chart.getZr().painter.getSvgDom().innerHTML : chart.renderToCanvas(clone(opts));\n          var boundingRect = chart.getDom().getBoundingClientRect();\n          left_1 = mathMin(boundingRect.left, left_1);\n          top_1 = mathMin(boundingRect.top, top_1);\n          right_1 = mathMax(boundingRect.right, right_1);\n          bottom_1 = mathMax(boundingRect.bottom, bottom_1);\n          canvasList_1.push({\n            dom: canvas,\n            left: boundingRect.left,\n            top: boundingRect.top\n          });\n        }\n      });\n      left_1 *= dpr_1;\n      top_1 *= dpr_1;\n      right_1 *= dpr_1;\n      bottom_1 *= dpr_1;\n      var width = right_1 - left_1;\n      var height = bottom_1 - top_1;\n      var targetCanvas = platformApi.createCanvas();\n      var zr_1 = zrender.init(targetCanvas, {\n        renderer: isSvg ? 'svg' : 'canvas'\n      });\n      zr_1.resize({\n        width: width,\n        height: height\n      });\n      if (isSvg) {\n        var content_1 = '';\n        each(canvasList_1, function (item) {\n          var x = item.left - left_1;\n          var y = item.top - top_1;\n          content_1 += '<g transform=\"translate(' + x + ',' + y + ')\">' + item.dom + '</g>';\n        });\n        zr_1.painter.getSvgRoot().innerHTML = content_1;\n        if (opts.connectedBackgroundColor) {\n          zr_1.painter.setBackgroundColor(opts.connectedBackgroundColor);\n        }\n        zr_1.refreshImmediately();\n        return zr_1.painter.toDataURL();\n      } else {\n        // Background between the charts\n        if (opts.connectedBackgroundColor) {\n          zr_1.add(new graphic.Rect({\n            shape: {\n              x: 0,\n              y: 0,\n              width: width,\n              height: height\n            },\n            style: {\n              fill: opts.connectedBackgroundColor\n            }\n          }));\n        }\n        each(canvasList_1, function (item) {\n          var img = new graphic.Image({\n            style: {\n              x: item.left * dpr_1 - left_1,\n              y: item.top * dpr_1 - top_1,\n              image: item.dom\n            }\n          });\n          zr_1.add(img);\n        });\n        zr_1.refreshImmediately();\n        return targetCanvas.toDataURL('image/' + (opts && opts.type || 'png'));\n      }\n    } else {\n      return this.getDataURL(opts);\n    }\n  };\n  ECharts.prototype.convertToPixel = function (finder, value) {\n    return doConvertPixel(this, 'convertToPixel', finder, value);\n  };\n  ECharts.prototype.convertFromPixel = function (finder, value) {\n    return doConvertPixel(this, 'convertFromPixel', finder, value);\n  };\n  /**\r\n   * Is the specified coordinate systems or components contain the given pixel point.\r\n   * @param {Array|number} value\r\n   * @return {boolean} result\r\n   */\n  ECharts.prototype.containPixel = function (finder, value) {\n    if (this._disposed) {\n      disposedWarning(this.id);\n      return;\n    }\n    var ecModel = this._model;\n    var result;\n    var findResult = modelUtil.parseFinder(ecModel, finder);\n    each(findResult, function (models, key) {\n      key.indexOf('Models') >= 0 && each(models, function (model) {\n        var coordSys = model.coordinateSystem;\n        if (coordSys && coordSys.containPoint) {\n          result = result || !!coordSys.containPoint(value);\n        } else if (key === 'seriesModels') {\n          var view = this._chartsMap[model.__viewId];\n          if (view && view.containPoint) {\n            result = result || view.containPoint(value, model);\n          } else {\n            if (process.env.NODE_ENV !== 'production') {\n              warn(key + ': ' + (view ? 'The found component do not support containPoint.' : 'No view mapping to the found component.'));\n            }\n          }\n        } else {\n          if (process.env.NODE_ENV !== 'production') {\n            warn(key + ': containPoint is not supported');\n          }\n        }\n      }, this);\n    }, this);\n    return !!result;\n  };\n  /**\r\n   * Get visual from series or data.\r\n   * @param finder\r\n   *        If string, e.g., 'series', means {seriesIndex: 0}.\r\n   *        If Object, could contain some of these properties below:\r\n   *        {\r\n   *            seriesIndex / seriesId / seriesName,\r\n   *            dataIndex / dataIndexInside\r\n   *        }\r\n   *        If dataIndex is not specified, series visual will be fetched,\r\n   *        but not data item visual.\r\n   *        If all of seriesIndex, seriesId, seriesName are not specified,\r\n   *        visual will be fetched from first series.\r\n   * @param visualType 'color', 'symbol', 'symbolSize'\r\n   */\n  ECharts.prototype.getVisual = function (finder, visualType) {\n    var ecModel = this._model;\n    var parsedFinder = modelUtil.parseFinder(ecModel, finder, {\n      defaultMainType: 'series'\n    });\n    var seriesModel = parsedFinder.seriesModel;\n    if (process.env.NODE_ENV !== 'production') {\n      if (!seriesModel) {\n        warn('There is no specified series model');\n      }\n    }\n    var data = seriesModel.getData();\n    var dataIndexInside = parsedFinder.hasOwnProperty('dataIndexInside') ? parsedFinder.dataIndexInside : parsedFinder.hasOwnProperty('dataIndex') ? data.indexOfRawIndex(parsedFinder.dataIndex) : null;\n    return dataIndexInside != null ? getItemVisualFromData(data, dataIndexInside, visualType) : getVisualFromData(data, visualType);\n  };\n  /**\r\n   * Get view of corresponding component model\r\n   */\n  ECharts.prototype.getViewOfComponentModel = function (componentModel) {\n    return this._componentsMap[componentModel.__viewId];\n  };\n  /**\r\n   * Get view of corresponding series model\r\n   */\n  ECharts.prototype.getViewOfSeriesModel = function (seriesModel) {\n    return this._chartsMap[seriesModel.__viewId];\n  };\n  ECharts.prototype._initEvents = function () {\n    var _this = this;\n    each(MOUSE_EVENT_NAMES, function (eveName) {\n      var handler = function (e) {\n        var ecModel = _this.getModel();\n        var el = e.target;\n        var params;\n        var isGlobalOut = eveName === 'globalout';\n        // no e.target when 'globalout'.\n        if (isGlobalOut) {\n          params = {};\n        } else {\n          el && findEventDispatcher(el, function (parent) {\n            var ecData = getECData(parent);\n            if (ecData && ecData.dataIndex != null) {\n              var dataModel = ecData.dataModel || ecModel.getSeriesByIndex(ecData.seriesIndex);\n              params = dataModel && dataModel.getDataParams(ecData.dataIndex, ecData.dataType, el) || {};\n              return true;\n            }\n            // If element has custom eventData of components\n            else if (ecData.eventData) {\n              params = extend({}, ecData.eventData);\n              return true;\n            }\n          }, true);\n        }\n        // Contract: if params prepared in mouse event,\n        // these properties must be specified:\n        // {\n        //    componentType: string (component main type)\n        //    componentIndex: number\n        // }\n        // Otherwise event query can not work.\n        if (params) {\n          var componentType = params.componentType;\n          var componentIndex = params.componentIndex;\n          // Special handling for historic reason: when trigger by\n          // markLine/markPoint/markArea, the componentType is\n          // 'markLine'/'markPoint'/'markArea', but we should better\n          // enable them to be queried by seriesIndex, since their\n          // option is set in each series.\n          if (componentType === 'markLine' || componentType === 'markPoint' || componentType === 'markArea') {\n            componentType = 'series';\n            componentIndex = params.seriesIndex;\n          }\n          var model = componentType && componentIndex != null && ecModel.getComponent(componentType, componentIndex);\n          var view = model && _this[model.mainType === 'series' ? '_chartsMap' : '_componentsMap'][model.__viewId];\n          if (process.env.NODE_ENV !== 'production') {\n            // `event.componentType` and `event[componentTpype + 'Index']` must not\n            // be missed, otherwise there is no way to distinguish source component.\n            // See `dataFormat.getDataParams`.\n            if (!isGlobalOut && !(model && view)) {\n              warn('model or view can not be found by params');\n            }\n          }\n          params.event = e;\n          params.type = eveName;\n          _this._$eventProcessor.eventInfo = {\n            targetEl: el,\n            packedEvent: params,\n            model: model,\n            view: view\n          };\n          _this.trigger(eveName, params);\n        }\n      };\n      // Consider that some component (like tooltip, brush, ...)\n      // register zr event handler, but user event handler might\n      // do anything, such as call `setOption` or `dispatchAction`,\n      // which probably update any of the content and probably\n      // cause problem if it is called previous other inner handlers.\n      handler.zrEventfulCallAtLast = true;\n      _this._zr.on(eveName, handler, _this);\n    });\n    each(eventActionMap, function (actionType, eventType) {\n      _this._messageCenter.on(eventType, function (event) {\n        this.trigger(eventType, event);\n      }, _this);\n    });\n    // Extra events\n    // TODO register?\n    each(['selectchanged'], function (eventType) {\n      _this._messageCenter.on(eventType, function (event) {\n        this.trigger(eventType, event);\n      }, _this);\n    });\n    handleLegacySelectEvents(this._messageCenter, this, this._api);\n  };\n  ECharts.prototype.isDisposed = function () {\n    return this._disposed;\n  };\n  ECharts.prototype.clear = function () {\n    if (this._disposed) {\n      disposedWarning(this.id);\n      return;\n    }\n    this.setOption({\n      series: []\n    }, true);\n  };\n  ECharts.prototype.dispose = function () {\n    if (this._disposed) {\n      disposedWarning(this.id);\n      return;\n    }\n    this._disposed = true;\n    var dom = this.getDom();\n    if (dom) {\n      modelUtil.setAttribute(this.getDom(), DOM_ATTRIBUTE_KEY, '');\n    }\n    var chart = this;\n    var api = chart._api;\n    var ecModel = chart._model;\n    each(chart._componentsViews, function (component) {\n      component.dispose(ecModel, api);\n    });\n    each(chart._chartsViews, function (chart) {\n      chart.dispose(ecModel, api);\n    });\n    // Dispose after all views disposed\n    chart._zr.dispose();\n    // Set properties to null.\n    // To reduce the memory cost in case the top code still holds this instance unexpectedly.\n    chart._dom = chart._model = chart._chartsMap = chart._componentsMap = chart._chartsViews = chart._componentsViews = chart._scheduler = chart._api = chart._zr = chart._throttledZrFlush = chart._theme = chart._coordSysMgr = chart._messageCenter = null;\n    delete instances[chart.id];\n  };\n  /**\r\n   * Resize the chart\r\n   */\n  ECharts.prototype.resize = function (opts) {\n    if (this[IN_MAIN_PROCESS_KEY]) {\n      if (process.env.NODE_ENV !== 'production') {\n        error('`resize` should not be called during main process.');\n      }\n      return;\n    }\n    if (this._disposed) {\n      disposedWarning(this.id);\n      return;\n    }\n    this._zr.resize(opts);\n    var ecModel = this._model;\n    // Resize loading effect\n    this._loadingFX && this._loadingFX.resize();\n    if (!ecModel) {\n      return;\n    }\n    var needPrepare = ecModel.resetOption('media');\n    var silent = opts && opts.silent;\n    // There is some real cases that:\n    // chart.setOption(option, { lazyUpdate: true });\n    // chart.resize();\n    if (this[PENDING_UPDATE]) {\n      if (silent == null) {\n        silent = this[PENDING_UPDATE].silent;\n      }\n      needPrepare = true;\n      this[PENDING_UPDATE] = null;\n    }\n    this[IN_MAIN_PROCESS_KEY] = true;\n    try {\n      needPrepare && prepare(this);\n      updateMethods.update.call(this, {\n        type: 'resize',\n        animation: extend({\n          // Disable animation\n          duration: 0\n        }, opts && opts.animation)\n      });\n    } catch (e) {\n      this[IN_MAIN_PROCESS_KEY] = false;\n      throw e;\n    }\n    this[IN_MAIN_PROCESS_KEY] = false;\n    flushPendingActions.call(this, silent);\n    triggerUpdatedEvent.call(this, silent);\n  };\n  ECharts.prototype.showLoading = function (name, cfg) {\n    if (this._disposed) {\n      disposedWarning(this.id);\n      return;\n    }\n    if (isObject(name)) {\n      cfg = name;\n      name = '';\n    }\n    name = name || 'default';\n    this.hideLoading();\n    if (!loadingEffects[name]) {\n      if (process.env.NODE_ENV !== 'production') {\n        warn('Loading effects ' + name + ' not exists.');\n      }\n      return;\n    }\n    var el = loadingEffects[name](this._api, cfg);\n    var zr = this._zr;\n    this._loadingFX = el;\n    zr.add(el);\n  };\n  /**\r\n   * Hide loading effect\r\n   */\n  ECharts.prototype.hideLoading = function () {\n    if (this._disposed) {\n      disposedWarning(this.id);\n      return;\n    }\n    this._loadingFX && this._zr.remove(this._loadingFX);\n    this._loadingFX = null;\n  };\n  ECharts.prototype.makeActionFromEvent = function (eventObj) {\n    var payload = extend({}, eventObj);\n    payload.type = eventActionMap[eventObj.type];\n    return payload;\n  };\n  /**\r\n   * @param opt If pass boolean, means opt.silent\r\n   * @param opt.silent Default `false`. Whether trigger events.\r\n   * @param opt.flush Default `undefined`.\r\n   *        true: Flush immediately, and then pixel in canvas can be fetched\r\n   *            immediately. Caution: it might affect performance.\r\n   *        false: Not flush.\r\n   *        undefined: Auto decide whether perform flush.\r\n   */\n  ECharts.prototype.dispatchAction = function (payload, opt) {\n    if (this._disposed) {\n      disposedWarning(this.id);\n      return;\n    }\n    if (!isObject(opt)) {\n      opt = {\n        silent: !!opt\n      };\n    }\n    if (!actions[payload.type]) {\n      return;\n    }\n    // Avoid dispatch action before setOption. Especially in `connect`.\n    if (!this._model) {\n      return;\n    }\n    // May dispatchAction in rendering procedure\n    if (this[IN_MAIN_PROCESS_KEY]) {\n      this._pendingActions.push(payload);\n      return;\n    }\n    var silent = opt.silent;\n    doDispatchAction.call(this, payload, silent);\n    var flush = opt.flush;\n    if (flush) {\n      this._zr.flush();\n    } else if (flush !== false && env.browser.weChat) {\n      // In WeChat embedded browser, `requestAnimationFrame` and `setInterval`\n      // hang when sliding page (on touch event), which cause that zr does not\n      // refresh until user interaction finished, which is not expected.\n      // But `dispatchAction` may be called too frequently when pan on touch\n      // screen, which impacts performance if do not throttle them.\n      this._throttledZrFlush();\n    }\n    flushPendingActions.call(this, silent);\n    triggerUpdatedEvent.call(this, silent);\n  };\n  ECharts.prototype.updateLabelLayout = function () {\n    lifecycle.trigger('series:layoutlabels', this._model, this._api, {\n      // Not adding series labels.\n      // TODO\n      updatedSeries: []\n    });\n  };\n  ECharts.prototype.appendData = function (params) {\n    if (this._disposed) {\n      disposedWarning(this.id);\n      return;\n    }\n    var seriesIndex = params.seriesIndex;\n    var ecModel = this.getModel();\n    var seriesModel = ecModel.getSeriesByIndex(seriesIndex);\n    if (process.env.NODE_ENV !== 'production') {\n      assert(params.data && seriesModel);\n    }\n    seriesModel.appendData(params);\n    // Note: `appendData` does not support that update extent of coordinate\n    // system, util some scenario require that. In the expected usage of\n    // `appendData`, the initial extent of coordinate system should better\n    // be fixed by axis `min`/`max` setting or initial data, otherwise if\n    // the extent changed while `appendData`, the location of the painted\n    // graphic elements have to be changed, which make the usage of\n    // `appendData` meaningless.\n    this._scheduler.unfinished = true;\n    this.getZr().wakeUp();\n  };\n  // A work around for no `internal` modifier in ts yet but\n  // need to strictly hide private methods to JS users.\n  ECharts.internalField = function () {\n    prepare = function (ecIns) {\n      var scheduler = ecIns._scheduler;\n      scheduler.restorePipelines(ecIns._model);\n      scheduler.prepareStageTasks();\n      prepareView(ecIns, true);\n      prepareView(ecIns, false);\n      scheduler.plan();\n    };\n    /**\r\n     * Prepare view instances of charts and components\r\n     */\n    prepareView = function (ecIns, isComponent) {\n      var ecModel = ecIns._model;\n      var scheduler = ecIns._scheduler;\n      var viewList = isComponent ? ecIns._componentsViews : ecIns._chartsViews;\n      var viewMap = isComponent ? ecIns._componentsMap : ecIns._chartsMap;\n      var zr = ecIns._zr;\n      var api = ecIns._api;\n      for (var i = 0; i < viewList.length; i++) {\n        viewList[i].__alive = false;\n      }\n      isComponent ? ecModel.eachComponent(function (componentType, model) {\n        componentType !== 'series' && doPrepare(model);\n      }) : ecModel.eachSeries(doPrepare);\n      function doPrepare(model) {\n        // By default view will be reused if possible for the case that `setOption` with \"notMerge\"\n        // mode and need to enable transition animation. (Usually, when they have the same id, or\n        // especially no id but have the same type & name & index. See the `model.id` generation\n        // rule in `makeIdAndName` and `viewId` generation rule here).\n        // But in `replaceMerge` mode, this feature should be able to disabled when it is clear that\n        // the new model has nothing to do with the old model.\n        var requireNewView = model.__requireNewView;\n        // This command should not work twice.\n        model.__requireNewView = false;\n        // Consider: id same and type changed.\n        var viewId = '_ec_' + model.id + '_' + model.type;\n        var view = !requireNewView && viewMap[viewId];\n        if (!view) {\n          var classType = parseClassType(model.type);\n          var Clazz = isComponent ? ComponentView.getClass(classType.main, classType.sub) :\n          // FIXME:TS\n          // (ChartView as ChartViewConstructor).getClass('series', classType.sub)\n          // For backward compat, still support a chart type declared as only subType\n          // like \"liquidfill\", but recommend \"series.liquidfill\"\n          // But need a base class to make a type series.\n          ChartView.getClass(classType.sub);\n          if (process.env.NODE_ENV !== 'production') {\n            assert(Clazz, classType.sub + ' does not exist.');\n          }\n          view = new Clazz();\n          view.init(ecModel, api);\n          viewMap[viewId] = view;\n          viewList.push(view);\n          zr.add(view.group);\n        }\n        model.__viewId = view.__id = viewId;\n        view.__alive = true;\n        view.__model = model;\n        view.group.__ecComponentInfo = {\n          mainType: model.mainType,\n          index: model.componentIndex\n        };\n        !isComponent && scheduler.prepareView(view, model, ecModel, api);\n      }\n      for (var i = 0; i < viewList.length;) {\n        var view = viewList[i];\n        if (!view.__alive) {\n          !isComponent && view.renderTask.dispose();\n          zr.remove(view.group);\n          view.dispose(ecModel, api);\n          viewList.splice(i, 1);\n          if (viewMap[view.__id] === view) {\n            delete viewMap[view.__id];\n          }\n          view.__id = view.group.__ecComponentInfo = null;\n        } else {\n          i++;\n        }\n      }\n    };\n    updateDirectly = function (ecIns, method, payload, mainType, subType) {\n      var ecModel = ecIns._model;\n      ecModel.setUpdatePayload(payload);\n      // broadcast\n      if (!mainType) {\n        // FIXME\n        // Chart will not be update directly here, except set dirty.\n        // But there is no such scenario now.\n        each([].concat(ecIns._componentsViews).concat(ecIns._chartsViews), callView);\n        return;\n      }\n      var query = {};\n      query[mainType + 'Id'] = payload[mainType + 'Id'];\n      query[mainType + 'Index'] = payload[mainType + 'Index'];\n      query[mainType + 'Name'] = payload[mainType + 'Name'];\n      var condition = {\n        mainType: mainType,\n        query: query\n      };\n      subType && (condition.subType = subType); // subType may be '' by parseClassType;\n      var excludeSeriesId = payload.excludeSeriesId;\n      var excludeSeriesIdMap;\n      if (excludeSeriesId != null) {\n        excludeSeriesIdMap = createHashMap();\n        each(modelUtil.normalizeToArray(excludeSeriesId), function (id) {\n          var modelId = modelUtil.convertOptionIdName(id, null);\n          if (modelId != null) {\n            excludeSeriesIdMap.set(modelId, true);\n          }\n        });\n      }\n      // If dispatchAction before setOption, do nothing.\n      ecModel && ecModel.eachComponent(condition, function (model) {\n        var isExcluded = excludeSeriesIdMap && excludeSeriesIdMap.get(model.id) != null;\n        if (isExcluded) {\n          return;\n        }\n        ;\n        if (isHighDownPayload(payload)) {\n          if (model instanceof SeriesModel) {\n            if (payload.type === HIGHLIGHT_ACTION_TYPE && !payload.notBlur && !model.get(['emphasis', 'disabled'])) {\n              blurSeriesFromHighlightPayload(model, payload, ecIns._api);\n            }\n          } else {\n            var _a = findComponentHighDownDispatchers(model.mainType, model.componentIndex, payload.name, ecIns._api),\n              focusSelf = _a.focusSelf,\n              dispatchers = _a.dispatchers;\n            if (payload.type === HIGHLIGHT_ACTION_TYPE && focusSelf && !payload.notBlur) {\n              blurComponent(model.mainType, model.componentIndex, ecIns._api);\n            }\n            // PENDING:\n            // Whether to put this \"enter emphasis\" code in `ComponentView`,\n            // which will be the same as `ChartView` but might be not necessary\n            // and will be far from this logic.\n            if (dispatchers) {\n              each(dispatchers, function (dispatcher) {\n                payload.type === HIGHLIGHT_ACTION_TYPE ? enterEmphasis(dispatcher) : leaveEmphasis(dispatcher);\n              });\n            }\n          }\n        } else if (isSelectChangePayload(payload)) {\n          // TODO geo\n          if (model instanceof SeriesModel) {\n            toggleSelectionFromPayload(model, payload, ecIns._api);\n            updateSeriesElementSelection(model);\n            markStatusToUpdate(ecIns);\n          }\n        }\n      }, ecIns);\n      ecModel && ecModel.eachComponent(condition, function (model) {\n        var isExcluded = excludeSeriesIdMap && excludeSeriesIdMap.get(model.id) != null;\n        if (isExcluded) {\n          return;\n        }\n        ;\n        callView(ecIns[mainType === 'series' ? '_chartsMap' : '_componentsMap'][model.__viewId]);\n      }, ecIns);\n      function callView(view) {\n        view && view.__alive && view[method] && view[method](view.__model, ecModel, ecIns._api, payload);\n      }\n    };\n    updateMethods = {\n      prepareAndUpdate: function (payload) {\n        prepare(this);\n        updateMethods.update.call(this, payload, {\n          // Needs to mark option changed if newOption is given.\n          // It's from MagicType.\n          // TODO If use a separate flag optionChanged in payload?\n          optionChanged: payload.newOption != null\n        });\n      },\n      update: function (payload, updateParams) {\n        var ecModel = this._model;\n        var api = this._api;\n        var zr = this._zr;\n        var coordSysMgr = this._coordSysMgr;\n        var scheduler = this._scheduler;\n        // update before setOption\n        if (!ecModel) {\n          return;\n        }\n        ecModel.setUpdatePayload(payload);\n        scheduler.restoreData(ecModel, payload);\n        scheduler.performSeriesTasks(ecModel);\n        // TODO\n        // Save total ecModel here for undo/redo (after restoring data and before processing data).\n        // Undo (restoration of total ecModel) can be carried out in 'action' or outside API call.\n        // Create new coordinate system each update\n        // In LineView may save the old coordinate system and use it to get the original point.\n        coordSysMgr.create(ecModel, api);\n        scheduler.performDataProcessorTasks(ecModel, payload);\n        // Current stream render is not supported in data process. So we can update\n        // stream modes after data processing, where the filtered data is used to\n        // determine whether to use progressive rendering.\n        updateStreamModes(this, ecModel);\n        // We update stream modes before coordinate system updated, then the modes info\n        // can be fetched when coord sys updating (consider the barGrid extent fix). But\n        // the drawback is the full coord info can not be fetched. Fortunately this full\n        // coord is not required in stream mode updater currently.\n        coordSysMgr.update(ecModel, api);\n        clearColorPalette(ecModel);\n        scheduler.performVisualTasks(ecModel, payload);\n        render(this, ecModel, api, payload, updateParams);\n        // Set background\n        var backgroundColor = ecModel.get('backgroundColor') || 'transparent';\n        var darkMode = ecModel.get('darkMode');\n        zr.setBackgroundColor(backgroundColor);\n        // Force set dark mode.\n        if (darkMode != null && darkMode !== 'auto') {\n          zr.setDarkMode(darkMode);\n        }\n        lifecycle.trigger('afterupdate', ecModel, api);\n      },\n      updateTransform: function (payload) {\n        var _this = this;\n        var ecModel = this._model;\n        var api = this._api;\n        // update before setOption\n        if (!ecModel) {\n          return;\n        }\n        ecModel.setUpdatePayload(payload);\n        // ChartView.markUpdateMethod(payload, 'updateTransform');\n        var componentDirtyList = [];\n        ecModel.eachComponent(function (componentType, componentModel) {\n          if (componentType === 'series') {\n            return;\n          }\n          var componentView = _this.getViewOfComponentModel(componentModel);\n          if (componentView && componentView.__alive) {\n            if (componentView.updateTransform) {\n              var result = componentView.updateTransform(componentModel, ecModel, api, payload);\n              result && result.update && componentDirtyList.push(componentView);\n            } else {\n              componentDirtyList.push(componentView);\n            }\n          }\n        });\n        var seriesDirtyMap = createHashMap();\n        ecModel.eachSeries(function (seriesModel) {\n          var chartView = _this._chartsMap[seriesModel.__viewId];\n          if (chartView.updateTransform) {\n            var result = chartView.updateTransform(seriesModel, ecModel, api, payload);\n            result && result.update && seriesDirtyMap.set(seriesModel.uid, 1);\n          } else {\n            seriesDirtyMap.set(seriesModel.uid, 1);\n          }\n        });\n        clearColorPalette(ecModel);\n        // Keep pipe to the exist pipeline because it depends on the render task of the full pipeline.\n        // this._scheduler.performVisualTasks(ecModel, payload, 'layout', true);\n        this._scheduler.performVisualTasks(ecModel, payload, {\n          setDirty: true,\n          dirtyMap: seriesDirtyMap\n        });\n        // Currently, not call render of components. Geo render cost a lot.\n        // renderComponents(ecIns, ecModel, api, payload, componentDirtyList);\n        renderSeries(this, ecModel, api, payload, {}, seriesDirtyMap);\n        lifecycle.trigger('afterupdate', ecModel, api);\n      },\n      updateView: function (payload) {\n        var ecModel = this._model;\n        // update before setOption\n        if (!ecModel) {\n          return;\n        }\n        ecModel.setUpdatePayload(payload);\n        ChartView.markUpdateMethod(payload, 'updateView');\n        clearColorPalette(ecModel);\n        // Keep pipe to the exist pipeline because it depends on the render task of the full pipeline.\n        this._scheduler.performVisualTasks(ecModel, payload, {\n          setDirty: true\n        });\n        render(this, ecModel, this._api, payload, {});\n        lifecycle.trigger('afterupdate', ecModel, this._api);\n      },\n      updateVisual: function (payload) {\n        // updateMethods.update.call(this, payload);\n        var _this = this;\n        var ecModel = this._model;\n        // update before setOption\n        if (!ecModel) {\n          return;\n        }\n        ecModel.setUpdatePayload(payload);\n        // clear all visual\n        ecModel.eachSeries(function (seriesModel) {\n          seriesModel.getData().clearAllVisual();\n        });\n        // Perform visual\n        ChartView.markUpdateMethod(payload, 'updateVisual');\n        clearColorPalette(ecModel);\n        // Keep pipe to the exist pipeline because it depends on the render task of the full pipeline.\n        this._scheduler.performVisualTasks(ecModel, payload, {\n          visualType: 'visual',\n          setDirty: true\n        });\n        ecModel.eachComponent(function (componentType, componentModel) {\n          if (componentType !== 'series') {\n            var componentView = _this.getViewOfComponentModel(componentModel);\n            componentView && componentView.__alive && componentView.updateVisual(componentModel, ecModel, _this._api, payload);\n          }\n        });\n        ecModel.eachSeries(function (seriesModel) {\n          var chartView = _this._chartsMap[seriesModel.__viewId];\n          chartView.updateVisual(seriesModel, ecModel, _this._api, payload);\n        });\n        lifecycle.trigger('afterupdate', ecModel, this._api);\n      },\n      updateLayout: function (payload) {\n        updateMethods.update.call(this, payload);\n      }\n    };\n    doConvertPixel = function (ecIns, methodName, finder, value) {\n      if (ecIns._disposed) {\n        disposedWarning(ecIns.id);\n        return;\n      }\n      var ecModel = ecIns._model;\n      var coordSysList = ecIns._coordSysMgr.getCoordinateSystems();\n      var result;\n      var parsedFinder = modelUtil.parseFinder(ecModel, finder);\n      for (var i = 0; i < coordSysList.length; i++) {\n        var coordSys = coordSysList[i];\n        if (coordSys[methodName] && (result = coordSys[methodName](ecModel, parsedFinder, value)) != null) {\n          return result;\n        }\n      }\n      if (process.env.NODE_ENV !== 'production') {\n        warn('No coordinate system that supports ' + methodName + ' found by the given finder.');\n      }\n    };\n    updateStreamModes = function (ecIns, ecModel) {\n      var chartsMap = ecIns._chartsMap;\n      var scheduler = ecIns._scheduler;\n      ecModel.eachSeries(function (seriesModel) {\n        scheduler.updateStreamModes(seriesModel, chartsMap[seriesModel.__viewId]);\n      });\n    };\n    doDispatchAction = function (payload, silent) {\n      var _this = this;\n      var ecModel = this.getModel();\n      var payloadType = payload.type;\n      var escapeConnect = payload.escapeConnect;\n      var actionWrap = actions[payloadType];\n      var actionInfo = actionWrap.actionInfo;\n      var cptTypeTmp = (actionInfo.update || 'update').split(':');\n      var updateMethod = cptTypeTmp.pop();\n      var cptType = cptTypeTmp[0] != null && parseClassType(cptTypeTmp[0]);\n      this[IN_MAIN_PROCESS_KEY] = true;\n      var payloads = [payload];\n      var batched = false;\n      // Batch action\n      if (payload.batch) {\n        batched = true;\n        payloads = map(payload.batch, function (item) {\n          item = defaults(extend({}, item), payload);\n          item.batch = null;\n          return item;\n        });\n      }\n      var eventObjBatch = [];\n      var eventObj;\n      var isSelectChange = isSelectChangePayload(payload);\n      var isHighDown = isHighDownPayload(payload);\n      // Only leave blur once if there are multiple batches.\n      if (isHighDown) {\n        allLeaveBlur(this._api);\n      }\n      each(payloads, function (batchItem) {\n        // Action can specify the event by return it.\n        eventObj = actionWrap.action(batchItem, _this._model, _this._api);\n        // Emit event outside\n        eventObj = eventObj || extend({}, batchItem);\n        // Convert type to eventType\n        eventObj.type = actionInfo.event || eventObj.type;\n        eventObjBatch.push(eventObj);\n        // light update does not perform data process, layout and visual.\n        if (isHighDown) {\n          var _a = modelUtil.preParseFinder(payload),\n            queryOptionMap = _a.queryOptionMap,\n            mainTypeSpecified = _a.mainTypeSpecified;\n          var componentMainType = mainTypeSpecified ? queryOptionMap.keys()[0] : 'series';\n          updateDirectly(_this, updateMethod, batchItem, componentMainType);\n          markStatusToUpdate(_this);\n        } else if (isSelectChange) {\n          // At present `dispatchAction({ type: 'select', ... })` is not supported on components.\n          // geo still use 'geoselect'.\n          updateDirectly(_this, updateMethod, batchItem, 'series');\n          markStatusToUpdate(_this);\n        } else if (cptType) {\n          updateDirectly(_this, updateMethod, batchItem, cptType.main, cptType.sub);\n        }\n      });\n      if (updateMethod !== 'none' && !isHighDown && !isSelectChange && !cptType) {\n        try {\n          // Still dirty\n          if (this[PENDING_UPDATE]) {\n            prepare(this);\n            updateMethods.update.call(this, payload);\n            this[PENDING_UPDATE] = null;\n          } else {\n            updateMethods[updateMethod].call(this, payload);\n          }\n        } catch (e) {\n          this[IN_MAIN_PROCESS_KEY] = false;\n          throw e;\n        }\n      }\n      // Follow the rule of action batch\n      if (batched) {\n        eventObj = {\n          type: actionInfo.event || payloadType,\n          escapeConnect: escapeConnect,\n          batch: eventObjBatch\n        };\n      } else {\n        eventObj = eventObjBatch[0];\n      }\n      this[IN_MAIN_PROCESS_KEY] = false;\n      if (!silent) {\n        var messageCenter = this._messageCenter;\n        messageCenter.trigger(eventObj.type, eventObj);\n        // Extra triggered 'selectchanged' event\n        if (isSelectChange) {\n          var newObj = {\n            type: 'selectchanged',\n            escapeConnect: escapeConnect,\n            selected: getAllSelectedIndices(ecModel),\n            isFromClick: payload.isFromClick || false,\n            fromAction: payload.type,\n            fromActionPayload: payload\n          };\n          messageCenter.trigger(newObj.type, newObj);\n        }\n      }\n    };\n    flushPendingActions = function (silent) {\n      var pendingActions = this._pendingActions;\n      while (pendingActions.length) {\n        var payload = pendingActions.shift();\n        doDispatchAction.call(this, payload, silent);\n      }\n    };\n    triggerUpdatedEvent = function (silent) {\n      !silent && this.trigger('updated');\n    };\n    /**\r\n     * Event `rendered` is triggered when zr\r\n     * rendered. It is useful for realtime\r\n     * snapshot (reflect animation).\r\n     *\r\n     * Event `finished` is triggered when:\r\n     * (1) zrender rendering finished.\r\n     * (2) initial animation finished.\r\n     * (3) progressive rendering finished.\r\n     * (4) no pending action.\r\n     * (5) no delayed setOption needs to be processed.\r\n     */\n    bindRenderedEvent = function (zr, ecIns) {\n      zr.on('rendered', function (params) {\n        ecIns.trigger('rendered', params);\n        // The `finished` event should not be triggered repeatedly,\n        // so it should only be triggered when rendering indeed happens\n        // in zrender. (Consider the case that dipatchAction is keep\n        // triggering when mouse move).\n        if (\n        // Although zr is dirty if initial animation is not finished\n        // and this checking is called on frame, we also check\n        // animation finished for robustness.\n        zr.animation.isFinished() && !ecIns[PENDING_UPDATE] && !ecIns._scheduler.unfinished && !ecIns._pendingActions.length) {\n          ecIns.trigger('finished');\n        }\n      });\n    };\n    bindMouseEvent = function (zr, ecIns) {\n      zr.on('mouseover', function (e) {\n        var el = e.target;\n        var dispatcher = findEventDispatcher(el, isHighDownDispatcher);\n        if (dispatcher) {\n          handleGlobalMouseOverForHighDown(dispatcher, e, ecIns._api);\n          markStatusToUpdate(ecIns);\n        }\n      }).on('mouseout', function (e) {\n        var el = e.target;\n        var dispatcher = findEventDispatcher(el, isHighDownDispatcher);\n        if (dispatcher) {\n          handleGlobalMouseOutForHighDown(dispatcher, e, ecIns._api);\n          markStatusToUpdate(ecIns);\n        }\n      }).on('click', function (e) {\n        var el = e.target;\n        var dispatcher = findEventDispatcher(el, function (target) {\n          return getECData(target).dataIndex != null;\n        }, true);\n        if (dispatcher) {\n          var actionType = dispatcher.selected ? 'unselect' : 'select';\n          var ecData = getECData(dispatcher);\n          ecIns._api.dispatchAction({\n            type: actionType,\n            dataType: ecData.dataType,\n            dataIndexInside: ecData.dataIndex,\n            seriesIndex: ecData.seriesIndex,\n            isFromClick: true\n          });\n        }\n      });\n    };\n    function clearColorPalette(ecModel) {\n      ecModel.clearColorPalette();\n      ecModel.eachSeries(function (seriesModel) {\n        seriesModel.clearColorPalette();\n      });\n    }\n    ;\n    // Allocate zlevels for series and components\n    function allocateZlevels(ecModel) {\n      ;\n      var componentZLevels = [];\n      var seriesZLevels = [];\n      var hasSeparateZLevel = false;\n      ecModel.eachComponent(function (componentType, componentModel) {\n        var zlevel = componentModel.get('zlevel') || 0;\n        var z = componentModel.get('z') || 0;\n        var zlevelKey = componentModel.getZLevelKey();\n        hasSeparateZLevel = hasSeparateZLevel || !!zlevelKey;\n        (componentType === 'series' ? seriesZLevels : componentZLevels).push({\n          zlevel: zlevel,\n          z: z,\n          idx: componentModel.componentIndex,\n          type: componentType,\n          key: zlevelKey\n        });\n      });\n      if (hasSeparateZLevel) {\n        // Series after component\n        var zLevels = componentZLevels.concat(seriesZLevels);\n        var lastSeriesZLevel_1;\n        var lastSeriesKey_1;\n        timsort(zLevels, function (a, b) {\n          if (a.zlevel === b.zlevel) {\n            return a.z - b.z;\n          }\n          return a.zlevel - b.zlevel;\n        });\n        each(zLevels, function (item) {\n          var componentModel = ecModel.getComponent(item.type, item.idx);\n          var zlevel = item.zlevel;\n          var key = item.key;\n          if (lastSeriesZLevel_1 != null) {\n            zlevel = Math.max(lastSeriesZLevel_1, zlevel);\n          }\n          if (key) {\n            if (zlevel === lastSeriesZLevel_1 && key !== lastSeriesKey_1) {\n              zlevel++;\n            }\n            lastSeriesKey_1 = key;\n          } else if (lastSeriesKey_1) {\n            if (zlevel === lastSeriesZLevel_1) {\n              zlevel++;\n            }\n            lastSeriesKey_1 = '';\n          }\n          lastSeriesZLevel_1 = zlevel;\n          componentModel.setZLevel(zlevel);\n        });\n      }\n    }\n    render = function (ecIns, ecModel, api, payload, updateParams) {\n      allocateZlevels(ecModel);\n      renderComponents(ecIns, ecModel, api, payload, updateParams);\n      each(ecIns._chartsViews, function (chart) {\n        chart.__alive = false;\n      });\n      renderSeries(ecIns, ecModel, api, payload, updateParams);\n      // Remove groups of unrendered charts\n      each(ecIns._chartsViews, function (chart) {\n        if (!chart.__alive) {\n          chart.remove(ecModel, api);\n        }\n      });\n    };\n    renderComponents = function (ecIns, ecModel, api, payload, updateParams, dirtyList) {\n      each(dirtyList || ecIns._componentsViews, function (componentView) {\n        var componentModel = componentView.__model;\n        clearStates(componentModel, componentView);\n        componentView.render(componentModel, ecModel, api, payload);\n        updateZ(componentModel, componentView);\n        updateStates(componentModel, componentView);\n      });\n    };\n    /**\r\n     * Render each chart and component\r\n     */\n    renderSeries = function (ecIns, ecModel, api, payload, updateParams, dirtyMap) {\n      // Render all charts\n      var scheduler = ecIns._scheduler;\n      updateParams = extend(updateParams || {}, {\n        updatedSeries: ecModel.getSeries()\n      });\n      // TODO progressive?\n      lifecycle.trigger('series:beforeupdate', ecModel, api, updateParams);\n      var unfinished = false;\n      ecModel.eachSeries(function (seriesModel) {\n        var chartView = ecIns._chartsMap[seriesModel.__viewId];\n        chartView.__alive = true;\n        var renderTask = chartView.renderTask;\n        scheduler.updatePayload(renderTask, payload);\n        // TODO states on marker.\n        clearStates(seriesModel, chartView);\n        if (dirtyMap && dirtyMap.get(seriesModel.uid)) {\n          renderTask.dirty();\n        }\n        if (renderTask.perform(scheduler.getPerformArgs(renderTask))) {\n          unfinished = true;\n        }\n        chartView.group.silent = !!seriesModel.get('silent');\n        // Should not call markRedraw on group, because it will disable zrender\n        // incremental render (always render from the __startIndex each frame)\n        // chartView.group.markRedraw();\n        updateBlend(seriesModel, chartView);\n        updateSeriesElementSelection(seriesModel);\n      });\n      scheduler.unfinished = unfinished || scheduler.unfinished;\n      lifecycle.trigger('series:layoutlabels', ecModel, api, updateParams);\n      // transition after label is layouted.\n      lifecycle.trigger('series:transition', ecModel, api, updateParams);\n      ecModel.eachSeries(function (seriesModel) {\n        var chartView = ecIns._chartsMap[seriesModel.__viewId];\n        // Update Z after labels updated. Before applying states.\n        updateZ(seriesModel, chartView);\n        // NOTE: Update states after label is updated.\n        // label should be in normal status when layouting.\n        updateStates(seriesModel, chartView);\n      });\n      // If use hover layer\n      updateHoverLayerStatus(ecIns, ecModel);\n      lifecycle.trigger('series:afterupdate', ecModel, api, updateParams);\n    };\n    markStatusToUpdate = function (ecIns) {\n      ecIns[STATUS_NEEDS_UPDATE_KEY] = true;\n      // Wake up zrender if it's sleep. Let it update states in the next frame.\n      ecIns.getZr().wakeUp();\n    };\n    applyChangedStates = function (ecIns) {\n      if (!ecIns[STATUS_NEEDS_UPDATE_KEY]) {\n        return;\n      }\n      ecIns.getZr().storage.traverse(function (el) {\n        // Not applied on removed elements, it may still in fading.\n        if (graphic.isElementRemoved(el)) {\n          return;\n        }\n        applyElementStates(el);\n      });\n      ecIns[STATUS_NEEDS_UPDATE_KEY] = false;\n    };\n    function applyElementStates(el) {\n      var newStates = [];\n      var oldStates = el.currentStates;\n      // Keep other states.\n      for (var i = 0; i < oldStates.length; i++) {\n        var stateName = oldStates[i];\n        if (!(stateName === 'emphasis' || stateName === 'blur' || stateName === 'select')) {\n          newStates.push(stateName);\n        }\n      }\n      // Only use states when it's exists.\n      if (el.selected && el.states.select) {\n        newStates.push('select');\n      }\n      if (el.hoverState === HOVER_STATE_EMPHASIS && el.states.emphasis) {\n        newStates.push('emphasis');\n      } else if (el.hoverState === HOVER_STATE_BLUR && el.states.blur) {\n        newStates.push('blur');\n      }\n      el.useStates(newStates);\n    }\n    function updateHoverLayerStatus(ecIns, ecModel) {\n      var zr = ecIns._zr;\n      var storage = zr.storage;\n      var elCount = 0;\n      storage.traverse(function (el) {\n        if (!el.isGroup) {\n          elCount++;\n        }\n      });\n      if (elCount > ecModel.get('hoverLayerThreshold') && !env.node && !env.worker) {\n        ecModel.eachSeries(function (seriesModel) {\n          if (seriesModel.preventUsingHoverLayer) {\n            return;\n          }\n          var chartView = ecIns._chartsMap[seriesModel.__viewId];\n          if (chartView.__alive) {\n            chartView.eachRendered(function (el) {\n              if (el.states.emphasis) {\n                el.states.emphasis.hoverLayer = true;\n              }\n            });\n          }\n        });\n      }\n    }\n    ;\n    /**\r\n     * Update chart and blend.\r\n     */\n    function updateBlend(seriesModel, chartView) {\n      var blendMode = seriesModel.get('blendMode') || null;\n      chartView.eachRendered(function (el) {\n        // FIXME marker and other components\n        if (!el.isGroup) {\n          // DON'T mark the element dirty. In case element is incremental and don't want to rerender.\n          el.style.blend = blendMode;\n        }\n      });\n    }\n    ;\n    function updateZ(model, view) {\n      if (model.preventAutoZ) {\n        return;\n      }\n      var z = model.get('z') || 0;\n      var zlevel = model.get('zlevel') || 0;\n      // Set z and zlevel\n      view.eachRendered(function (el) {\n        doUpdateZ(el, z, zlevel, -Infinity);\n        // Don't traverse the children because it has been traversed in _updateZ.\n        return true;\n      });\n    }\n    ;\n    function doUpdateZ(el, z, zlevel, maxZ2) {\n      // Group may also have textContent\n      var label = el.getTextContent();\n      var labelLine = el.getTextGuideLine();\n      var isGroup = el.isGroup;\n      if (isGroup) {\n        // set z & zlevel of children elements of Group\n        var children = el.childrenRef();\n        for (var i = 0; i < children.length; i++) {\n          maxZ2 = Math.max(doUpdateZ(children[i], z, zlevel, maxZ2), maxZ2);\n        }\n      } else {\n        // not Group\n        el.z = z;\n        el.zlevel = zlevel;\n        maxZ2 = Math.max(el.z2, maxZ2);\n      }\n      // always set z and zlevel if label/labelLine exists\n      if (label) {\n        label.z = z;\n        label.zlevel = zlevel;\n        // lift z2 of text content\n        // TODO if el.emphasis.z2 is spcefied, what about textContent.\n        isFinite(maxZ2) && (label.z2 = maxZ2 + 2);\n      }\n      if (labelLine) {\n        var textGuideLineConfig = el.textGuideLineConfig;\n        labelLine.z = z;\n        labelLine.zlevel = zlevel;\n        isFinite(maxZ2) && (labelLine.z2 = maxZ2 + (textGuideLineConfig && textGuideLineConfig.showAbove ? 1 : -1));\n      }\n      return maxZ2;\n    }\n    // Clear states without animation.\n    // TODO States on component.\n    function clearStates(model, view) {\n      view.eachRendered(function (el) {\n        // Not applied on removed elements, it may still in fading.\n        if (graphic.isElementRemoved(el)) {\n          return;\n        }\n        var textContent = el.getTextContent();\n        var textGuide = el.getTextGuideLine();\n        if (el.stateTransition) {\n          el.stateTransition = null;\n        }\n        if (textContent && textContent.stateTransition) {\n          textContent.stateTransition = null;\n        }\n        if (textGuide && textGuide.stateTransition) {\n          textGuide.stateTransition = null;\n        }\n        // TODO If el is incremental.\n        if (el.hasState()) {\n          el.prevStates = el.currentStates;\n          el.clearStates();\n        } else if (el.prevStates) {\n          el.prevStates = null;\n        }\n      });\n    }\n    function updateStates(model, view) {\n      var stateAnimationModel = model.getModel('stateAnimation');\n      var enableAnimation = model.isAnimationEnabled();\n      var duration = stateAnimationModel.get('duration');\n      var stateTransition = duration > 0 ? {\n        duration: duration,\n        delay: stateAnimationModel.get('delay'),\n        easing: stateAnimationModel.get('easing')\n        // additive: stateAnimationModel.get('additive')\n      } : null;\n      view.eachRendered(function (el) {\n        if (el.states && el.states.emphasis) {\n          // Not applied on removed elements, it may still in fading.\n          if (graphic.isElementRemoved(el)) {\n            return;\n          }\n          if (el instanceof graphic.Path) {\n            savePathStates(el);\n          }\n          // Only updated on changed element. In case element is incremental and don't want to rerender.\n          // TODO, a more proper way?\n          if (el.__dirty) {\n            var prevStates = el.prevStates;\n            // Restore states without animation\n            if (prevStates) {\n              el.useStates(prevStates);\n            }\n          }\n          // Update state transition and enable animation again.\n          if (enableAnimation) {\n            el.stateTransition = stateTransition;\n            var textContent = el.getTextContent();\n            var textGuide = el.getTextGuideLine();\n            // TODO Is it necessary to animate label?\n            if (textContent) {\n              textContent.stateTransition = stateTransition;\n            }\n            if (textGuide) {\n              textGuide.stateTransition = stateTransition;\n            }\n          }\n          // Use highlighted and selected flag to toggle states.\n          if (el.__dirty) {\n            applyElementStates(el);\n          }\n        }\n      });\n    }\n    ;\n    createExtensionAPI = function (ecIns) {\n      return new (/** @class */function (_super) {\n        __extends(class_1, _super);\n        function class_1() {\n          return _super !== null && _super.apply(this, arguments) || this;\n        }\n        class_1.prototype.getCoordinateSystems = function () {\n          return ecIns._coordSysMgr.getCoordinateSystems();\n        };\n        class_1.prototype.getComponentByElement = function (el) {\n          while (el) {\n            var modelInfo = el.__ecComponentInfo;\n            if (modelInfo != null) {\n              return ecIns._model.getComponent(modelInfo.mainType, modelInfo.index);\n            }\n            el = el.parent;\n          }\n        };\n        class_1.prototype.enterEmphasis = function (el, highlightDigit) {\n          enterEmphasis(el, highlightDigit);\n          markStatusToUpdate(ecIns);\n        };\n        class_1.prototype.leaveEmphasis = function (el, highlightDigit) {\n          leaveEmphasis(el, highlightDigit);\n          markStatusToUpdate(ecIns);\n        };\n        class_1.prototype.enterBlur = function (el) {\n          enterBlur(el);\n          markStatusToUpdate(ecIns);\n        };\n        class_1.prototype.leaveBlur = function (el) {\n          leaveBlur(el);\n          markStatusToUpdate(ecIns);\n        };\n        class_1.prototype.enterSelect = function (el) {\n          enterSelect(el);\n          markStatusToUpdate(ecIns);\n        };\n        class_1.prototype.leaveSelect = function (el) {\n          leaveSelect(el);\n          markStatusToUpdate(ecIns);\n        };\n        class_1.prototype.getModel = function () {\n          return ecIns.getModel();\n        };\n        class_1.prototype.getViewOfComponentModel = function (componentModel) {\n          return ecIns.getViewOfComponentModel(componentModel);\n        };\n        class_1.prototype.getViewOfSeriesModel = function (seriesModel) {\n          return ecIns.getViewOfSeriesModel(seriesModel);\n        };\n        return class_1;\n      }(ExtensionAPI))(ecIns);\n    };\n    enableConnect = function (chart) {\n      function updateConnectedChartsStatus(charts, status) {\n        for (var i = 0; i < charts.length; i++) {\n          var otherChart = charts[i];\n          otherChart[CONNECT_STATUS_KEY] = status;\n        }\n      }\n      each(eventActionMap, function (actionType, eventType) {\n        chart._messageCenter.on(eventType, function (event) {\n          if (connectedGroups[chart.group] && chart[CONNECT_STATUS_KEY] !== CONNECT_STATUS_PENDING) {\n            if (event && event.escapeConnect) {\n              return;\n            }\n            var action_1 = chart.makeActionFromEvent(event);\n            var otherCharts_1 = [];\n            each(instances, function (otherChart) {\n              if (otherChart !== chart && otherChart.group === chart.group) {\n                otherCharts_1.push(otherChart);\n              }\n            });\n            updateConnectedChartsStatus(otherCharts_1, CONNECT_STATUS_PENDING);\n            each(otherCharts_1, function (otherChart) {\n              if (otherChart[CONNECT_STATUS_KEY] !== CONNECT_STATUS_UPDATING) {\n                otherChart.dispatchAction(action_1);\n              }\n            });\n            updateConnectedChartsStatus(otherCharts_1, CONNECT_STATUS_UPDATED);\n          }\n        });\n      });\n    };\n  }();\n  return ECharts;\n}(Eventful);\nvar echartsProto = ECharts.prototype;\nechartsProto.on = createRegisterEventWithLowercaseECharts('on');\nechartsProto.off = createRegisterEventWithLowercaseECharts('off');\n/**\r\n * @deprecated\r\n */\n// @ts-ignore\nechartsProto.one = function (eventName, cb, ctx) {\n  var self = this;\n  deprecateLog('ECharts#one is deprecated.');\n  function wrapped() {\n    var args2 = [];\n    for (var _i = 0; _i < arguments.length; _i++) {\n      args2[_i] = arguments[_i];\n    }\n    cb && cb.apply && cb.apply(this, args2);\n    // @ts-ignore\n    self.off(eventName, wrapped);\n  }\n  ;\n  // @ts-ignore\n  this.on.call(this, eventName, wrapped, ctx);\n};\nvar MOUSE_EVENT_NAMES = ['click', 'dblclick', 'mouseover', 'mouseout', 'mousemove', 'mousedown', 'mouseup', 'globalout', 'contextmenu'];\nfunction disposedWarning(id) {\n  if (process.env.NODE_ENV !== 'production') {\n    warn('Instance ' + id + ' has been disposed');\n  }\n}\nvar actions = {};\n/**\r\n * Map eventType to actionType\r\n */\nvar eventActionMap = {};\nvar dataProcessorFuncs = [];\nvar optionPreprocessorFuncs = [];\nvar visualFuncs = [];\nvar themeStorage = {};\nvar loadingEffects = {};\nvar instances = {};\nvar connectedGroups = {};\nvar idBase = +new Date() - 0;\nvar groupIdBase = +new Date() - 0;\nvar DOM_ATTRIBUTE_KEY = '_echarts_instance_';\n/**\r\n * @param opts.devicePixelRatio Use window.devicePixelRatio by default\r\n * @param opts.renderer Can choose 'canvas' or 'svg' to render the chart.\r\n * @param opts.width Use clientWidth of the input `dom` by default.\r\n *        Can be 'auto' (the same as null/undefined)\r\n * @param opts.height Use clientHeight of the input `dom` by default.\r\n *        Can be 'auto' (the same as null/undefined)\r\n * @param opts.locale Specify the locale.\r\n * @param opts.useDirtyRect Enable dirty rectangle rendering or not.\r\n */\nexport function init(dom, theme, opts) {\n  var isClient = !(opts && opts.ssr);\n  if (isClient) {\n    if (process.env.NODE_ENV !== 'production') {\n      if (!dom) {\n        throw new Error('Initialize failed: invalid dom.');\n      }\n    }\n    var existInstance = getInstanceByDom(dom);\n    if (existInstance) {\n      if (process.env.NODE_ENV !== 'production') {\n        warn('There is a chart instance already initialized on the dom.');\n      }\n      return existInstance;\n    }\n    if (process.env.NODE_ENV !== 'production') {\n      if (isDom(dom) && dom.nodeName.toUpperCase() !== 'CANVAS' && (!dom.clientWidth && (!opts || opts.width == null) || !dom.clientHeight && (!opts || opts.height == null))) {\n        warn('Can\\'t get DOM width or height. Please check ' + 'dom.clientWidth and dom.clientHeight. They should not be 0.' + 'For example, you may need to call this in the callback ' + 'of window.onload.');\n      }\n    }\n  }\n  var chart = new ECharts(dom, theme, opts);\n  chart.id = 'ec_' + idBase++;\n  instances[chart.id] = chart;\n  isClient && modelUtil.setAttribute(dom, DOM_ATTRIBUTE_KEY, chart.id);\n  enableConnect(chart);\n  lifecycle.trigger('afterinit', chart);\n  return chart;\n}\n/**\r\n * @usage\r\n * (A)\r\n * ```js\r\n * let chart1 = echarts.init(dom1);\r\n * let chart2 = echarts.init(dom2);\r\n * chart1.group = 'xxx';\r\n * chart2.group = 'xxx';\r\n * echarts.connect('xxx');\r\n * ```\r\n * (B)\r\n * ```js\r\n * let chart1 = echarts.init(dom1);\r\n * let chart2 = echarts.init(dom2);\r\n * echarts.connect('xxx', [chart1, chart2]);\r\n * ```\r\n */\nexport function connect(groupId) {\n  // Is array of charts\n  if (isArray(groupId)) {\n    var charts = groupId;\n    groupId = null;\n    // If any chart has group\n    each(charts, function (chart) {\n      if (chart.group != null) {\n        groupId = chart.group;\n      }\n    });\n    groupId = groupId || 'g_' + groupIdBase++;\n    each(charts, function (chart) {\n      chart.group = groupId;\n    });\n  }\n  connectedGroups[groupId] = true;\n  return groupId;\n}\nexport function disconnect(groupId) {\n  connectedGroups[groupId] = false;\n}\n/**\r\n * Alias and backward compatibility\r\n * @deprecated\r\n */\nexport var disConnect = disconnect;\n/**\r\n * Dispose a chart instance\r\n */\nexport function dispose(chart) {\n  if (isString(chart)) {\n    chart = instances[chart];\n  } else if (!(chart instanceof ECharts)) {\n    // Try to treat as dom\n    chart = getInstanceByDom(chart);\n  }\n  if (chart instanceof ECharts && !chart.isDisposed()) {\n    chart.dispose();\n  }\n}\nexport function getInstanceByDom(dom) {\n  return instances[modelUtil.getAttribute(dom, DOM_ATTRIBUTE_KEY)];\n}\nexport function getInstanceById(key) {\n  return instances[key];\n}\n/**\r\n * Register theme\r\n */\nexport function registerTheme(name, theme) {\n  themeStorage[name] = theme;\n}\n/**\r\n * Register option preprocessor\r\n */\nexport function registerPreprocessor(preprocessorFunc) {\n  if (indexOf(optionPreprocessorFuncs, preprocessorFunc) < 0) {\n    optionPreprocessorFuncs.push(preprocessorFunc);\n  }\n}\nexport function registerProcessor(priority, processor) {\n  normalizeRegister(dataProcessorFuncs, priority, processor, PRIORITY_PROCESSOR_DEFAULT);\n}\n/**\r\n * Register postIniter\r\n * @param {Function} postInitFunc\r\n */\nexport function registerPostInit(postInitFunc) {\n  registerUpdateLifecycle('afterinit', postInitFunc);\n}\n/**\r\n * Register postUpdater\r\n * @param {Function} postUpdateFunc\r\n */\nexport function registerPostUpdate(postUpdateFunc) {\n  registerUpdateLifecycle('afterupdate', postUpdateFunc);\n}\nexport function registerUpdateLifecycle(name, cb) {\n  lifecycle.on(name, cb);\n}\nexport function registerAction(actionInfo, eventName, action) {\n  if (isFunction(eventName)) {\n    action = eventName;\n    eventName = '';\n  }\n  var actionType = isObject(actionInfo) ? actionInfo.type : [actionInfo, actionInfo = {\n    event: eventName\n  }][0];\n  // Event name is all lowercase\n  actionInfo.event = (actionInfo.event || actionType).toLowerCase();\n  eventName = actionInfo.event;\n  if (eventActionMap[eventName]) {\n    // Already registered.\n    return;\n  }\n  // Validate action type and event name.\n  assert(ACTION_REG.test(actionType) && ACTION_REG.test(eventName));\n  if (!actions[actionType]) {\n    actions[actionType] = {\n      action: action,\n      actionInfo: actionInfo\n    };\n  }\n  eventActionMap[eventName] = actionType;\n}\nexport function registerCoordinateSystem(type, coordSysCreator) {\n  CoordinateSystemManager.register(type, coordSysCreator);\n}\n/**\r\n * Get dimensions of specified coordinate system.\r\n * @param {string} type\r\n * @return {Array.<string|Object>}\r\n */\nexport function getCoordinateSystemDimensions(type) {\n  var coordSysCreator = CoordinateSystemManager.get(type);\n  if (coordSysCreator) {\n    return coordSysCreator.getDimensionsInfo ? coordSysCreator.getDimensionsInfo() : coordSysCreator.dimensions.slice();\n  }\n}\nexport { registerLocale } from './locale.js';\nfunction registerLayout(priority, layoutTask) {\n  normalizeRegister(visualFuncs, priority, layoutTask, PRIORITY_VISUAL_LAYOUT, 'layout');\n}\nfunction registerVisual(priority, visualTask) {\n  normalizeRegister(visualFuncs, priority, visualTask, PRIORITY_VISUAL_CHART, 'visual');\n}\nexport { registerLayout, registerVisual };\nvar registeredTasks = [];\nfunction normalizeRegister(targetList, priority, fn, defaultPriority, visualType) {\n  if (isFunction(priority) || isObject(priority)) {\n    fn = priority;\n    priority = defaultPriority;\n  }\n  if (process.env.NODE_ENV !== 'production') {\n    if (isNaN(priority) || priority == null) {\n      throw new Error('Illegal priority');\n    }\n    // Check duplicate\n    each(targetList, function (wrap) {\n      assert(wrap.__raw !== fn);\n    });\n  }\n  // Already registered\n  if (indexOf(registeredTasks, fn) >= 0) {\n    return;\n  }\n  registeredTasks.push(fn);\n  var stageHandler = Scheduler.wrapStageHandler(fn, visualType);\n  stageHandler.__prio = priority;\n  stageHandler.__raw = fn;\n  targetList.push(stageHandler);\n}\nexport function registerLoading(name, loadingFx) {\n  loadingEffects[name] = loadingFx;\n}\n/**\r\n * ZRender need a canvas context to do measureText.\r\n * But in node environment canvas may be created by node-canvas.\r\n * So we need to specify how to create a canvas instead of using document.createElement('canvas')\r\n *\r\n *\r\n * @deprecated use setPlatformAPI({ createCanvas }) instead.\r\n *\r\n * @example\r\n *     let Canvas = require('canvas');\r\n *     let echarts = require('echarts');\r\n *     echarts.setCanvasCreator(function () {\r\n *         // Small size is enough.\r\n *         return new Canvas(32, 32);\r\n *     });\r\n */\nexport function setCanvasCreator(creator) {\n  if (process.env.NODE_ENV !== 'production') {\n    deprecateLog('setCanvasCreator is deprecated. Use setPlatformAPI({ createCanvas }) instead.');\n  }\n  setPlatformAPI({\n    createCanvas: creator\n  });\n}\n/**\r\n * The parameters and usage: see `geoSourceManager.registerMap`.\r\n * Compatible with previous `echarts.registerMap`.\r\n */\nexport function registerMap(mapName, geoJson, specialAreas) {\n  var registerMap = getImpl('registerMap');\n  registerMap && registerMap(mapName, geoJson, specialAreas);\n}\nexport function getMap(mapName) {\n  var getMap = getImpl('getMap');\n  return getMap && getMap(mapName);\n}\nexport var registerTransform = registerExternalTransform;\n/**\r\n * Globa dispatchAction to a specified chart instance.\r\n */\n// export function dispatchAction(payload: { chartId: string } & Payload, opt?: Parameters<ECharts['dispatchAction']>[1]) {\n//     if (!payload || !payload.chartId) {\n//         // Must have chartId to find chart\n//         return;\n//     }\n//     const chart = instances[payload.chartId];\n//     if (chart) {\n//         chart.dispatchAction(payload, opt);\n//     }\n// }\n// Builtin global visual\nregisterVisual(PRIORITY_VISUAL_GLOBAL, seriesStyleTask);\nregisterVisual(PRIORITY_VISUAL_CHART_DATA_CUSTOM, dataStyleTask);\nregisterVisual(PRIORITY_VISUAL_CHART_DATA_CUSTOM, dataColorPaletteTask);\nregisterVisual(PRIORITY_VISUAL_GLOBAL, seriesSymbolTask);\nregisterVisual(PRIORITY_VISUAL_CHART_DATA_CUSTOM, dataSymbolTask);\nregisterVisual(PRIORITY_VISUAL_DECAL, decal);\nregisterPreprocessor(backwardCompat);\nregisterProcessor(PRIORITY_PROCESSOR_DATASTACK, dataStack);\nregisterLoading('default', loadingDefault);\n// Default actions\nregisterAction({\n  type: HIGHLIGHT_ACTION_TYPE,\n  event: HIGHLIGHT_ACTION_TYPE,\n  update: HIGHLIGHT_ACTION_TYPE\n}, noop);\nregisterAction({\n  type: DOWNPLAY_ACTION_TYPE,\n  event: DOWNPLAY_ACTION_TYPE,\n  update: DOWNPLAY_ACTION_TYPE\n}, noop);\nregisterAction({\n  type: SELECT_ACTION_TYPE,\n  event: SELECT_ACTION_TYPE,\n  update: SELECT_ACTION_TYPE\n}, noop);\nregisterAction({\n  type: UNSELECT_ACTION_TYPE,\n  event: UNSELECT_ACTION_TYPE,\n  update: UNSELECT_ACTION_TYPE\n}, noop);\nregisterAction({\n  type: TOGGLE_SELECT_ACTION_TYPE,\n  event: TOGGLE_SELECT_ACTION_TYPE,\n  update: TOGGLE_SELECT_ACTION_TYPE\n}, noop);\n// Default theme\nregisterTheme('light', lightTheme);\nregisterTheme('dark', darkTheme);\n// For backward compatibility, where the namespace `dataTool` will\n// be mounted on `echarts` is the extension `dataTool` is imported.\nexport var dataTool = {};"], "names": [], "mappings": "AACA;;;;;;;;;;;;;;;;;AAiBA,GAGA;;CAEC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAiVS;AA/UV;AACA;;;;;;;;;;;;;;;;;AAiBA,GACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AACO,IAAI,UAAU;AACd,IAAI,eAAe;IACxB,SAAS;AACX;AACA,IAAI,yBAAyB;AAC7B,IAAI,mCAAmC;AACvC,yFAAyF;AACzF,mEAAmE;AACnE,IAAI,+BAA+B;AACnC,uDAAuD;AACvD,2CAA2C;AAC3C,IAAI,4BAA4B;AAChC,IAAI,6BAA6B;AACjC,IAAI,+BAA+B;AACnC,IAAI,yBAAyB;AAC7B,IAAI,qCAAqC;AACzC,IAAI,yBAAyB;AAC7B,IAAI,wBAAwB;AAC5B,IAAI,4BAA4B;AAChC,iFAAiF;AACjF,8DAA8D;AAC9D,qEAAqE;AACrE,IAAI,oCAAoC;AACxC,gFAAgF;AAChF,mCAAmC;AACnC,IAAI,oCAAoC;AACxC,IAAI,wBAAwB;AAC5B,IAAI,uBAAuB;AAC3B,IAAI,wBAAwB;AACrB,IAAI,WAAW;IACpB,WAAW;QACT,QAAQ;QACR,eAAe;QACf,WAAW;IACb;IACA,QAAQ;QACN,QAAQ;QACR,oBAAoB;QACpB,QAAQ;QACR,OAAO;QACP,mBAAmB;QACnB,WAAW;QACX,OAAO;QACP,YAAY;QACZ,MAAM;QACN,OAAO;IACT;AACF;AACA,+EAA+E;AAC/E,wEAAwE;AACxE,2DAA2D;AAC3D,4CAA4C;AAC5C,yFAAyF;AACzF,IAAI,sBAAsB;AAC1B,IAAI,iBAAiB;AACrB,IAAI,0BAA0B;AAC9B,IAAI,aAAa;AACjB,IAAI,qBAAqB;AACzB,IAAI,yBAAyB;AAC7B,IAAI,0BAA0B;AAC9B,IAAI,yBAAyB;;;AAG7B,SAAS,wCAAwC,MAAM;IACrD,OAAO;QACL,IAAI,OAAO,EAAE;QACb,IAAK,IAAI,KAAK,GAAG,KAAK,UAAU,MAAM,EAAE,KAAM;YAC5C,IAAI,CAAC,GAAG,GAAG,SAAS,CAAC,GAAG;QAC1B;QACA,IAAI,IAAI,CAAC,UAAU,IAAI;YACrB,gBAAgB,IAAI,CAAC,EAAE;YACvB;QACF;QACA,OAAO,+BAA+B,IAAI,EAAE,QAAQ;IACtD;AACF;AACA,SAAS,8CAA8C,MAAM;IAC3D,OAAO;QACL,IAAI,OAAO,EAAE;QACb,IAAK,IAAI,KAAK,GAAG,KAAK,UAAU,MAAM,EAAE,KAAM;YAC5C,IAAI,CAAC,GAAG,GAAG,SAAS,CAAC,GAAG;QAC1B;QACA,OAAO,+BAA+B,IAAI,EAAE,QAAQ;IACtD;AACF;AACA,SAAS,+BAA+B,IAAI,EAAE,MAAM,EAAE,IAAI;IACxD,wDAAwD;IACxD,IAAI,CAAC,EAAE,GAAG,IAAI,CAAC,EAAE,IAAI,IAAI,CAAC,EAAE,CAAC,WAAW;IACxC,OAAO,qJAAA,CAAA,UAAQ,CAAC,SAAS,CAAC,OAAO,CAAC,KAAK,CAAC,MAAM;AAChD;AACA,IAAI,gBAAgB,WAAW,GAAE,SAAU,MAAM;IAC/C,CAAA,GAAA,mKAAA,CAAA,YAAS,AAAD,EAAE,eAAe;IACzB,SAAS;QACP,OAAO,WAAW,QAAQ,OAAO,KAAK,CAAC,IAAI,EAAE,cAAc,IAAI;IACjE;IACA,OAAO;AACT,EAAE,qJAAA,CAAA,UAAQ;AACV,IAAI,qBAAqB,cAAc,SAAS;AAChD,mBAAmB,EAAE,GAAG,8CAA8C;AACtE,mBAAmB,GAAG,GAAG,8CAA8C;AACvE,0CAA0C;AAC1C,0CAA0C;AAC1C,0CAA0C;AAC1C,IAAI;AACJ,IAAI;AACJ,IAAI;AACJ,IAAI;AACJ,IAAI;AACJ,IAAI;AACJ,IAAI;AACJ,IAAI;AACJ,IAAI;AACJ,IAAI;AACJ,IAAI;AACJ,IAAI;AACJ,IAAI;AACJ,IAAI;AACJ,IAAI;AACJ,IAAI;AACJ,IAAI;AACJ,IAAI;AACJ,IAAI,UAAU,WAAW,GAAE,SAAU,MAAM;IACzC,CAAA,GAAA,mKAAA,CAAA,YAAS,AAAD,EAAE,SAAS;IACnB,SAAS,QAAQ,GAAG,EACpB,6BAA6B;IAC7B,KAAK,EAAE,IAAI;QACT,IAAI,QAAQ,OAAO,IAAI,CAAC,IAAI,EAAE,IAAI,6JAAA,CAAA,mBAAgB,OAAO,IAAI;QAC7D,MAAM,YAAY,GAAG,EAAE;QACvB,MAAM,UAAU,GAAG,CAAC;QACpB,MAAM,gBAAgB,GAAG,EAAE;QAC3B,MAAM,cAAc,GAAG,CAAC;QACxB,mDAAmD;QACnD,MAAM,eAAe,GAAG,EAAE;QAC1B,OAAO,QAAQ,CAAC;QAChB,oBAAoB;QACpB,IAAI,CAAA,GAAA,iJAAA,CAAA,WAAQ,AAAD,EAAE,QAAQ;YACnB,QAAQ,YAAY,CAAC,MAAM;QAC7B;QACA,MAAM,IAAI,GAAG;QACb,IAAI,kBAAkB;QACtB,IAAI,uBAAuB;QAC3B,IAAI,sBAAsB;QAC1B,wCAA2C;YACzC,IAAI,OAAO,4BAA4B,GACvC,gJAAA,CAAA,UAAG,CAAC,eAAe,GAAG,SAAS;YAC/B,IAAI,MAAM;gBACR,kBAAkB,CAAA,GAAA,iJAAA,CAAA,YAAS,AAAD,EAAE,KAAK,8BAA8B,EAAE;gBACjE,uBAAuB,CAAA,GAAA,iJAAA,CAAA,YAAS,AAAD,EAAE,KAAK,kCAAkC,EAAE;gBAC1E,sBAAsB,CAAA,GAAA,iJAAA,CAAA,YAAS,AAAD,EAAE,KAAK,oCAAoC,EAAE;YAC7E;QACF;QACA,IAAI,KAAK,GAAG,EAAE;YACZ,CAAA,GAAA,4IAAA,CAAA,wBAA6B,AAAD,EAAE,SAAU,EAAE;gBACxC,IAAI,SAAS,CAAA,GAAA,uJAAA,CAAA,YAAS,AAAD,EAAE;gBACvB,IAAI,YAAY,OAAO,SAAS;gBAChC,IAAI,aAAa,MAAM;oBACrB;gBACF;gBACA,IAAI,UAAU,CAAA,GAAA,iJAAA,CAAA,gBAAa,AAAD;gBAC1B,QAAQ,GAAG,CAAC,gBAAgB,OAAO,WAAW;gBAC9C,QAAQ,GAAG,CAAC,cAAc;gBAC1B,OAAO,OAAO,IAAI,QAAQ,GAAG,CAAC,YAAY,OAAO,OAAO;gBACxD,OAAO;YACT;QACF;QACA,IAAI,KAAK,MAAM,GAAG,GAAG,CAAA,GAAA,4IAAA,CAAA,OAAY,AAAD,EAAE,KAAK;YACrC,UAAU,KAAK,QAAQ,IAAI;YAC3B,kBAAkB,KAAK,gBAAgB;YACvC,OAAO,KAAK,KAAK;YACjB,QAAQ,KAAK,MAAM;YACnB,KAAK,KAAK,GAAG;YACb,cAAc,CAAA,GAAA,iJAAA,CAAA,YAAS,AAAD,EAAE,KAAK,YAAY,EAAE;YAC3C,kBAAkB,CAAA,GAAA,iJAAA,CAAA,YAAS,AAAD,EAAE,KAAK,gBAAgB,EAAE;YACnD,aAAa,KAAK,WAAW;QAC/B;QACA,MAAM,IAAI,GAAG,KAAK,GAAG;QACrB,iBAAiB;QACjB,MAAM,iBAAiB,GAAG,CAAA,GAAA,qJAAA,CAAA,WAAQ,AAAD,EAAE,CAAA,GAAA,iJAAA,CAAA,OAAI,AAAD,EAAE,GAAG,KAAK,EAAE,KAAK;QACvD,QAAQ,CAAA,GAAA,iJAAA,CAAA,QAAK,AAAD,EAAE;QACd,SAAS,CAAA,GAAA,mKAAA,CAAA,UAAc,AAAD,EAAE,OAAO;QAC/B,MAAM,MAAM,GAAG;QACf,MAAM,OAAO,GAAG,CAAA,GAAA,mJAAA,CAAA,qBAAkB,AAAD,EAAE,KAAK,MAAM,IAAI,mJAAA,CAAA,cAAW;QAC7D,MAAM,YAAY,GAAG,IAAI,6JAAA,CAAA,UAAuB;QAChD,IAAI,MAAM,MAAM,IAAI,GAAG,mBAAmB;QAC1C,iBAAiB;QACjB,SAAS,iBAAiB,CAAC,EAAE,CAAC;YAC5B,OAAO,EAAE,MAAM,GAAG,EAAE,MAAM;QAC5B;QACA,CAAA,GAAA,oJAAA,CAAA,UAAO,AAAD,EAAE,aAAa;QACrB,CAAA,GAAA,oJAAA,CAAA,UAAO,AAAD,EAAE,oBAAoB;QAC5B,MAAM,UAAU,GAAG,IAAI,sJAAA,CAAA,UAAS,CAAC,OAAO,KAAK,oBAAoB;QACjE,MAAM,cAAc,GAAG,IAAI;QAC3B,oBAAoB;QACpB,MAAM,WAAW;QACjB,6DAA6D;QAC7D,MAAM,MAAM,GAAG,CAAA,GAAA,iJAAA,CAAA,OAAI,AAAD,EAAE,MAAM,MAAM,EAAE;QAClC,GAAG,SAAS,CAAC,EAAE,CAAC,SAAS,MAAM,QAAQ,EAAE;QACzC,kBAAkB,IAAI;QACtB,eAAe,IAAI;QACnB,yCAAyC;QACzC,CAAA,GAAA,iJAAA,CAAA,iBAAc,AAAD,EAAE;QACf,OAAO;IACT;IACA,QAAQ,SAAS,CAAC,QAAQ,GAAG;QAC3B,IAAI,IAAI,CAAC,SAAS,EAAE;YAClB;QACF;QACA,mBAAmB,IAAI;QACvB,IAAI,YAAY,IAAI,CAAC,UAAU;QAC/B,cAAc;QACd,IAAI,IAAI,CAAC,eAAe,EAAE;YACxB,IAAI,SAAS,IAAI,CAAC,eAAe,CAAC,MAAM;YACxC,IAAI,CAAC,oBAAoB,GAAG;YAC5B,IAAI;gBACF,QAAQ,IAAI;gBACZ,cAAc,MAAM,CAAC,IAAI,CAAC,IAAI,EAAE,MAAM,IAAI,CAAC,eAAe,CAAC,YAAY;YACzE,EAAE,OAAO,GAAG;gBACV,IAAI,CAAC,oBAAoB,GAAG;gBAC5B,IAAI,CAAC,eAAe,GAAG;gBACvB,MAAM;YACR;YACA,+CAA+C;YAC/C,gCAAgC;YAChC,2DAA2D;YAC3D,gCAAgC;YAChC,6EAA6E;YAC7E,iFAAiF;YACjF,IAAI,CAAC,GAAG,CAAC,KAAK;YACd,IAAI,CAAC,oBAAoB,GAAG;YAC5B,IAAI,CAAC,eAAe,GAAG;YACvB,oBAAoB,IAAI,CAAC,IAAI,EAAE;YAC/B,oBAAoB,IAAI,CAAC,IAAI,EAAE;QACjC,OAEK,IAAI,UAAU,UAAU,EAAE;YAC7B,mBAAmB;YACnB,IAAI,aAAa;YACjB,IAAI,UAAU,IAAI,CAAC,MAAM;YACzB,IAAI,MAAM,IAAI,CAAC,IAAI;YACnB,UAAU,UAAU,GAAG;YACvB,GAAG;gBACD,IAAI,YAAY,CAAC,IAAI;gBACrB,UAAU,kBAAkB,CAAC;gBAC7B,uDAAuD;gBACvD,UAAU,yBAAyB,CAAC;gBACpC,kBAAkB,IAAI,EAAE;gBACxB,4EAA4E;gBAC5E,uEAAuE;gBACvE,4EAA4E;gBAC5E,kDAAkD;gBAClD,0CAA0C;gBAC1C,sDAAsD;gBACtD,UAAU,kBAAkB,CAAC;gBAC7B,aAAa,IAAI,EAAE,IAAI,CAAC,MAAM,EAAE,KAAK,UAAU,CAAC;gBAChD,cAAc,CAAC,IAAI,SAAS;YAC9B,QAAS,aAAa,KAAK,UAAU,UAAU,CAAE;YACjD,oDAAoD;YACpD,IAAI,CAAC,UAAU,UAAU,EAAE;gBACzB,IAAI,CAAC,GAAG,CAAC,KAAK;YAChB;QACA,oDAAoD;QACpD,8CAA8C;QAChD;IACF;IACA,QAAQ,SAAS,CAAC,MAAM,GAAG;QACzB,OAAO,IAAI,CAAC,IAAI;IAClB;IACA,QAAQ,SAAS,CAAC,KAAK,GAAG;QACxB,OAAO,IAAI,CAAC,EAAE;IAChB;IACA,QAAQ,SAAS,CAAC,KAAK,GAAG;QACxB,OAAO,IAAI,CAAC,GAAG;IACjB;IACA,QAAQ,SAAS,CAAC,KAAK,GAAG;QACxB,OAAO,IAAI,CAAC,IAAI;IAClB;IACA,4BAA4B,GAC5B,QAAQ,SAAS,CAAC,SAAS,GAAG,SAAU,MAAM,EAAE,QAAQ,EAAE,UAAU;QAClE,IAAI,IAAI,CAAC,oBAAoB,EAAE;YAC7B,wCAA2C;gBACzC,CAAA,GAAA,gJAAA,CAAA,QAAK,AAAD,EAAE;YACR;YACA;QACF;QACA,IAAI,IAAI,CAAC,SAAS,EAAE;YAClB,gBAAgB,IAAI,CAAC,EAAE;YACvB;QACF;QACA,IAAI;QACJ,IAAI;QACJ,IAAI;QACJ,IAAI,CAAA,GAAA,iJAAA,CAAA,WAAQ,AAAD,EAAE,WAAW;YACtB,aAAa,SAAS,UAAU;YAChC,SAAS,SAAS,MAAM;YACxB,eAAe,SAAS,YAAY;YACpC,gBAAgB,SAAS,UAAU;YACnC,WAAW,SAAS,QAAQ;QAC9B;QACA,IAAI,CAAC,oBAAoB,GAAG;QAC5B,IAAI,CAAC,IAAI,CAAC,MAAM,IAAI,UAAU;YAC5B,IAAI,gBAAgB,IAAI,2JAAA,CAAA,UAAa,CAAC,IAAI,CAAC,IAAI;YAC/C,IAAI,QAAQ,IAAI,CAAC,MAAM;YACvB,IAAI,UAAU,IAAI,CAAC,MAAM,GAAG,IAAI,oJAAA,CAAA,UAAW;YAC3C,QAAQ,SAAS,GAAG,IAAI,CAAC,UAAU;YACnC,QAAQ,GAAG,GAAG,IAAI,CAAC,IAAI;YACvB,QAAQ,IAAI,CAAC,MAAM,MAAM,MAAM,OAAO,IAAI,CAAC,OAAO,EAAE;QACtD;QACA,IAAI,CAAC,MAAM,CAAC,SAAS,CAAC,QAAQ;YAC5B,cAAc;QAChB,GAAG;QACH,IAAI,eAAe;YACjB,kBAAkB;YAClB,eAAe;QACjB;QACA,IAAI,YAAY;YACd,IAAI,CAAC,eAAe,GAAG;gBACrB,QAAQ;gBACR,cAAc;YAChB;YACA,IAAI,CAAC,oBAAoB,GAAG;YAC5B,mFAAmF;YACnF,+EAA+E;YAC/E,IAAI,CAAC,KAAK,GAAG,MAAM;QACrB,OAAO;YACL,IAAI;gBACF,QAAQ,IAAI;gBACZ,cAAc,MAAM,CAAC,IAAI,CAAC,IAAI,EAAE,MAAM;YACxC,EAAE,OAAO,GAAG;gBACV,IAAI,CAAC,eAAe,GAAG;gBACvB,IAAI,CAAC,oBAAoB,GAAG;gBAC5B,MAAM;YACR;YACA,kEAAkE;YAClE,6BAA6B;YAC7B,IAAI,CAAC,IAAI,CAAC,IAAI,EAAE;gBACd,qCAAqC;gBACrC,IAAI,CAAC,GAAG,CAAC,KAAK;YAChB;YACA,IAAI,CAAC,eAAe,GAAG;YACvB,IAAI,CAAC,oBAAoB,GAAG;YAC5B,oBAAoB,IAAI,CAAC,IAAI,EAAE;YAC/B,oBAAoB,IAAI,CAAC,IAAI,EAAE;QACjC;IACF;IACA;;GAEC,GACD,QAAQ,SAAS,CAAC,QAAQ,GAAG;QAC3B,CAAA,GAAA,gJAAA,CAAA,eAAY,AAAD,EAAE;IACf;IACA,qDAAqD;IACrD,QAAQ,SAAS,CAAC,QAAQ,GAAG;QAC3B,OAAO,IAAI,CAAC,MAAM;IACpB;IACA,QAAQ,SAAS,CAAC,SAAS,GAAG;QAC5B,OAAO,IAAI,CAAC,MAAM,IAAI,IAAI,CAAC,MAAM,CAAC,SAAS;IAC7C;IACA,QAAQ,SAAS,CAAC,QAAQ,GAAG;QAC3B,OAAO,IAAI,CAAC,GAAG,CAAC,QAAQ;IAC1B;IACA,QAAQ,SAAS,CAAC,SAAS,GAAG;QAC5B,OAAO,IAAI,CAAC,GAAG,CAAC,SAAS;IAC3B;IACA,QAAQ,SAAS,CAAC,mBAAmB,GAAG;QACtC,OAAO,IAAI,CAAC,GAAG,CAAC,OAAO,CAAC,GAAG,IACO,gJAAA,CAAA,UAAG,CAAC,eAAe,IAAI,OAAO,gBAAgB,IAAI;IACtF;IACA;;;GAGC,GACD,QAAQ,SAAS,CAAC,iBAAiB,GAAG,SAAU,IAAI;QAClD,wCAA2C;YACzC,CAAA,GAAA,gJAAA,CAAA,sBAAmB,AAAD,EAAE,qBAAqB;QAC3C;QACA,OAAO,IAAI,CAAC,cAAc,CAAC;IAC7B;IACA,QAAQ,SAAS,CAAC,cAAc,GAAG,SAAU,IAAI;QAC/C,OAAO,QAAQ,CAAC;QAChB,IAAI,UAAU,IAAI,CAAC,GAAG,CAAC,OAAO;QAC9B,wCAA2C;YACzC,IAAI,QAAQ,IAAI,KAAK,UAAU;gBAC7B,MAAM,IAAI,MAAM;YAClB;QACF;QACA,OAAO,QAAQ,iBAAiB,CAAC;YAC/B,iBAAiB,KAAK,eAAe,IAAI,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC;YACzD,YAAY,KAAK,UAAU,IAAI,IAAI,CAAC,mBAAmB;QACzD;IACF;IACA,QAAQ,SAAS,CAAC,iBAAiB,GAAG,SAAU,IAAI;QAClD,OAAO,QAAQ,CAAC;QAChB,IAAI,UAAU,IAAI,CAAC,GAAG,CAAC,OAAO;QAC9B,wCAA2C;YACzC,IAAI,QAAQ,IAAI,KAAK,OAAO;gBAC1B,MAAM,IAAI,MAAM;YAClB;QACF;QACA,OAAO,QAAQ,cAAc,CAAC;YAC5B,YAAY,KAAK,UAAU;QAC7B;IACF;IACA;;GAEC,GACD,QAAQ,SAAS,CAAC,aAAa,GAAG;QAChC,IAAI,CAAC,gJAAA,CAAA,UAAG,CAAC,YAAY,EAAE;YACrB;QACF;QACA,IAAI,KAAK,IAAI,CAAC,GAAG;QACjB,IAAI,OAAO,GAAG,OAAO,CAAC,cAAc;QACpC,kBAAkB;QAClB,CAAA,GAAA,iJAAA,CAAA,OAAI,AAAD,EAAE,MAAM,SAAU,EAAE;YACrB,GAAG,aAAa,CAAC,MAAM;QACzB;QACA,OAAO,GAAG,OAAO,CAAC,SAAS;IAC7B;IACA,QAAQ,SAAS,CAAC,UAAU,GAAG,SAAU,IAAI;QAC3C,IAAI,IAAI,CAAC,SAAS,EAAE;YAClB,gBAAgB,IAAI,CAAC,EAAE;YACvB;QACF;QACA,OAAO,QAAQ,CAAC;QAChB,IAAI,oBAAoB,KAAK,iBAAiB;QAC9C,IAAI,UAAU,IAAI,CAAC,MAAM;QACzB,IAAI,yBAAyB,EAAE;QAC/B,IAAI,OAAO,IAAI;QACf,CAAA,GAAA,iJAAA,CAAA,OAAI,AAAD,EAAE,mBAAmB,SAAU,aAAa;YAC7C,QAAQ,aAAa,CAAC;gBACpB,UAAU;YACZ,GAAG,SAAU,SAAS;gBACpB,IAAI,OAAO,KAAK,cAAc,CAAC,UAAU,QAAQ,CAAC;gBAClD,IAAI,CAAC,KAAK,KAAK,CAAC,MAAM,EAAE;oBACtB,uBAAuB,IAAI,CAAC;oBAC5B,KAAK,KAAK,CAAC,MAAM,GAAG;gBACtB;YACF;QACF;QACA,IAAI,MAAM,IAAI,CAAC,GAAG,CAAC,OAAO,CAAC,OAAO,OAAO,QAAQ,IAAI,CAAC,aAAa,KAAK,IAAI,CAAC,cAAc,CAAC,MAAM,SAAS,CAAC,WAAW,CAAC,QAAQ,KAAK,IAAI,IAAI,KAAK;QAClJ,CAAA,GAAA,iJAAA,CAAA,OAAI,AAAD,EAAE,wBAAwB,SAAU,IAAI;YACzC,KAAK,KAAK,CAAC,MAAM,GAAG;QACtB;QACA,OAAO;IACT;IACA,QAAQ,SAAS,CAAC,mBAAmB,GAAG,SAAU,IAAI;QACpD,IAAI,IAAI,CAAC,SAAS,EAAE;YAClB,gBAAgB,IAAI,CAAC,EAAE;YACvB;QACF;QACA,IAAI,QAAQ,KAAK,IAAI,KAAK;QAC1B,IAAI,UAAU,IAAI,CAAC,KAAK;QACxB,IAAI,UAAU,KAAK,GAAG;QACtB,IAAI,UAAU,KAAK,GAAG;QACtB,IAAI,aAAa;QACjB,IAAI,eAAe,CAAC,QAAQ,EAAE;YAC5B,IAAI,SAAS;YACb,IAAI,QAAQ;YACZ,IAAI,UAAU,CAAC;YACf,IAAI,WAAW,CAAC;YAChB,IAAI,eAAe,EAAE;YACrB,IAAI,QAAQ,QAAQ,KAAK,UAAU,IAAI,IAAI,CAAC,mBAAmB;YAC/D,CAAA,GAAA,iJAAA,CAAA,OAAI,AAAD,EAAE,WAAW,SAAU,KAAK,EAAE,EAAE;gBACjC,IAAI,MAAM,KAAK,KAAK,SAAS;oBAC3B,IAAI,SAAS,QAAQ,MAAM,KAAK,GAAG,OAAO,CAAC,SAAS,GAAG,SAAS,GAAG,MAAM,cAAc,CAAC,CAAA,GAAA,iJAAA,CAAA,QAAK,AAAD,EAAE;oBAC9F,IAAI,eAAe,MAAM,MAAM,GAAG,qBAAqB;oBACvD,SAAS,QAAQ,aAAa,IAAI,EAAE;oBACpC,QAAQ,QAAQ,aAAa,GAAG,EAAE;oBAClC,UAAU,QAAQ,aAAa,KAAK,EAAE;oBACtC,WAAW,QAAQ,aAAa,MAAM,EAAE;oBACxC,aAAa,IAAI,CAAC;wBAChB,KAAK;wBACL,MAAM,aAAa,IAAI;wBACvB,KAAK,aAAa,GAAG;oBACvB;gBACF;YACF;YACA,UAAU;YACV,SAAS;YACT,WAAW;YACX,YAAY;YACZ,IAAI,QAAQ,UAAU;YACtB,IAAI,SAAS,WAAW;YACxB,IAAI,eAAe,qJAAA,CAAA,cAAW,CAAC,YAAY;YAC3C,IAAI,OAAO,CAAA,GAAA,4IAAA,CAAA,OAAY,AAAD,EAAE,cAAc;gBACpC,UAAU,QAAQ,QAAQ;YAC5B;YACA,KAAK,MAAM,CAAC;gBACV,OAAO;gBACP,QAAQ;YACV;YACA,IAAI,OAAO;gBACT,IAAI,YAAY;gBAChB,CAAA,GAAA,iJAAA,CAAA,OAAI,AAAD,EAAE,cAAc,SAAU,IAAI;oBAC/B,IAAI,IAAI,KAAK,IAAI,GAAG;oBACpB,IAAI,IAAI,KAAK,GAAG,GAAG;oBACnB,aAAa,6BAA6B,IAAI,MAAM,IAAI,QAAQ,KAAK,GAAG,GAAG;gBAC7E;gBACA,KAAK,OAAO,CAAC,UAAU,GAAG,SAAS,GAAG;gBACtC,IAAI,KAAK,wBAAwB,EAAE;oBACjC,KAAK,OAAO,CAAC,kBAAkB,CAAC,KAAK,wBAAwB;gBAC/D;gBACA,KAAK,kBAAkB;gBACvB,OAAO,KAAK,OAAO,CAAC,SAAS;YAC/B,OAAO;gBACL,gCAAgC;gBAChC,IAAI,KAAK,wBAAwB,EAAE;oBACjC,KAAK,GAAG,CAAC,IAAI,gMAAA,CAAA,OAAY,CAAC;wBACxB,OAAO;4BACL,GAAG;4BACH,GAAG;4BACH,OAAO;4BACP,QAAQ;wBACV;wBACA,OAAO;4BACL,MAAM,KAAK,wBAAwB;wBACrC;oBACF;gBACF;gBACA,CAAA,GAAA,iJAAA,CAAA,OAAI,AAAD,EAAE,cAAc,SAAU,IAAI;oBAC/B,IAAI,MAAM,IAAI,yLAAA,CAAA,QAAa,CAAC;wBAC1B,OAAO;4BACL,GAAG,KAAK,IAAI,GAAG,QAAQ;4BACvB,GAAG,KAAK,GAAG,GAAG,QAAQ;4BACtB,OAAO,KAAK,GAAG;wBACjB;oBACF;oBACA,KAAK,GAAG,CAAC;gBACX;gBACA,KAAK,kBAAkB;gBACvB,OAAO,aAAa,SAAS,CAAC,WAAW,CAAC,QAAQ,KAAK,IAAI,IAAI,KAAK;YACtE;QACF,OAAO;YACL,OAAO,IAAI,CAAC,UAAU,CAAC;QACzB;IACF;IACA,QAAQ,SAAS,CAAC,cAAc,GAAG,SAAU,MAAM,EAAE,KAAK;QACxD,OAAO,eAAe,IAAI,EAAE,kBAAkB,QAAQ;IACxD;IACA,QAAQ,SAAS,CAAC,gBAAgB,GAAG,SAAU,MAAM,EAAE,KAAK;QAC1D,OAAO,eAAe,IAAI,EAAE,oBAAoB,QAAQ;IAC1D;IACA;;;;GAIC,GACD,QAAQ,SAAS,CAAC,YAAY,GAAG,SAAU,MAAM,EAAE,KAAK;QACtD,IAAI,IAAI,CAAC,SAAS,EAAE;YAClB,gBAAgB,IAAI,CAAC,EAAE;YACvB;QACF;QACA,IAAI,UAAU,IAAI,CAAC,MAAM;QACzB,IAAI;QACJ,IAAI,aAAa,CAAA,GAAA,kJAAA,CAAA,cAAqB,AAAD,EAAE,SAAS;QAChD,CAAA,GAAA,iJAAA,CAAA,OAAI,AAAD,EAAE,YAAY,SAAU,MAAM,EAAE,GAAG;YACpC,IAAI,OAAO,CAAC,aAAa,KAAK,CAAA,GAAA,iJAAA,CAAA,OAAI,AAAD,EAAE,QAAQ,SAAU,KAAK;gBACxD,IAAI,WAAW,MAAM,gBAAgB;gBACrC,IAAI,YAAY,SAAS,YAAY,EAAE;oBACrC,SAAS,UAAU,CAAC,CAAC,SAAS,YAAY,CAAC;gBAC7C,OAAO,IAAI,QAAQ,gBAAgB;oBACjC,IAAI,OAAO,IAAI,CAAC,UAAU,CAAC,MAAM,QAAQ,CAAC;oBAC1C,IAAI,QAAQ,KAAK,YAAY,EAAE;wBAC7B,SAAS,UAAU,KAAK,YAAY,CAAC,OAAO;oBAC9C,OAAO;wBACL,wCAA2C;4BACzC,CAAA,GAAA,gJAAA,CAAA,OAAI,AAAD,EAAE,MAAM,OAAO,CAAC,OAAO,qDAAqD,yCAAyC;wBAC1H;oBACF;gBACF,OAAO;oBACL,wCAA2C;wBACzC,CAAA,GAAA,gJAAA,CAAA,OAAI,AAAD,EAAE,MAAM;oBACb;gBACF;YACF,GAAG,IAAI;QACT,GAAG,IAAI;QACP,OAAO,CAAC,CAAC;IACX;IACA;;;;;;;;;;;;;;GAcC,GACD,QAAQ,SAAS,CAAC,SAAS,GAAG,SAAU,MAAM,EAAE,UAAU;QACxD,IAAI,UAAU,IAAI,CAAC,MAAM;QACzB,IAAI,eAAe,CAAA,GAAA,kJAAA,CAAA,cAAqB,AAAD,EAAE,SAAS,QAAQ;YACxD,iBAAiB;QACnB;QACA,IAAI,cAAc,aAAa,WAAW;QAC1C,wCAA2C;YACzC,IAAI,CAAC,aAAa;gBAChB,CAAA,GAAA,gJAAA,CAAA,OAAI,AAAD,EAAE;YACP;QACF;QACA,IAAI,OAAO,YAAY,OAAO;QAC9B,IAAI,kBAAkB,aAAa,cAAc,CAAC,qBAAqB,aAAa,eAAe,GAAG,aAAa,cAAc,CAAC,eAAe,KAAK,eAAe,CAAC,aAAa,SAAS,IAAI;QAChM,OAAO,mBAAmB,OAAO,CAAA,GAAA,qJAAA,CAAA,wBAAqB,AAAD,EAAE,MAAM,iBAAiB,cAAc,CAAA,GAAA,qJAAA,CAAA,oBAAiB,AAAD,EAAE,MAAM;IACtH;IACA;;GAEC,GACD,QAAQ,SAAS,CAAC,uBAAuB,GAAG,SAAU,cAAc;QAClE,OAAO,IAAI,CAAC,cAAc,CAAC,eAAe,QAAQ,CAAC;IACrD;IACA;;GAEC,GACD,QAAQ,SAAS,CAAC,oBAAoB,GAAG,SAAU,WAAW;QAC5D,OAAO,IAAI,CAAC,UAAU,CAAC,YAAY,QAAQ,CAAC;IAC9C;IACA,QAAQ,SAAS,CAAC,WAAW,GAAG;QAC9B,IAAI,QAAQ,IAAI;QAChB,CAAA,GAAA,iJAAA,CAAA,OAAI,AAAD,EAAE,mBAAmB,SAAU,OAAO;YACvC,IAAI,UAAU,SAAU,CAAC;gBACvB,IAAI,UAAU,MAAM,QAAQ;gBAC5B,IAAI,KAAK,EAAE,MAAM;gBACjB,IAAI;gBACJ,IAAI,cAAc,YAAY;gBAC9B,gCAAgC;gBAChC,IAAI,aAAa;oBACf,SAAS,CAAC;gBACZ,OAAO;oBACL,MAAM,CAAA,GAAA,kJAAA,CAAA,sBAAmB,AAAD,EAAE,IAAI,SAAU,MAAM;wBAC5C,IAAI,SAAS,CAAA,GAAA,uJAAA,CAAA,YAAS,AAAD,EAAE;wBACvB,IAAI,UAAU,OAAO,SAAS,IAAI,MAAM;4BACtC,IAAI,YAAY,OAAO,SAAS,IAAI,QAAQ,gBAAgB,CAAC,OAAO,WAAW;4BAC/E,SAAS,aAAa,UAAU,aAAa,CAAC,OAAO,SAAS,EAAE,OAAO,QAAQ,EAAE,OAAO,CAAC;4BACzF,OAAO;wBACT,OAEK,IAAI,OAAO,SAAS,EAAE;4BACzB,SAAS,CAAA,GAAA,iJAAA,CAAA,SAAM,AAAD,EAAE,CAAC,GAAG,OAAO,SAAS;4BACpC,OAAO;wBACT;oBACF,GAAG;gBACL;gBACA,+CAA+C;gBAC/C,sCAAsC;gBACtC,IAAI;gBACJ,iDAAiD;gBACjD,4BAA4B;gBAC5B,IAAI;gBACJ,sCAAsC;gBACtC,IAAI,QAAQ;oBACV,IAAI,gBAAgB,OAAO,aAAa;oBACxC,IAAI,iBAAiB,OAAO,cAAc;oBAC1C,wDAAwD;oBACxD,oDAAoD;oBACpD,0DAA0D;oBAC1D,wDAAwD;oBACxD,gCAAgC;oBAChC,IAAI,kBAAkB,cAAc,kBAAkB,eAAe,kBAAkB,YAAY;wBACjG,gBAAgB;wBAChB,iBAAiB,OAAO,WAAW;oBACrC;oBACA,IAAI,QAAQ,iBAAiB,kBAAkB,QAAQ,QAAQ,YAAY,CAAC,eAAe;oBAC3F,IAAI,OAAO,SAAS,KAAK,CAAC,MAAM,QAAQ,KAAK,WAAW,eAAe,iBAAiB,CAAC,MAAM,QAAQ,CAAC;oBACxG,wCAA2C;wBACzC,uEAAuE;wBACvE,wEAAwE;wBACxE,kCAAkC;wBAClC,IAAI,CAAC,eAAe,CAAC,CAAC,SAAS,IAAI,GAAG;4BACpC,CAAA,GAAA,gJAAA,CAAA,OAAI,AAAD,EAAE;wBACP;oBACF;oBACA,OAAO,KAAK,GAAG;oBACf,OAAO,IAAI,GAAG;oBACd,MAAM,gBAAgB,CAAC,SAAS,GAAG;wBACjC,UAAU;wBACV,aAAa;wBACb,OAAO;wBACP,MAAM;oBACR;oBACA,MAAM,OAAO,CAAC,SAAS;gBACzB;YACF;YACA,0DAA0D;YAC1D,0DAA0D;YAC1D,6DAA6D;YAC7D,wDAAwD;YACxD,+DAA+D;YAC/D,QAAQ,oBAAoB,GAAG;YAC/B,MAAM,GAAG,CAAC,EAAE,CAAC,SAAS,SAAS;QACjC;QACA,CAAA,GAAA,iJAAA,CAAA,OAAI,AAAD,EAAE,gBAAgB,SAAU,UAAU,EAAE,SAAS;YAClD,MAAM,cAAc,CAAC,EAAE,CAAC,WAAW,SAAU,KAAK;gBAChD,IAAI,CAAC,OAAO,CAAC,WAAW;YAC1B,GAAG;QACL;QACA,eAAe;QACf,iBAAiB;QACjB,CAAA,GAAA,iJAAA,CAAA,OAAI,AAAD,EAAE;YAAC;SAAgB,EAAE,SAAU,SAAS;YACzC,MAAM,cAAc,CAAC,EAAE,CAAC,WAAW,SAAU,KAAK;gBAChD,IAAI,CAAC,OAAO,CAAC,WAAW;YAC1B,GAAG;QACL;QACA,CAAA,GAAA,+JAAA,CAAA,2BAAwB,AAAD,EAAE,IAAI,CAAC,cAAc,EAAE,IAAI,EAAE,IAAI,CAAC,IAAI;IAC/D;IACA,QAAQ,SAAS,CAAC,UAAU,GAAG;QAC7B,OAAO,IAAI,CAAC,SAAS;IACvB;IACA,QAAQ,SAAS,CAAC,KAAK,GAAG;QACxB,IAAI,IAAI,CAAC,SAAS,EAAE;YAClB,gBAAgB,IAAI,CAAC,EAAE;YACvB;QACF;QACA,IAAI,CAAC,SAAS,CAAC;YACb,QAAQ,EAAE;QACZ,GAAG;IACL;IACA,QAAQ,SAAS,CAAC,OAAO,GAAG;QAC1B,IAAI,IAAI,CAAC,SAAS,EAAE;YAClB,gBAAgB,IAAI,CAAC,EAAE;YACvB;QACF;QACA,IAAI,CAAC,SAAS,GAAG;QACjB,IAAI,MAAM,IAAI,CAAC,MAAM;QACrB,IAAI,KAAK;YACP,CAAA,GAAA,kJAAA,CAAA,eAAsB,AAAD,EAAE,IAAI,CAAC,MAAM,IAAI,mBAAmB;QAC3D;QACA,IAAI,QAAQ,IAAI;QAChB,IAAI,MAAM,MAAM,IAAI;QACpB,IAAI,UAAU,MAAM,MAAM;QAC1B,CAAA,GAAA,iJAAA,CAAA,OAAI,AAAD,EAAE,MAAM,gBAAgB,EAAE,SAAU,SAAS;YAC9C,UAAU,OAAO,CAAC,SAAS;QAC7B;QACA,CAAA,GAAA,iJAAA,CAAA,OAAI,AAAD,EAAE,MAAM,YAAY,EAAE,SAAU,KAAK;YACtC,MAAM,OAAO,CAAC,SAAS;QACzB;QACA,mCAAmC;QACnC,MAAM,GAAG,CAAC,OAAO;QACjB,0BAA0B;QAC1B,yFAAyF;QACzF,MAAM,IAAI,GAAG,MAAM,MAAM,GAAG,MAAM,UAAU,GAAG,MAAM,cAAc,GAAG,MAAM,YAAY,GAAG,MAAM,gBAAgB,GAAG,MAAM,UAAU,GAAG,MAAM,IAAI,GAAG,MAAM,GAAG,GAAG,MAAM,iBAAiB,GAAG,MAAM,MAAM,GAAG,MAAM,YAAY,GAAG,MAAM,cAAc,GAAG;QACrP,OAAO,SAAS,CAAC,MAAM,EAAE,CAAC;IAC5B;IACA;;GAEC,GACD,QAAQ,SAAS,CAAC,MAAM,GAAG,SAAU,IAAI;QACvC,IAAI,IAAI,CAAC,oBAAoB,EAAE;YAC7B,wCAA2C;gBACzC,CAAA,GAAA,gJAAA,CAAA,QAAK,AAAD,EAAE;YACR;YACA;QACF;QACA,IAAI,IAAI,CAAC,SAAS,EAAE;YAClB,gBAAgB,IAAI,CAAC,EAAE;YACvB;QACF;QACA,IAAI,CAAC,GAAG,CAAC,MAAM,CAAC;QAChB,IAAI,UAAU,IAAI,CAAC,MAAM;QACzB,wBAAwB;QACxB,IAAI,CAAC,UAAU,IAAI,IAAI,CAAC,UAAU,CAAC,MAAM;QACzC,IAAI,CAAC,SAAS;YACZ;QACF;QACA,IAAI,cAAc,QAAQ,WAAW,CAAC;QACtC,IAAI,SAAS,QAAQ,KAAK,MAAM;QAChC,iCAAiC;QACjC,iDAAiD;QACjD,kBAAkB;QAClB,IAAI,IAAI,CAAC,eAAe,EAAE;YACxB,IAAI,UAAU,MAAM;gBAClB,SAAS,IAAI,CAAC,eAAe,CAAC,MAAM;YACtC;YACA,cAAc;YACd,IAAI,CAAC,eAAe,GAAG;QACzB;QACA,IAAI,CAAC,oBAAoB,GAAG;QAC5B,IAAI;YACF,eAAe,QAAQ,IAAI;YAC3B,cAAc,MAAM,CAAC,IAAI,CAAC,IAAI,EAAE;gBAC9B,MAAM;gBACN,WAAW,CAAA,GAAA,iJAAA,CAAA,SAAM,AAAD,EAAE;oBAChB,oBAAoB;oBACpB,UAAU;gBACZ,GAAG,QAAQ,KAAK,SAAS;YAC3B;QACF,EAAE,OAAO,GAAG;YACV,IAAI,CAAC,oBAAoB,GAAG;YAC5B,MAAM;QACR;QACA,IAAI,CAAC,oBAAoB,GAAG;QAC5B,oBAAoB,IAAI,CAAC,IAAI,EAAE;QAC/B,oBAAoB,IAAI,CAAC,IAAI,EAAE;IACjC;IACA,QAAQ,SAAS,CAAC,WAAW,GAAG,SAAU,IAAI,EAAE,GAAG;QACjD,IAAI,IAAI,CAAC,SAAS,EAAE;YAClB,gBAAgB,IAAI,CAAC,EAAE;YACvB;QACF;QACA,IAAI,CAAA,GAAA,iJAAA,CAAA,WAAQ,AAAD,EAAE,OAAO;YAClB,MAAM;YACN,OAAO;QACT;QACA,OAAO,QAAQ;QACf,IAAI,CAAC,WAAW;QAChB,IAAI,CAAC,cAAc,CAAC,KAAK,EAAE;YACzB,wCAA2C;gBACzC,CAAA,GAAA,gJAAA,CAAA,OAAI,AAAD,EAAE,qBAAqB,OAAO;YACnC;YACA;QACF;QACA,IAAI,KAAK,cAAc,CAAC,KAAK,CAAC,IAAI,CAAC,IAAI,EAAE;QACzC,IAAI,KAAK,IAAI,CAAC,GAAG;QACjB,IAAI,CAAC,UAAU,GAAG;QAClB,GAAG,GAAG,CAAC;IACT;IACA;;GAEC,GACD,QAAQ,SAAS,CAAC,WAAW,GAAG;QAC9B,IAAI,IAAI,CAAC,SAAS,EAAE;YAClB,gBAAgB,IAAI,CAAC,EAAE;YACvB;QACF;QACA,IAAI,CAAC,UAAU,IAAI,IAAI,CAAC,GAAG,CAAC,MAAM,CAAC,IAAI,CAAC,UAAU;QAClD,IAAI,CAAC,UAAU,GAAG;IACpB;IACA,QAAQ,SAAS,CAAC,mBAAmB,GAAG,SAAU,QAAQ;QACxD,IAAI,UAAU,CAAA,GAAA,iJAAA,CAAA,SAAM,AAAD,EAAE,CAAC,GAAG;QACzB,QAAQ,IAAI,GAAG,cAAc,CAAC,SAAS,IAAI,CAAC;QAC5C,OAAO;IACT;IACA;;;;;;;;GAQC,GACD,QAAQ,SAAS,CAAC,cAAc,GAAG,SAAU,OAAO,EAAE,GAAG;QACvD,IAAI,IAAI,CAAC,SAAS,EAAE;YAClB,gBAAgB,IAAI,CAAC,EAAE;YACvB;QACF;QACA,IAAI,CAAC,CAAA,GAAA,iJAAA,CAAA,WAAQ,AAAD,EAAE,MAAM;YAClB,MAAM;gBACJ,QAAQ,CAAC,CAAC;YACZ;QACF;QACA,IAAI,CAAC,OAAO,CAAC,QAAQ,IAAI,CAAC,EAAE;YAC1B;QACF;QACA,mEAAmE;QACnE,IAAI,CAAC,IAAI,CAAC,MAAM,EAAE;YAChB;QACF;QACA,4CAA4C;QAC5C,IAAI,IAAI,CAAC,oBAAoB,EAAE;YAC7B,IAAI,CAAC,eAAe,CAAC,IAAI,CAAC;YAC1B;QACF;QACA,IAAI,SAAS,IAAI,MAAM;QACvB,iBAAiB,IAAI,CAAC,IAAI,EAAE,SAAS;QACrC,IAAI,QAAQ,IAAI,KAAK;QACrB,IAAI,OAAO;YACT,IAAI,CAAC,GAAG,CAAC,KAAK;QAChB,OAAO,IAAI,UAAU,SAAS,gJAAA,CAAA,UAAG,CAAC,OAAO,CAAC,MAAM,EAAE;YAChD,wEAAwE;YACxE,wEAAwE;YACxE,kEAAkE;YAClE,sEAAsE;YACtE,6DAA6D;YAC7D,IAAI,CAAC,iBAAiB;QACxB;QACA,oBAAoB,IAAI,CAAC,IAAI,EAAE;QAC/B,oBAAoB,IAAI,CAAC,IAAI,EAAE;IACjC;IACA,QAAQ,SAAS,CAAC,iBAAiB,GAAG;QACpC,sJAAA,CAAA,UAAS,CAAC,OAAO,CAAC,uBAAuB,IAAI,CAAC,MAAM,EAAE,IAAI,CAAC,IAAI,EAAE;YAC/D,4BAA4B;YAC5B,OAAO;YACP,eAAe,EAAE;QACnB;IACF;IACA,QAAQ,SAAS,CAAC,UAAU,GAAG,SAAU,MAAM;QAC7C,IAAI,IAAI,CAAC,SAAS,EAAE;YAClB,gBAAgB,IAAI,CAAC,EAAE;YACvB;QACF;QACA,IAAI,cAAc,OAAO,WAAW;QACpC,IAAI,UAAU,IAAI,CAAC,QAAQ;QAC3B,IAAI,cAAc,QAAQ,gBAAgB,CAAC;QAC3C,wCAA2C;YACzC,CAAA,GAAA,iJAAA,CAAA,SAAM,AAAD,EAAE,OAAO,IAAI,IAAI;QACxB;QACA,YAAY,UAAU,CAAC;QACvB,uEAAuE;QACvE,oEAAoE;QACpE,sEAAsE;QACtE,qEAAqE;QACrE,qEAAqE;QACrE,+DAA+D;QAC/D,4BAA4B;QAC5B,IAAI,CAAC,UAAU,CAAC,UAAU,GAAG;QAC7B,IAAI,CAAC,KAAK,GAAG,MAAM;IACrB;IACA,yDAAyD;IACzD,qDAAqD;IACrD,QAAQ,aAAa,GAAG;QACtB,UAAU,SAAU,KAAK;YACvB,IAAI,YAAY,MAAM,UAAU;YAChC,UAAU,gBAAgB,CAAC,MAAM,MAAM;YACvC,UAAU,iBAAiB;YAC3B,YAAY,OAAO;YACnB,YAAY,OAAO;YACnB,UAAU,IAAI;QAChB;QACA;;KAEC,GACD,cAAc,SAAU,KAAK,EAAE,WAAW;YACxC,IAAI,UAAU,MAAM,MAAM;YAC1B,IAAI,YAAY,MAAM,UAAU;YAChC,IAAI,WAAW,cAAc,MAAM,gBAAgB,GAAG,MAAM,YAAY;YACxE,IAAI,UAAU,cAAc,MAAM,cAAc,GAAG,MAAM,UAAU;YACnE,IAAI,KAAK,MAAM,GAAG;YAClB,IAAI,MAAM,MAAM,IAAI;YACpB,IAAK,IAAI,IAAI,GAAG,IAAI,SAAS,MAAM,EAAE,IAAK;gBACxC,QAAQ,CAAC,EAAE,CAAC,OAAO,GAAG;YACxB;YACA,cAAc,QAAQ,aAAa,CAAC,SAAU,aAAa,EAAE,KAAK;gBAChE,kBAAkB,YAAY,UAAU;YAC1C,KAAK,QAAQ,UAAU,CAAC;YACxB,SAAS,UAAU,KAAK;gBACtB,2FAA2F;gBAC3F,yFAAyF;gBACzF,wFAAwF;gBACxF,8DAA8D;gBAC9D,4FAA4F;gBAC5F,sDAAsD;gBACtD,IAAI,iBAAiB,MAAM,gBAAgB;gBAC3C,sCAAsC;gBACtC,MAAM,gBAAgB,GAAG;gBACzB,sCAAsC;gBACtC,IAAI,SAAS,SAAS,MAAM,EAAE,GAAG,MAAM,MAAM,IAAI;gBACjD,IAAI,OAAO,CAAC,kBAAkB,OAAO,CAAC,OAAO;gBAC7C,IAAI,CAAC,MAAM;oBACT,IAAI,YAAY,CAAA,GAAA,kJAAA,CAAA,iBAAc,AAAD,EAAE,MAAM,IAAI;oBACzC,IAAI,QAAQ,cAAc,sJAAA,CAAA,UAAa,CAAC,QAAQ,CAAC,UAAU,IAAI,EAAE,UAAU,GAAG,IAC9E,WAAW;oBACX,wEAAwE;oBACxE,2EAA2E;oBAC3E,uDAAuD;oBACvD,+CAA+C;oBAC/C,kJAAA,CAAA,UAAS,CAAC,QAAQ,CAAC,UAAU,GAAG;oBAChC,wCAA2C;wBACzC,CAAA,GAAA,iJAAA,CAAA,SAAM,AAAD,EAAE,OAAO,UAAU,GAAG,GAAG;oBAChC;oBACA,OAAO,IAAI;oBACX,KAAK,IAAI,CAAC,SAAS;oBACnB,OAAO,CAAC,OAAO,GAAG;oBAClB,SAAS,IAAI,CAAC;oBACd,GAAG,GAAG,CAAC,KAAK,KAAK;gBACnB;gBACA,MAAM,QAAQ,GAAG,KAAK,IAAI,GAAG;gBAC7B,KAAK,OAAO,GAAG;gBACf,KAAK,OAAO,GAAG;gBACf,KAAK,KAAK,CAAC,iBAAiB,GAAG;oBAC7B,UAAU,MAAM,QAAQ;oBACxB,OAAO,MAAM,cAAc;gBAC7B;gBACA,CAAC,eAAe,UAAU,WAAW,CAAC,MAAM,OAAO,SAAS;YAC9D;YACA,IAAK,IAAI,IAAI,GAAG,IAAI,SAAS,MAAM,EAAG;gBACpC,IAAI,OAAO,QAAQ,CAAC,EAAE;gBACtB,IAAI,CAAC,KAAK,OAAO,EAAE;oBACjB,CAAC,eAAe,KAAK,UAAU,CAAC,OAAO;oBACvC,GAAG,MAAM,CAAC,KAAK,KAAK;oBACpB,KAAK,OAAO,CAAC,SAAS;oBACtB,SAAS,MAAM,CAAC,GAAG;oBACnB,IAAI,OAAO,CAAC,KAAK,IAAI,CAAC,KAAK,MAAM;wBAC/B,OAAO,OAAO,CAAC,KAAK,IAAI,CAAC;oBAC3B;oBACA,KAAK,IAAI,GAAG,KAAK,KAAK,CAAC,iBAAiB,GAAG;gBAC7C,OAAO;oBACL;gBACF;YACF;QACF;QACA,iBAAiB,SAAU,KAAK,EAAE,MAAM,EAAE,OAAO,EAAE,QAAQ,EAAE,OAAO;YAClE,IAAI,UAAU,MAAM,MAAM;YAC1B,QAAQ,gBAAgB,CAAC;YACzB,YAAY;YACZ,IAAI,CAAC,UAAU;gBACb,QAAQ;gBACR,4DAA4D;gBAC5D,qCAAqC;gBACrC,CAAA,GAAA,iJAAA,CAAA,OAAI,AAAD,EAAE,EAAE,CAAC,MAAM,CAAC,MAAM,gBAAgB,EAAE,MAAM,CAAC,MAAM,YAAY,GAAG;gBACnE;YACF;YACA,IAAI,QAAQ,CAAC;YACb,KAAK,CAAC,WAAW,KAAK,GAAG,OAAO,CAAC,WAAW,KAAK;YACjD,KAAK,CAAC,WAAW,QAAQ,GAAG,OAAO,CAAC,WAAW,QAAQ;YACvD,KAAK,CAAC,WAAW,OAAO,GAAG,OAAO,CAAC,WAAW,OAAO;YACrD,IAAI,YAAY;gBACd,UAAU;gBACV,OAAO;YACT;YACA,WAAW,CAAC,UAAU,OAAO,GAAG,OAAO,GAAG,uCAAuC;YACjF,IAAI,kBAAkB,QAAQ,eAAe;YAC7C,IAAI;YACJ,IAAI,mBAAmB,MAAM;gBAC3B,qBAAqB,CAAA,GAAA,iJAAA,CAAA,gBAAa,AAAD;gBACjC,CAAA,GAAA,iJAAA,CAAA,OAAI,AAAD,EAAE,CAAA,GAAA,kJAAA,CAAA,mBAA0B,AAAD,EAAE,kBAAkB,SAAU,EAAE;oBAC5D,IAAI,UAAU,CAAA,GAAA,kJAAA,CAAA,sBAA6B,AAAD,EAAE,IAAI;oBAChD,IAAI,WAAW,MAAM;wBACnB,mBAAmB,GAAG,CAAC,SAAS;oBAClC;gBACF;YACF;YACA,kDAAkD;YAClD,WAAW,QAAQ,aAAa,CAAC,WAAW,SAAU,KAAK;gBACzD,IAAI,aAAa,sBAAsB,mBAAmB,GAAG,CAAC,MAAM,EAAE,KAAK;gBAC3E,IAAI,YAAY;oBACd;gBACF;;gBAEA,IAAI,CAAA,GAAA,mJAAA,CAAA,oBAAiB,AAAD,EAAE,UAAU;oBAC9B,IAAI,iBAAiB,oJAAA,CAAA,UAAW,EAAE;wBAChC,IAAI,QAAQ,IAAI,KAAK,mJAAA,CAAA,wBAAqB,IAAI,CAAC,QAAQ,OAAO,IAAI,CAAC,MAAM,GAAG,CAAC;4BAAC;4BAAY;yBAAW,GAAG;4BACtG,CAAA,GAAA,mJAAA,CAAA,iCAA8B,AAAD,EAAE,OAAO,SAAS,MAAM,IAAI;wBAC3D;oBACF,OAAO;wBACL,IAAI,KAAK,CAAA,GAAA,mJAAA,CAAA,mCAAgC,AAAD,EAAE,MAAM,QAAQ,EAAE,MAAM,cAAc,EAAE,QAAQ,IAAI,EAAE,MAAM,IAAI,GACtG,YAAY,GAAG,SAAS,EACxB,cAAc,GAAG,WAAW;wBAC9B,IAAI,QAAQ,IAAI,KAAK,mJAAA,CAAA,wBAAqB,IAAI,aAAa,CAAC,QAAQ,OAAO,EAAE;4BAC3E,CAAA,GAAA,mJAAA,CAAA,gBAAa,AAAD,EAAE,MAAM,QAAQ,EAAE,MAAM,cAAc,EAAE,MAAM,IAAI;wBAChE;wBACA,WAAW;wBACX,gEAAgE;wBAChE,mEAAmE;wBACnE,mCAAmC;wBACnC,IAAI,aAAa;4BACf,CAAA,GAAA,iJAAA,CAAA,OAAI,AAAD,EAAE,aAAa,SAAU,UAAU;gCACpC,QAAQ,IAAI,KAAK,mJAAA,CAAA,wBAAqB,GAAG,CAAA,GAAA,mJAAA,CAAA,gBAAa,AAAD,EAAE,cAAc,CAAA,GAAA,mJAAA,CAAA,gBAAa,AAAD,EAAE;4BACrF;wBACF;oBACF;gBACF,OAAO,IAAI,CAAA,GAAA,mJAAA,CAAA,wBAAqB,AAAD,EAAE,UAAU;oBACzC,WAAW;oBACX,IAAI,iBAAiB,oJAAA,CAAA,UAAW,EAAE;wBAChC,CAAA,GAAA,mJAAA,CAAA,6BAA0B,AAAD,EAAE,OAAO,SAAS,MAAM,IAAI;wBACrD,CAAA,GAAA,mJAAA,CAAA,+BAA4B,AAAD,EAAE;wBAC7B,mBAAmB;oBACrB;gBACF;YACF,GAAG;YACH,WAAW,QAAQ,aAAa,CAAC,WAAW,SAAU,KAAK;gBACzD,IAAI,aAAa,sBAAsB,mBAAmB,GAAG,CAAC,MAAM,EAAE,KAAK;gBAC3E,IAAI,YAAY;oBACd;gBACF;;gBAEA,SAAS,KAAK,CAAC,aAAa,WAAW,eAAe,iBAAiB,CAAC,MAAM,QAAQ,CAAC;YACzF,GAAG;YACH,SAAS,SAAS,IAAI;gBACpB,QAAQ,KAAK,OAAO,IAAI,IAAI,CAAC,OAAO,IAAI,IAAI,CAAC,OAAO,CAAC,KAAK,OAAO,EAAE,SAAS,MAAM,IAAI,EAAE;YAC1F;QACF;QACA,gBAAgB;YACd,kBAAkB,SAAU,OAAO;gBACjC,QAAQ,IAAI;gBACZ,cAAc,MAAM,CAAC,IAAI,CAAC,IAAI,EAAE,SAAS;oBACvC,sDAAsD;oBACtD,uBAAuB;oBACvB,wDAAwD;oBACxD,eAAe,QAAQ,SAAS,IAAI;gBACtC;YACF;YACA,QAAQ,SAAU,OAAO,EAAE,YAAY;gBACrC,IAAI,UAAU,IAAI,CAAC,MAAM;gBACzB,IAAI,MAAM,IAAI,CAAC,IAAI;gBACnB,IAAI,KAAK,IAAI,CAAC,GAAG;gBACjB,IAAI,cAAc,IAAI,CAAC,YAAY;gBACnC,IAAI,YAAY,IAAI,CAAC,UAAU;gBAC/B,0BAA0B;gBAC1B,IAAI,CAAC,SAAS;oBACZ;gBACF;gBACA,QAAQ,gBAAgB,CAAC;gBACzB,UAAU,WAAW,CAAC,SAAS;gBAC/B,UAAU,kBAAkB,CAAC;gBAC7B,OAAO;gBACP,2FAA2F;gBAC3F,0FAA0F;gBAC1F,2CAA2C;gBAC3C,uFAAuF;gBACvF,YAAY,MAAM,CAAC,SAAS;gBAC5B,UAAU,yBAAyB,CAAC,SAAS;gBAC7C,2EAA2E;gBAC3E,yEAAyE;gBACzE,kDAAkD;gBAClD,kBAAkB,IAAI,EAAE;gBACxB,+EAA+E;gBAC/E,gFAAgF;gBAChF,gFAAgF;gBAChF,0DAA0D;gBAC1D,YAAY,MAAM,CAAC,SAAS;gBAC5B,kBAAkB;gBAClB,UAAU,kBAAkB,CAAC,SAAS;gBACtC,OAAO,IAAI,EAAE,SAAS,KAAK,SAAS;gBACpC,iBAAiB;gBACjB,IAAI,kBAAkB,QAAQ,GAAG,CAAC,sBAAsB;gBACxD,IAAI,WAAW,QAAQ,GAAG,CAAC;gBAC3B,GAAG,kBAAkB,CAAC;gBACtB,uBAAuB;gBACvB,IAAI,YAAY,QAAQ,aAAa,QAAQ;oBAC3C,GAAG,WAAW,CAAC;gBACjB;gBACA,sJAAA,CAAA,UAAS,CAAC,OAAO,CAAC,eAAe,SAAS;YAC5C;YACA,iBAAiB,SAAU,OAAO;gBAChC,IAAI,QAAQ,IAAI;gBAChB,IAAI,UAAU,IAAI,CAAC,MAAM;gBACzB,IAAI,MAAM,IAAI,CAAC,IAAI;gBACnB,0BAA0B;gBAC1B,IAAI,CAAC,SAAS;oBACZ;gBACF;gBACA,QAAQ,gBAAgB,CAAC;gBACzB,0DAA0D;gBAC1D,IAAI,qBAAqB,EAAE;gBAC3B,QAAQ,aAAa,CAAC,SAAU,aAAa,EAAE,cAAc;oBAC3D,IAAI,kBAAkB,UAAU;wBAC9B;oBACF;oBACA,IAAI,gBAAgB,MAAM,uBAAuB,CAAC;oBAClD,IAAI,iBAAiB,cAAc,OAAO,EAAE;wBAC1C,IAAI,cAAc,eAAe,EAAE;4BACjC,IAAI,SAAS,cAAc,eAAe,CAAC,gBAAgB,SAAS,KAAK;4BACzE,UAAU,OAAO,MAAM,IAAI,mBAAmB,IAAI,CAAC;wBACrD,OAAO;4BACL,mBAAmB,IAAI,CAAC;wBAC1B;oBACF;gBACF;gBACA,IAAI,iBAAiB,CAAA,GAAA,iJAAA,CAAA,gBAAa,AAAD;gBACjC,QAAQ,UAAU,CAAC,SAAU,WAAW;oBACtC,IAAI,YAAY,MAAM,UAAU,CAAC,YAAY,QAAQ,CAAC;oBACtD,IAAI,UAAU,eAAe,EAAE;wBAC7B,IAAI,SAAS,UAAU,eAAe,CAAC,aAAa,SAAS,KAAK;wBAClE,UAAU,OAAO,MAAM,IAAI,eAAe,GAAG,CAAC,YAAY,GAAG,EAAE;oBACjE,OAAO;wBACL,eAAe,GAAG,CAAC,YAAY,GAAG,EAAE;oBACtC;gBACF;gBACA,kBAAkB;gBAClB,8FAA8F;gBAC9F,wEAAwE;gBACxE,IAAI,CAAC,UAAU,CAAC,kBAAkB,CAAC,SAAS,SAAS;oBACnD,UAAU;oBACV,UAAU;gBACZ;gBACA,mEAAmE;gBACnE,sEAAsE;gBACtE,aAAa,IAAI,EAAE,SAAS,KAAK,SAAS,CAAC,GAAG;gBAC9C,sJAAA,CAAA,UAAS,CAAC,OAAO,CAAC,eAAe,SAAS;YAC5C;YACA,YAAY,SAAU,OAAO;gBAC3B,IAAI,UAAU,IAAI,CAAC,MAAM;gBACzB,0BAA0B;gBAC1B,IAAI,CAAC,SAAS;oBACZ;gBACF;gBACA,QAAQ,gBAAgB,CAAC;gBACzB,kJAAA,CAAA,UAAS,CAAC,gBAAgB,CAAC,SAAS;gBACpC,kBAAkB;gBAClB,8FAA8F;gBAC9F,IAAI,CAAC,UAAU,CAAC,kBAAkB,CAAC,SAAS,SAAS;oBACnD,UAAU;gBACZ;gBACA,OAAO,IAAI,EAAE,SAAS,IAAI,CAAC,IAAI,EAAE,SAAS,CAAC;gBAC3C,sJAAA,CAAA,UAAS,CAAC,OAAO,CAAC,eAAe,SAAS,IAAI,CAAC,IAAI;YACrD;YACA,cAAc,SAAU,OAAO;gBAC7B,4CAA4C;gBAC5C,IAAI,QAAQ,IAAI;gBAChB,IAAI,UAAU,IAAI,CAAC,MAAM;gBACzB,0BAA0B;gBAC1B,IAAI,CAAC,SAAS;oBACZ;gBACF;gBACA,QAAQ,gBAAgB,CAAC;gBACzB,mBAAmB;gBACnB,QAAQ,UAAU,CAAC,SAAU,WAAW;oBACtC,YAAY,OAAO,GAAG,cAAc;gBACtC;gBACA,iBAAiB;gBACjB,kJAAA,CAAA,UAAS,CAAC,gBAAgB,CAAC,SAAS;gBACpC,kBAAkB;gBAClB,8FAA8F;gBAC9F,IAAI,CAAC,UAAU,CAAC,kBAAkB,CAAC,SAAS,SAAS;oBACnD,YAAY;oBACZ,UAAU;gBACZ;gBACA,QAAQ,aAAa,CAAC,SAAU,aAAa,EAAE,cAAc;oBAC3D,IAAI,kBAAkB,UAAU;wBAC9B,IAAI,gBAAgB,MAAM,uBAAuB,CAAC;wBAClD,iBAAiB,cAAc,OAAO,IAAI,cAAc,YAAY,CAAC,gBAAgB,SAAS,MAAM,IAAI,EAAE;oBAC5G;gBACF;gBACA,QAAQ,UAAU,CAAC,SAAU,WAAW;oBACtC,IAAI,YAAY,MAAM,UAAU,CAAC,YAAY,QAAQ,CAAC;oBACtD,UAAU,YAAY,CAAC,aAAa,SAAS,MAAM,IAAI,EAAE;gBAC3D;gBACA,sJAAA,CAAA,UAAS,CAAC,OAAO,CAAC,eAAe,SAAS,IAAI,CAAC,IAAI;YACrD;YACA,cAAc,SAAU,OAAO;gBAC7B,cAAc,MAAM,CAAC,IAAI,CAAC,IAAI,EAAE;YAClC;QACF;QACA,iBAAiB,SAAU,KAAK,EAAE,UAAU,EAAE,MAAM,EAAE,KAAK;YACzD,IAAI,MAAM,SAAS,EAAE;gBACnB,gBAAgB,MAAM,EAAE;gBACxB;YACF;YACA,IAAI,UAAU,MAAM,MAAM;YAC1B,IAAI,eAAe,MAAM,YAAY,CAAC,oBAAoB;YAC1D,IAAI;YACJ,IAAI,eAAe,CAAA,GAAA,kJAAA,CAAA,cAAqB,AAAD,EAAE,SAAS;YAClD,IAAK,IAAI,IAAI,GAAG,IAAI,aAAa,MAAM,EAAE,IAAK;gBAC5C,IAAI,WAAW,YAAY,CAAC,EAAE;gBAC9B,IAAI,QAAQ,CAAC,WAAW,IAAI,CAAC,SAAS,QAAQ,CAAC,WAAW,CAAC,SAAS,cAAc,MAAM,KAAK,MAAM;oBACjG,OAAO;gBACT;YACF;YACA,wCAA2C;gBACzC,CAAA,GAAA,gJAAA,CAAA,OAAI,AAAD,EAAE,wCAAwC,aAAa;YAC5D;QACF;QACA,oBAAoB,SAAU,KAAK,EAAE,OAAO;YAC1C,IAAI,YAAY,MAAM,UAAU;YAChC,IAAI,YAAY,MAAM,UAAU;YAChC,QAAQ,UAAU,CAAC,SAAU,WAAW;gBACtC,UAAU,iBAAiB,CAAC,aAAa,SAAS,CAAC,YAAY,QAAQ,CAAC;YAC1E;QACF;QACA,mBAAmB,SAAU,OAAO,EAAE,MAAM;YAC1C,IAAI,QAAQ,IAAI;YAChB,IAAI,UAAU,IAAI,CAAC,QAAQ;YAC3B,IAAI,cAAc,QAAQ,IAAI;YAC9B,IAAI,gBAAgB,QAAQ,aAAa;YACzC,IAAI,aAAa,OAAO,CAAC,YAAY;YACrC,IAAI,aAAa,WAAW,UAAU;YACtC,IAAI,aAAa,CAAC,WAAW,MAAM,IAAI,QAAQ,EAAE,KAAK,CAAC;YACvD,IAAI,eAAe,WAAW,GAAG;YACjC,IAAI,UAAU,UAAU,CAAC,EAAE,IAAI,QAAQ,CAAA,GAAA,kJAAA,CAAA,iBAAc,AAAD,EAAE,UAAU,CAAC,EAAE;YACnE,IAAI,CAAC,oBAAoB,GAAG;YAC5B,IAAI,WAAW;gBAAC;aAAQ;YACxB,IAAI,UAAU;YACd,eAAe;YACf,IAAI,QAAQ,KAAK,EAAE;gBACjB,UAAU;gBACV,WAAW,CAAA,GAAA,iJAAA,CAAA,MAAG,AAAD,EAAE,QAAQ,KAAK,EAAE,SAAU,IAAI;oBAC1C,OAAO,CAAA,GAAA,iJAAA,CAAA,WAAQ,AAAD,EAAE,CAAA,GAAA,iJAAA,CAAA,SAAM,AAAD,EAAE,CAAC,GAAG,OAAO;oBAClC,KAAK,KAAK,GAAG;oBACb,OAAO;gBACT;YACF;YACA,IAAI,gBAAgB,EAAE;YACtB,IAAI;YACJ,IAAI,iBAAiB,CAAA,GAAA,mJAAA,CAAA,wBAAqB,AAAD,EAAE;YAC3C,IAAI,aAAa,CAAA,GAAA,mJAAA,CAAA,oBAAiB,AAAD,EAAE;YACnC,sDAAsD;YACtD,IAAI,YAAY;gBACd,CAAA,GAAA,mJAAA,CAAA,eAAY,AAAD,EAAE,IAAI,CAAC,IAAI;YACxB;YACA,CAAA,GAAA,iJAAA,CAAA,OAAI,AAAD,EAAE,UAAU,SAAU,SAAS;gBAChC,6CAA6C;gBAC7C,WAAW,WAAW,MAAM,CAAC,WAAW,MAAM,MAAM,EAAE,MAAM,IAAI;gBAChE,qBAAqB;gBACrB,WAAW,YAAY,CAAA,GAAA,iJAAA,CAAA,SAAM,AAAD,EAAE,CAAC,GAAG;gBAClC,4BAA4B;gBAC5B,SAAS,IAAI,GAAG,WAAW,KAAK,IAAI,SAAS,IAAI;gBACjD,cAAc,IAAI,CAAC;gBACnB,iEAAiE;gBACjE,IAAI,YAAY;oBACd,IAAI,KAAK,CAAA,GAAA,kJAAA,CAAA,iBAAwB,AAAD,EAAE,UAChC,iBAAiB,GAAG,cAAc,EAClC,oBAAoB,GAAG,iBAAiB;oBAC1C,IAAI,oBAAoB,oBAAoB,eAAe,IAAI,EAAE,CAAC,EAAE,GAAG;oBACvE,eAAe,OAAO,cAAc,WAAW;oBAC/C,mBAAmB;gBACrB,OAAO,IAAI,gBAAgB;oBACzB,uFAAuF;oBACvF,6BAA6B;oBAC7B,eAAe,OAAO,cAAc,WAAW;oBAC/C,mBAAmB;gBACrB,OAAO,IAAI,SAAS;oBAClB,eAAe,OAAO,cAAc,WAAW,QAAQ,IAAI,EAAE,QAAQ,GAAG;gBAC1E;YACF;YACA,IAAI,iBAAiB,UAAU,CAAC,cAAc,CAAC,kBAAkB,CAAC,SAAS;gBACzE,IAAI;oBACF,cAAc;oBACd,IAAI,IAAI,CAAC,eAAe,EAAE;wBACxB,QAAQ,IAAI;wBACZ,cAAc,MAAM,CAAC,IAAI,CAAC,IAAI,EAAE;wBAChC,IAAI,CAAC,eAAe,GAAG;oBACzB,OAAO;wBACL,aAAa,CAAC,aAAa,CAAC,IAAI,CAAC,IAAI,EAAE;oBACzC;gBACF,EAAE,OAAO,GAAG;oBACV,IAAI,CAAC,oBAAoB,GAAG;oBAC5B,MAAM;gBACR;YACF;YACA,kCAAkC;YAClC,IAAI,SAAS;gBACX,WAAW;oBACT,MAAM,WAAW,KAAK,IAAI;oBAC1B,eAAe;oBACf,OAAO;gBACT;YACF,OAAO;gBACL,WAAW,aAAa,CAAC,EAAE;YAC7B;YACA,IAAI,CAAC,oBAAoB,GAAG;YAC5B,IAAI,CAAC,QAAQ;gBACX,IAAI,gBAAgB,IAAI,CAAC,cAAc;gBACvC,cAAc,OAAO,CAAC,SAAS,IAAI,EAAE;gBACrC,wCAAwC;gBACxC,IAAI,gBAAgB;oBAClB,IAAI,SAAS;wBACX,MAAM;wBACN,eAAe;wBACf,UAAU,CAAA,GAAA,mJAAA,CAAA,wBAAqB,AAAD,EAAE;wBAChC,aAAa,QAAQ,WAAW,IAAI;wBACpC,YAAY,QAAQ,IAAI;wBACxB,mBAAmB;oBACrB;oBACA,cAAc,OAAO,CAAC,OAAO,IAAI,EAAE;gBACrC;YACF;QACF;QACA,sBAAsB,SAAU,MAAM;YACpC,IAAI,iBAAiB,IAAI,CAAC,eAAe;YACzC,MAAO,eAAe,MAAM,CAAE;gBAC5B,IAAI,UAAU,eAAe,KAAK;gBAClC,iBAAiB,IAAI,CAAC,IAAI,EAAE,SAAS;YACvC;QACF;QACA,sBAAsB,SAAU,MAAM;YACpC,CAAC,UAAU,IAAI,CAAC,OAAO,CAAC;QAC1B;QACA;;;;;;;;;;;KAWC,GACD,oBAAoB,SAAU,EAAE,EAAE,KAAK;YACrC,GAAG,EAAE,CAAC,YAAY,SAAU,MAAM;gBAChC,MAAM,OAAO,CAAC,YAAY;gBAC1B,2DAA2D;gBAC3D,+DAA+D;gBAC/D,4DAA4D;gBAC5D,+BAA+B;gBAC/B,IACA,4DAA4D;gBAC5D,sDAAsD;gBACtD,qCAAqC;gBACrC,GAAG,SAAS,CAAC,UAAU,MAAM,CAAC,KAAK,CAAC,eAAe,IAAI,CAAC,MAAM,UAAU,CAAC,UAAU,IAAI,CAAC,MAAM,eAAe,CAAC,MAAM,EAAE;oBACpH,MAAM,OAAO,CAAC;gBAChB;YACF;QACF;QACA,iBAAiB,SAAU,EAAE,EAAE,KAAK;YAClC,GAAG,EAAE,CAAC,aAAa,SAAU,CAAC;gBAC5B,IAAI,KAAK,EAAE,MAAM;gBACjB,IAAI,aAAa,CAAA,GAAA,kJAAA,CAAA,sBAAmB,AAAD,EAAE,IAAI,mJAAA,CAAA,uBAAoB;gBAC7D,IAAI,YAAY;oBACd,CAAA,GAAA,mJAAA,CAAA,mCAAgC,AAAD,EAAE,YAAY,GAAG,MAAM,IAAI;oBAC1D,mBAAmB;gBACrB;YACF,GAAG,EAAE,CAAC,YAAY,SAAU,CAAC;gBAC3B,IAAI,KAAK,EAAE,MAAM;gBACjB,IAAI,aAAa,CAAA,GAAA,kJAAA,CAAA,sBAAmB,AAAD,EAAE,IAAI,mJAAA,CAAA,uBAAoB;gBAC7D,IAAI,YAAY;oBACd,CAAA,GAAA,mJAAA,CAAA,kCAA+B,AAAD,EAAE,YAAY,GAAG,MAAM,IAAI;oBACzD,mBAAmB;gBACrB;YACF,GAAG,EAAE,CAAC,SAAS,SAAU,CAAC;gBACxB,IAAI,KAAK,EAAE,MAAM;gBACjB,IAAI,aAAa,CAAA,GAAA,kJAAA,CAAA,sBAAmB,AAAD,EAAE,IAAI,SAAU,MAAM;oBACvD,OAAO,CAAA,GAAA,uJAAA,CAAA,YAAS,AAAD,EAAE,QAAQ,SAAS,IAAI;gBACxC,GAAG;gBACH,IAAI,YAAY;oBACd,IAAI,aAAa,WAAW,QAAQ,GAAG,aAAa;oBACpD,IAAI,SAAS,CAAA,GAAA,uJAAA,CAAA,YAAS,AAAD,EAAE;oBACvB,MAAM,IAAI,CAAC,cAAc,CAAC;wBACxB,MAAM;wBACN,UAAU,OAAO,QAAQ;wBACzB,iBAAiB,OAAO,SAAS;wBACjC,aAAa,OAAO,WAAW;wBAC/B,aAAa;oBACf;gBACF;YACF;QACF;QACA,SAAS,kBAAkB,OAAO;YAChC,QAAQ,iBAAiB;YACzB,QAAQ,UAAU,CAAC,SAAU,WAAW;gBACtC,YAAY,iBAAiB;YAC/B;QACF;;QAEA,6CAA6C;QAC7C,SAAS,gBAAgB,OAAO;;YAE9B,IAAI,mBAAmB,EAAE;YACzB,IAAI,gBAAgB,EAAE;YACtB,IAAI,oBAAoB;YACxB,QAAQ,aAAa,CAAC,SAAU,aAAa,EAAE,cAAc;gBAC3D,IAAI,SAAS,eAAe,GAAG,CAAC,aAAa;gBAC7C,IAAI,IAAI,eAAe,GAAG,CAAC,QAAQ;gBACnC,IAAI,YAAY,eAAe,YAAY;gBAC3C,oBAAoB,qBAAqB,CAAC,CAAC;gBAC3C,CAAC,kBAAkB,WAAW,gBAAgB,gBAAgB,EAAE,IAAI,CAAC;oBACnE,QAAQ;oBACR,GAAG;oBACH,KAAK,eAAe,cAAc;oBAClC,MAAM;oBACN,KAAK;gBACP;YACF;YACA,IAAI,mBAAmB;gBACrB,yBAAyB;gBACzB,IAAI,UAAU,iBAAiB,MAAM,CAAC;gBACtC,IAAI;gBACJ,IAAI;gBACJ,CAAA,GAAA,oJAAA,CAAA,UAAO,AAAD,EAAE,SAAS,SAAU,CAAC,EAAE,CAAC;oBAC7B,IAAI,EAAE,MAAM,KAAK,EAAE,MAAM,EAAE;wBACzB,OAAO,EAAE,CAAC,GAAG,EAAE,CAAC;oBAClB;oBACA,OAAO,EAAE,MAAM,GAAG,EAAE,MAAM;gBAC5B;gBACA,CAAA,GAAA,iJAAA,CAAA,OAAI,AAAD,EAAE,SAAS,SAAU,IAAI;oBAC1B,IAAI,iBAAiB,QAAQ,YAAY,CAAC,KAAK,IAAI,EAAE,KAAK,GAAG;oBAC7D,IAAI,SAAS,KAAK,MAAM;oBACxB,IAAI,MAAM,KAAK,GAAG;oBAClB,IAAI,sBAAsB,MAAM;wBAC9B,SAAS,KAAK,GAAG,CAAC,oBAAoB;oBACxC;oBACA,IAAI,KAAK;wBACP,IAAI,WAAW,sBAAsB,QAAQ,iBAAiB;4BAC5D;wBACF;wBACA,kBAAkB;oBACpB,OAAO,IAAI,iBAAiB;wBAC1B,IAAI,WAAW,oBAAoB;4BACjC;wBACF;wBACA,kBAAkB;oBACpB;oBACA,qBAAqB;oBACrB,eAAe,SAAS,CAAC;gBAC3B;YACF;QACF;QACA,SAAS,SAAU,KAAK,EAAE,OAAO,EAAE,GAAG,EAAE,OAAO,EAAE,YAAY;YAC3D,gBAAgB;YAChB,iBAAiB,OAAO,SAAS,KAAK,SAAS;YAC/C,CAAA,GAAA,iJAAA,CAAA,OAAI,AAAD,EAAE,MAAM,YAAY,EAAE,SAAU,KAAK;gBACtC,MAAM,OAAO,GAAG;YAClB;YACA,aAAa,OAAO,SAAS,KAAK,SAAS;YAC3C,qCAAqC;YACrC,CAAA,GAAA,iJAAA,CAAA,OAAI,AAAD,EAAE,MAAM,YAAY,EAAE,SAAU,KAAK;gBACtC,IAAI,CAAC,MAAM,OAAO,EAAE;oBAClB,MAAM,MAAM,CAAC,SAAS;gBACxB;YACF;QACF;QACA,mBAAmB,SAAU,KAAK,EAAE,OAAO,EAAE,GAAG,EAAE,OAAO,EAAE,YAAY,EAAE,SAAS;YAChF,CAAA,GAAA,iJAAA,CAAA,OAAI,AAAD,EAAE,aAAa,MAAM,gBAAgB,EAAE,SAAU,aAAa;gBAC/D,IAAI,iBAAiB,cAAc,OAAO;gBAC1C,YAAY,gBAAgB;gBAC5B,cAAc,MAAM,CAAC,gBAAgB,SAAS,KAAK;gBACnD,QAAQ,gBAAgB;gBACxB,aAAa,gBAAgB;YAC/B;QACF;QACA;;KAEC,GACD,eAAe,SAAU,KAAK,EAAE,OAAO,EAAE,GAAG,EAAE,OAAO,EAAE,YAAY,EAAE,QAAQ;YAC3E,oBAAoB;YACpB,IAAI,YAAY,MAAM,UAAU;YAChC,eAAe,CAAA,GAAA,iJAAA,CAAA,SAAM,AAAD,EAAE,gBAAgB,CAAC,GAAG;gBACxC,eAAe,QAAQ,SAAS;YAClC;YACA,oBAAoB;YACpB,sJAAA,CAAA,UAAS,CAAC,OAAO,CAAC,uBAAuB,SAAS,KAAK;YACvD,IAAI,aAAa;YACjB,QAAQ,UAAU,CAAC,SAAU,WAAW;gBACtC,IAAI,YAAY,MAAM,UAAU,CAAC,YAAY,QAAQ,CAAC;gBACtD,UAAU,OAAO,GAAG;gBACpB,IAAI,aAAa,UAAU,UAAU;gBACrC,UAAU,aAAa,CAAC,YAAY;gBACpC,yBAAyB;gBACzB,YAAY,aAAa;gBACzB,IAAI,YAAY,SAAS,GAAG,CAAC,YAAY,GAAG,GAAG;oBAC7C,WAAW,KAAK;gBAClB;gBACA,IAAI,WAAW,OAAO,CAAC,UAAU,cAAc,CAAC,cAAc;oBAC5D,aAAa;gBACf;gBACA,UAAU,KAAK,CAAC,MAAM,GAAG,CAAC,CAAC,YAAY,GAAG,CAAC;gBAC3C,uEAAuE;gBACvE,sEAAsE;gBACtE,gCAAgC;gBAChC,YAAY,aAAa;gBACzB,CAAA,GAAA,mJAAA,CAAA,+BAA4B,AAAD,EAAE;YAC/B;YACA,UAAU,UAAU,GAAG,cAAc,UAAU,UAAU;YACzD,sJAAA,CAAA,UAAS,CAAC,OAAO,CAAC,uBAAuB,SAAS,KAAK;YACvD,sCAAsC;YACtC,sJAAA,CAAA,UAAS,CAAC,OAAO,CAAC,qBAAqB,SAAS,KAAK;YACrD,QAAQ,UAAU,CAAC,SAAU,WAAW;gBACtC,IAAI,YAAY,MAAM,UAAU,CAAC,YAAY,QAAQ,CAAC;gBACtD,yDAAyD;gBACzD,QAAQ,aAAa;gBACrB,8CAA8C;gBAC9C,mDAAmD;gBACnD,aAAa,aAAa;YAC5B;YACA,qBAAqB;YACrB,uBAAuB,OAAO;YAC9B,sJAAA,CAAA,UAAS,CAAC,OAAO,CAAC,sBAAsB,SAAS,KAAK;QACxD;QACA,qBAAqB,SAAU,KAAK;YAClC,KAAK,CAAC,wBAAwB,GAAG;YACjC,yEAAyE;YACzE,MAAM,KAAK,GAAG,MAAM;QACtB;QACA,qBAAqB,SAAU,KAAK;YAClC,IAAI,CAAC,KAAK,CAAC,wBAAwB,EAAE;gBACnC;YACF;YACA,MAAM,KAAK,GAAG,OAAO,CAAC,QAAQ,CAAC,SAAU,EAAE;gBACzC,2DAA2D;gBAC3D,IAAI,CAAA,GAAA,iKAAA,CAAA,mBAAwB,AAAD,EAAE,KAAK;oBAChC;gBACF;gBACA,mBAAmB;YACrB;YACA,KAAK,CAAC,wBAAwB,GAAG;QACnC;QACA,SAAS,mBAAmB,EAAE;YAC5B,IAAI,YAAY,EAAE;YAClB,IAAI,YAAY,GAAG,aAAa;YAChC,qBAAqB;YACrB,IAAK,IAAI,IAAI,GAAG,IAAI,UAAU,MAAM,EAAE,IAAK;gBACzC,IAAI,YAAY,SAAS,CAAC,EAAE;gBAC5B,IAAI,CAAC,CAAC,cAAc,cAAc,cAAc,UAAU,cAAc,QAAQ,GAAG;oBACjF,UAAU,IAAI,CAAC;gBACjB;YACF;YACA,oCAAoC;YACpC,IAAI,GAAG,QAAQ,IAAI,GAAG,MAAM,CAAC,MAAM,EAAE;gBACnC,UAAU,IAAI,CAAC;YACjB;YACA,IAAI,GAAG,UAAU,KAAK,mJAAA,CAAA,uBAAoB,IAAI,GAAG,MAAM,CAAC,QAAQ,EAAE;gBAChE,UAAU,IAAI,CAAC;YACjB,OAAO,IAAI,GAAG,UAAU,KAAK,mJAAA,CAAA,mBAAgB,IAAI,GAAG,MAAM,CAAC,IAAI,EAAE;gBAC/D,UAAU,IAAI,CAAC;YACjB;YACA,GAAG,SAAS,CAAC;QACf;QACA,SAAS,uBAAuB,KAAK,EAAE,OAAO;YAC5C,IAAI,KAAK,MAAM,GAAG;YAClB,IAAI,UAAU,GAAG,OAAO;YACxB,IAAI,UAAU;YACd,QAAQ,QAAQ,CAAC,SAAU,EAAE;gBAC3B,IAAI,CAAC,GAAG,OAAO,EAAE;oBACf;gBACF;YACF;YACA,IAAI,UAAU,QAAQ,GAAG,CAAC,0BAA0B,CAAC,gJAAA,CAAA,UAAG,CAAC,IAAI,IAAI,CAAC,gJAAA,CAAA,UAAG,CAAC,MAAM,EAAE;gBAC5E,QAAQ,UAAU,CAAC,SAAU,WAAW;oBACtC,IAAI,YAAY,sBAAsB,EAAE;wBACtC;oBACF;oBACA,IAAI,YAAY,MAAM,UAAU,CAAC,YAAY,QAAQ,CAAC;oBACtD,IAAI,UAAU,OAAO,EAAE;wBACrB,UAAU,YAAY,CAAC,SAAU,EAAE;4BACjC,IAAI,GAAG,MAAM,CAAC,QAAQ,EAAE;gCACtB,GAAG,MAAM,CAAC,QAAQ,CAAC,UAAU,GAAG;4BAClC;wBACF;oBACF;gBACF;YACF;QACF;;QAEA;;KAEC,GACD,SAAS,YAAY,WAAW,EAAE,SAAS;YACzC,IAAI,YAAY,YAAY,GAAG,CAAC,gBAAgB;YAChD,UAAU,YAAY,CAAC,SAAU,EAAE;gBACjC,oCAAoC;gBACpC,IAAI,CAAC,GAAG,OAAO,EAAE;oBACf,2FAA2F;oBAC3F,GAAG,KAAK,CAAC,KAAK,GAAG;gBACnB;YACF;QACF;;QAEA,SAAS,QAAQ,KAAK,EAAE,IAAI;YAC1B,IAAI,MAAM,YAAY,EAAE;gBACtB;YACF;YACA,IAAI,IAAI,MAAM,GAAG,CAAC,QAAQ;YAC1B,IAAI,SAAS,MAAM,GAAG,CAAC,aAAa;YACpC,mBAAmB;YACnB,KAAK,YAAY,CAAC,SAAU,EAAE;gBAC5B,UAAU,IAAI,GAAG,QAAQ,CAAC;gBAC1B,yEAAyE;gBACzE,OAAO;YACT;QACF;;QAEA,SAAS,UAAU,EAAE,EAAE,CAAC,EAAE,MAAM,EAAE,KAAK;YACrC,kCAAkC;YAClC,IAAI,QAAQ,GAAG,cAAc;YAC7B,IAAI,YAAY,GAAG,gBAAgB;YACnC,IAAI,UAAU,GAAG,OAAO;YACxB,IAAI,SAAS;gBACX,+CAA+C;gBAC/C,IAAI,WAAW,GAAG,WAAW;gBAC7B,IAAK,IAAI,IAAI,GAAG,IAAI,SAAS,MAAM,EAAE,IAAK;oBACxC,QAAQ,KAAK,GAAG,CAAC,UAAU,QAAQ,CAAC,EAAE,EAAE,GAAG,QAAQ,QAAQ;gBAC7D;YACF,OAAO;gBACL,YAAY;gBACZ,GAAG,CAAC,GAAG;gBACP,GAAG,MAAM,GAAG;gBACZ,QAAQ,KAAK,GAAG,CAAC,GAAG,EAAE,EAAE;YAC1B;YACA,oDAAoD;YACpD,IAAI,OAAO;gBACT,MAAM,CAAC,GAAG;gBACV,MAAM,MAAM,GAAG;gBACf,0BAA0B;gBAC1B,8DAA8D;gBAC9D,SAAS,UAAU,CAAC,MAAM,EAAE,GAAG,QAAQ,CAAC;YAC1C;YACA,IAAI,WAAW;gBACb,IAAI,sBAAsB,GAAG,mBAAmB;gBAChD,UAAU,CAAC,GAAG;gBACd,UAAU,MAAM,GAAG;gBACnB,SAAS,UAAU,CAAC,UAAU,EAAE,GAAG,QAAQ,CAAC,uBAAuB,oBAAoB,SAAS,GAAG,IAAI,CAAC,CAAC,CAAC;YAC5G;YACA,OAAO;QACT;QACA,kCAAkC;QAClC,4BAA4B;QAC5B,SAAS,YAAY,KAAK,EAAE,IAAI;YAC9B,KAAK,YAAY,CAAC,SAAU,EAAE;gBAC5B,2DAA2D;gBAC3D,IAAI,CAAA,GAAA,iKAAA,CAAA,mBAAwB,AAAD,EAAE,KAAK;oBAChC;gBACF;gBACA,IAAI,cAAc,GAAG,cAAc;gBACnC,IAAI,YAAY,GAAG,gBAAgB;gBACnC,IAAI,GAAG,eAAe,EAAE;oBACtB,GAAG,eAAe,GAAG;gBACvB;gBACA,IAAI,eAAe,YAAY,eAAe,EAAE;oBAC9C,YAAY,eAAe,GAAG;gBAChC;gBACA,IAAI,aAAa,UAAU,eAAe,EAAE;oBAC1C,UAAU,eAAe,GAAG;gBAC9B;gBACA,6BAA6B;gBAC7B,IAAI,GAAG,QAAQ,IAAI;oBACjB,GAAG,UAAU,GAAG,GAAG,aAAa;oBAChC,GAAG,WAAW;gBAChB,OAAO,IAAI,GAAG,UAAU,EAAE;oBACxB,GAAG,UAAU,GAAG;gBAClB;YACF;QACF;QACA,SAAS,aAAa,KAAK,EAAE,IAAI;YAC/B,IAAI,sBAAsB,MAAM,QAAQ,CAAC;YACzC,IAAI,kBAAkB,MAAM,kBAAkB;YAC9C,IAAI,WAAW,oBAAoB,GAAG,CAAC;YACvC,IAAI,kBAAkB,WAAW,IAAI;gBACnC,UAAU;gBACV,OAAO,oBAAoB,GAAG,CAAC;gBAC/B,QAAQ,oBAAoB,GAAG,CAAC;YAElC,IAAI;YACJ,KAAK,YAAY,CAAC,SAAU,EAAE;gBAC5B,IAAI,GAAG,MAAM,IAAI,GAAG,MAAM,CAAC,QAAQ,EAAE;oBACnC,2DAA2D;oBAC3D,IAAI,CAAA,GAAA,iKAAA,CAAA,mBAAwB,AAAD,EAAE,KAAK;wBAChC;oBACF;oBACA,IAAI,cAAc,uLAAA,CAAA,OAAY,EAAE;wBAC9B,CAAA,GAAA,mJAAA,CAAA,iBAAc,AAAD,EAAE;oBACjB;oBACA,8FAA8F;oBAC9F,2BAA2B;oBAC3B,IAAI,GAAG,OAAO,EAAE;wBACd,IAAI,aAAa,GAAG,UAAU;wBAC9B,mCAAmC;wBACnC,IAAI,YAAY;4BACd,GAAG,SAAS,CAAC;wBACf;oBACF;oBACA,sDAAsD;oBACtD,IAAI,iBAAiB;wBACnB,GAAG,eAAe,GAAG;wBACrB,IAAI,cAAc,GAAG,cAAc;wBACnC,IAAI,YAAY,GAAG,gBAAgB;wBACnC,yCAAyC;wBACzC,IAAI,aAAa;4BACf,YAAY,eAAe,GAAG;wBAChC;wBACA,IAAI,WAAW;4BACb,UAAU,eAAe,GAAG;wBAC9B;oBACF;oBACA,sDAAsD;oBACtD,IAAI,GAAG,OAAO,EAAE;wBACd,mBAAmB;oBACrB;gBACF;YACF;QACF;;QAEA,qBAAqB,SAAU,KAAK;YAClC,OAAO,IAAI,CAAC,WAAW,GAAE,SAAU,MAAM;gBACvC,CAAA,GAAA,mKAAA,CAAA,YAAS,AAAD,EAAE,SAAS;gBACnB,SAAS;oBACP,OAAO,WAAW,QAAQ,OAAO,KAAK,CAAC,IAAI,EAAE,cAAc,IAAI;gBACjE;gBACA,QAAQ,SAAS,CAAC,oBAAoB,GAAG;oBACvC,OAAO,MAAM,YAAY,CAAC,oBAAoB;gBAChD;gBACA,QAAQ,SAAS,CAAC,qBAAqB,GAAG,SAAU,EAAE;oBACpD,MAAO,GAAI;wBACT,IAAI,YAAY,GAAG,iBAAiB;wBACpC,IAAI,aAAa,MAAM;4BACrB,OAAO,MAAM,MAAM,CAAC,YAAY,CAAC,UAAU,QAAQ,EAAE,UAAU,KAAK;wBACtE;wBACA,KAAK,GAAG,MAAM;oBAChB;gBACF;gBACA,QAAQ,SAAS,CAAC,aAAa,GAAG,SAAU,EAAE,EAAE,cAAc;oBAC5D,CAAA,GAAA,mJAAA,CAAA,gBAAa,AAAD,EAAE,IAAI;oBAClB,mBAAmB;gBACrB;gBACA,QAAQ,SAAS,CAAC,aAAa,GAAG,SAAU,EAAE,EAAE,cAAc;oBAC5D,CAAA,GAAA,mJAAA,CAAA,gBAAa,AAAD,EAAE,IAAI;oBAClB,mBAAmB;gBACrB;gBACA,QAAQ,SAAS,CAAC,SAAS,GAAG,SAAU,EAAE;oBACxC,CAAA,GAAA,mJAAA,CAAA,YAAS,AAAD,EAAE;oBACV,mBAAmB;gBACrB;gBACA,QAAQ,SAAS,CAAC,SAAS,GAAG,SAAU,EAAE;oBACxC,CAAA,GAAA,mJAAA,CAAA,YAAS,AAAD,EAAE;oBACV,mBAAmB;gBACrB;gBACA,QAAQ,SAAS,CAAC,WAAW,GAAG,SAAU,EAAE;oBAC1C,CAAA,GAAA,mJAAA,CAAA,cAAW,AAAD,EAAE;oBACZ,mBAAmB;gBACrB;gBACA,QAAQ,SAAS,CAAC,WAAW,GAAG,SAAU,EAAE;oBAC1C,CAAA,GAAA,mJAAA,CAAA,cAAW,AAAD,EAAE;oBACZ,mBAAmB;gBACrB;gBACA,QAAQ,SAAS,CAAC,QAAQ,GAAG;oBAC3B,OAAO,MAAM,QAAQ;gBACvB;gBACA,QAAQ,SAAS,CAAC,uBAAuB,GAAG,SAAU,cAAc;oBAClE,OAAO,MAAM,uBAAuB,CAAC;gBACvC;gBACA,QAAQ,SAAS,CAAC,oBAAoB,GAAG,SAAU,WAAW;oBAC5D,OAAO,MAAM,oBAAoB,CAAC;gBACpC;gBACA,OAAO;YACT,EAAE,yJAAA,CAAA,UAAY,CAAC,EAAE;QACnB;QACA,gBAAgB,SAAU,KAAK;YAC7B,SAAS,4BAA4B,MAAM,EAAE,MAAM;gBACjD,IAAK,IAAI,IAAI,GAAG,IAAI,OAAO,MAAM,EAAE,IAAK;oBACtC,IAAI,aAAa,MAAM,CAAC,EAAE;oBAC1B,UAAU,CAAC,mBAAmB,GAAG;gBACnC;YACF;YACA,CAAA,GAAA,iJAAA,CAAA,OAAI,AAAD,EAAE,gBAAgB,SAAU,UAAU,EAAE,SAAS;gBAClD,MAAM,cAAc,CAAC,EAAE,CAAC,WAAW,SAAU,KAAK;oBAChD,IAAI,eAAe,CAAC,MAAM,KAAK,CAAC,IAAI,KAAK,CAAC,mBAAmB,KAAK,wBAAwB;wBACxF,IAAI,SAAS,MAAM,aAAa,EAAE;4BAChC;wBACF;wBACA,IAAI,WAAW,MAAM,mBAAmB,CAAC;wBACzC,IAAI,gBAAgB,EAAE;wBACtB,CAAA,GAAA,iJAAA,CAAA,OAAI,AAAD,EAAE,WAAW,SAAU,UAAU;4BAClC,IAAI,eAAe,SAAS,WAAW,KAAK,KAAK,MAAM,KAAK,EAAE;gCAC5D,cAAc,IAAI,CAAC;4BACrB;wBACF;wBACA,4BAA4B,eAAe;wBAC3C,CAAA,GAAA,iJAAA,CAAA,OAAI,AAAD,EAAE,eAAe,SAAU,UAAU;4BACtC,IAAI,UAAU,CAAC,mBAAmB,KAAK,yBAAyB;gCAC9D,WAAW,cAAc,CAAC;4BAC5B;wBACF;wBACA,4BAA4B,eAAe;oBAC7C;gBACF;YACF;QACF;IACF;IACA,OAAO;AACT,EAAE,qJAAA,CAAA,UAAQ;AACV,IAAI,eAAe,QAAQ,SAAS;AACpC,aAAa,EAAE,GAAG,wCAAwC;AAC1D,aAAa,GAAG,GAAG,wCAAwC;AAC3D;;CAEC,GACD,aAAa;AACb,aAAa,GAAG,GAAG,SAAU,SAAS,EAAE,EAAE,EAAE,GAAG;IAC7C,IAAI,OAAO,IAAI;IACf,CAAA,GAAA,gJAAA,CAAA,eAAY,AAAD,EAAE;IACb,SAAS;QACP,IAAI,QAAQ,EAAE;QACd,IAAK,IAAI,KAAK,GAAG,KAAK,UAAU,MAAM,EAAE,KAAM;YAC5C,KAAK,CAAC,GAAG,GAAG,SAAS,CAAC,GAAG;QAC3B;QACA,MAAM,GAAG,KAAK,IAAI,GAAG,KAAK,CAAC,IAAI,EAAE;QACjC,aAAa;QACb,KAAK,GAAG,CAAC,WAAW;IACtB;;IAEA,aAAa;IACb,IAAI,CAAC,EAAE,CAAC,IAAI,CAAC,IAAI,EAAE,WAAW,SAAS;AACzC;AACA,IAAI,oBAAoB;IAAC;IAAS;IAAY;IAAa;IAAY;IAAa;IAAa;IAAW;IAAa;CAAc;AACvI,SAAS,gBAAgB,EAAE;IACzB,wCAA2C;QACzC,CAAA,GAAA,gJAAA,CAAA,OAAI,AAAD,EAAE,cAAc,KAAK;IAC1B;AACF;AACA,IAAI,UAAU,CAAC;AACf;;CAEC,GACD,IAAI,iBAAiB,CAAC;AACtB,IAAI,qBAAqB,EAAE;AAC3B,IAAI,0BAA0B,EAAE;AAChC,IAAI,cAAc,EAAE;AACpB,IAAI,eAAe,CAAC;AACpB,IAAI,iBAAiB,CAAC;AACtB,IAAI,YAAY,CAAC;AACjB,IAAI,kBAAkB,CAAC;AACvB,IAAI,SAAS,CAAC,IAAI,SAAS;AAC3B,IAAI,cAAc,CAAC,IAAI,SAAS;AAChC,IAAI,oBAAoB;AAWjB,SAAS,KAAK,GAAG,EAAE,KAAK,EAAE,IAAI;IACnC,IAAI,WAAW,CAAC,CAAC,QAAQ,KAAK,GAAG;IACjC,IAAI,UAAU;QACZ,wCAA2C;YACzC,IAAI,CAAC,KAAK;gBACR,MAAM,IAAI,MAAM;YAClB;QACF;QACA,IAAI,gBAAgB,iBAAiB;QACrC,IAAI,eAAe;YACjB,wCAA2C;gBACzC,CAAA,GAAA,gJAAA,CAAA,OAAI,AAAD,EAAE;YACP;YACA,OAAO;QACT;QACA,wCAA2C;YACzC,IAAI,CAAA,GAAA,iJAAA,CAAA,QAAK,AAAD,EAAE,QAAQ,IAAI,QAAQ,CAAC,WAAW,OAAO,YAAY,CAAC,CAAC,IAAI,WAAW,IAAI,CAAC,CAAC,QAAQ,KAAK,KAAK,IAAI,IAAI,KAAK,CAAC,IAAI,YAAY,IAAI,CAAC,CAAC,QAAQ,KAAK,MAAM,IAAI,IAAI,CAAC,GAAG;gBACvK,CAAA,GAAA,gJAAA,CAAA,OAAI,AAAD,EAAE,kDAAkD,gEAAgE,4DAA4D;YACrL;QACF;IACF;IACA,IAAI,QAAQ,IAAI,QAAQ,KAAK,OAAO;IACpC,MAAM,EAAE,GAAG,QAAQ;IACnB,SAAS,CAAC,MAAM,EAAE,CAAC,GAAG;IACtB,YAAY,CAAA,GAAA,kJAAA,CAAA,eAAsB,AAAD,EAAE,KAAK,mBAAmB,MAAM,EAAE;IACnE,cAAc;IACd,sJAAA,CAAA,UAAS,CAAC,OAAO,CAAC,aAAa;IAC/B,OAAO;AACT;AAkBO,SAAS,QAAQ,OAAO;IAC7B,qBAAqB;IACrB,IAAI,CAAA,GAAA,iJAAA,CAAA,UAAO,AAAD,EAAE,UAAU;QACpB,IAAI,SAAS;QACb,UAAU;QACV,yBAAyB;QACzB,CAAA,GAAA,iJAAA,CAAA,OAAI,AAAD,EAAE,QAAQ,SAAU,KAAK;YAC1B,IAAI,MAAM,KAAK,IAAI,MAAM;gBACvB,UAAU,MAAM,KAAK;YACvB;QACF;QACA,UAAU,WAAW,OAAO;QAC5B,CAAA,GAAA,iJAAA,CAAA,OAAI,AAAD,EAAE,QAAQ,SAAU,KAAK;YAC1B,MAAM,KAAK,GAAG;QAChB;IACF;IACA,eAAe,CAAC,QAAQ,GAAG;IAC3B,OAAO;AACT;AACO,SAAS,WAAW,OAAO;IAChC,eAAe,CAAC,QAAQ,GAAG;AAC7B;AAKO,IAAI,aAAa;AAIjB,SAAS,QAAQ,KAAK;IAC3B,IAAI,CAAA,GAAA,iJAAA,CAAA,WAAQ,AAAD,EAAE,QAAQ;QACnB,QAAQ,SAAS,CAAC,MAAM;IAC1B,OAAO,IAAI,CAAC,CAAC,iBAAiB,OAAO,GAAG;QACtC,sBAAsB;QACtB,QAAQ,iBAAiB;IAC3B;IACA,IAAI,iBAAiB,WAAW,CAAC,MAAM,UAAU,IAAI;QACnD,MAAM,OAAO;IACf;AACF;AACO,SAAS,iBAAiB,GAAG;IAClC,OAAO,SAAS,CAAC,CAAA,GAAA,kJAAA,CAAA,eAAsB,AAAD,EAAE,KAAK,mBAAmB;AAClE;AACO,SAAS,gBAAgB,GAAG;IACjC,OAAO,SAAS,CAAC,IAAI;AACvB;AAIO,SAAS,cAAc,IAAI,EAAE,KAAK;IACvC,YAAY,CAAC,KAAK,GAAG;AACvB;AAIO,SAAS,qBAAqB,gBAAgB;IACnD,IAAI,CAAA,GAAA,iJAAA,CAAA,UAAO,AAAD,EAAE,yBAAyB,oBAAoB,GAAG;QAC1D,wBAAwB,IAAI,CAAC;IAC/B;AACF;AACO,SAAS,kBAAkB,QAAQ,EAAE,SAAS;IACnD,kBAAkB,oBAAoB,UAAU,WAAW;AAC7D;AAKO,SAAS,iBAAiB,YAAY;IAC3C,wBAAwB,aAAa;AACvC;AAKO,SAAS,mBAAmB,cAAc;IAC/C,wBAAwB,eAAe;AACzC;AACO,SAAS,wBAAwB,IAAI,EAAE,EAAE;IAC9C,sJAAA,CAAA,UAAS,CAAC,EAAE,CAAC,MAAM;AACrB;AACO,SAAS,eAAe,UAAU,EAAE,SAAS,EAAE,MAAM;IAC1D,IAAI,CAAA,GAAA,iJAAA,CAAA,aAAU,AAAD,EAAE,YAAY;QACzB,SAAS;QACT,YAAY;IACd;IACA,IAAI,aAAa,CAAA,GAAA,iJAAA,CAAA,WAAQ,AAAD,EAAE,cAAc,WAAW,IAAI,GAAG;QAAC;QAAY,aAAa;YAClF,OAAO;QACT;KAAE,CAAC,EAAE;IACL,8BAA8B;IAC9B,WAAW,KAAK,GAAG,CAAC,WAAW,KAAK,IAAI,UAAU,EAAE,WAAW;IAC/D,YAAY,WAAW,KAAK;IAC5B,IAAI,cAAc,CAAC,UAAU,EAAE;QAC7B,sBAAsB;QACtB;IACF;IACA,uCAAuC;IACvC,CAAA,GAAA,iJAAA,CAAA,SAAM,AAAD,EAAE,WAAW,IAAI,CAAC,eAAe,WAAW,IAAI,CAAC;IACtD,IAAI,CAAC,OAAO,CAAC,WAAW,EAAE;QACxB,OAAO,CAAC,WAAW,GAAG;YACpB,QAAQ;YACR,YAAY;QACd;IACF;IACA,cAAc,CAAC,UAAU,GAAG;AAC9B;AACO,SAAS,yBAAyB,IAAI,EAAE,eAAe;IAC5D,6JAAA,CAAA,UAAuB,CAAC,QAAQ,CAAC,MAAM;AACzC;AAMO,SAAS,8BAA8B,IAAI;IAChD,IAAI,kBAAkB,6JAAA,CAAA,UAAuB,CAAC,GAAG,CAAC;IAClD,IAAI,iBAAiB;QACnB,OAAO,gBAAgB,iBAAiB,GAAG,gBAAgB,iBAAiB,KAAK,gBAAgB,UAAU,CAAC,KAAK;IACnH;AACF;;AAEA,SAAS,eAAe,QAAQ,EAAE,UAAU;IAC1C,kBAAkB,aAAa,UAAU,YAAY,wBAAwB;AAC/E;AACA,SAAS,eAAe,QAAQ,EAAE,UAAU;IAC1C,kBAAkB,aAAa,UAAU,YAAY,uBAAuB;AAC9E;;AAEA,IAAI,kBAAkB,EAAE;AACxB,SAAS,kBAAkB,UAAU,EAAE,QAAQ,EAAE,EAAE,EAAE,eAAe,EAAE,UAAU;IAC9E,IAAI,CAAA,GAAA,iJAAA,CAAA,aAAU,AAAD,EAAE,aAAa,CAAA,GAAA,iJAAA,CAAA,WAAQ,AAAD,EAAE,WAAW;QAC9C,KAAK;QACL,WAAW;IACb;IACA,wCAA2C;QACzC,IAAI,MAAM,aAAa,YAAY,MAAM;YACvC,MAAM,IAAI,MAAM;QAClB;QACA,kBAAkB;QAClB,CAAA,GAAA,iJAAA,CAAA,OAAI,AAAD,EAAE,YAAY,SAAU,IAAI;YAC7B,CAAA,GAAA,iJAAA,CAAA,SAAM,AAAD,EAAE,KAAK,KAAK,KAAK;QACxB;IACF;IACA,qBAAqB;IACrB,IAAI,CAAA,GAAA,iJAAA,CAAA,UAAO,AAAD,EAAE,iBAAiB,OAAO,GAAG;QACrC;IACF;IACA,gBAAgB,IAAI,CAAC;IACrB,IAAI,eAAe,sJAAA,CAAA,UAAS,CAAC,gBAAgB,CAAC,IAAI;IAClD,aAAa,MAAM,GAAG;IACtB,aAAa,KAAK,GAAG;IACrB,WAAW,IAAI,CAAC;AAClB;AACO,SAAS,gBAAgB,IAAI,EAAE,SAAS;IAC7C,cAAc,CAAC,KAAK,GAAG;AACzB;AAiBO,SAAS,iBAAiB,OAAO;IACtC,wCAA2C;QACzC,CAAA,GAAA,gJAAA,CAAA,eAAY,AAAD,EAAE;IACf;IACA,CAAA,GAAA,qJAAA,CAAA,iBAAc,AAAD,EAAE;QACb,cAAc;IAChB;AACF;AAKO,SAAS,YAAY,OAAO,EAAE,OAAO,EAAE,YAAY;IACxD,IAAI,cAAc,CAAA,GAAA,iJAAA,CAAA,UAAO,AAAD,EAAE;IAC1B,eAAe,YAAY,SAAS,SAAS;AAC/C;AACO,SAAS,OAAO,OAAO;IAC5B,IAAI,SAAS,CAAA,GAAA,iJAAA,CAAA,UAAO,AAAD,EAAE;IACrB,OAAO,UAAU,OAAO;AAC1B;AACO,IAAI,oBAAoB,gKAAA,CAAA,4BAAyB;AACxD;;CAEC,GACD,2HAA2H;AAC3H,0CAA0C;AAC1C,6CAA6C;AAC7C,kBAAkB;AAClB,QAAQ;AACR,gDAAgD;AAChD,mBAAmB;AACnB,8CAA8C;AAC9C,QAAQ;AACR,IAAI;AACJ,wBAAwB;AACxB,eAAe,wBAAwB,oJAAA,CAAA,kBAAe;AACtD,eAAe,mCAAmC,oJAAA,CAAA,gBAAa;AAC/D,eAAe,mCAAmC,oJAAA,CAAA,uBAAoB;AACtE,eAAe,wBAAwB,qJAAA,CAAA,mBAAgB;AACvD,eAAe,mCAAmC,qJAAA,CAAA,iBAAc;AAChE,eAAe,uBAAuB,oJAAA,CAAA,UAAK;AAC3C,qBAAqB,mKAAA,CAAA,UAAc;AACnC,kBAAkB,8BAA8B,2JAAA,CAAA,UAAS;AACzD,gBAAgB,WAAW,uJAAA,CAAA,UAAc;AACzC,kBAAkB;AAClB,eAAe;IACb,MAAM,mJAAA,CAAA,wBAAqB;IAC3B,OAAO,mJAAA,CAAA,wBAAqB;IAC5B,QAAQ,mJAAA,CAAA,wBAAqB;AAC/B,GAAG,iJAAA,CAAA,OAAI;AACP,eAAe;IACb,MAAM,mJAAA,CAAA,uBAAoB;IAC1B,OAAO,mJAAA,CAAA,uBAAoB;IAC3B,QAAQ,mJAAA,CAAA,uBAAoB;AAC9B,GAAG,iJAAA,CAAA,OAAI;AACP,eAAe;IACb,MAAM,mJAAA,CAAA,qBAAkB;IACxB,OAAO,mJAAA,CAAA,qBAAkB;IACzB,QAAQ,mJAAA,CAAA,qBAAkB;AAC5B,GAAG,iJAAA,CAAA,OAAI;AACP,eAAe;IACb,MAAM,mJAAA,CAAA,uBAAoB;IAC1B,OAAO,mJAAA,CAAA,uBAAoB;IAC3B,QAAQ,mJAAA,CAAA,uBAAoB;AAC9B,GAAG,iJAAA,CAAA,OAAI;AACP,eAAe;IACb,MAAM,mJAAA,CAAA,4BAAyB;IAC/B,OAAO,mJAAA,CAAA,4BAAyB;IAChC,QAAQ,mJAAA,CAAA,4BAAyB;AACnC,GAAG,iJAAA,CAAA,OAAI;AACP,gBAAgB;AAChB,cAAc,SAAS,mJAAA,CAAA,UAAU;AACjC,cAAc,QAAQ,kJAAA,CAAA,UAAS;AAGxB,IAAI,WAAW,CAAC", "ignoreList": [0], "debugId": null}}]}