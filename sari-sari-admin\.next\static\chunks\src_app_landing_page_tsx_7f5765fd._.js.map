{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/tindahan/sari-sari-admin/src/app/landing/page.tsx"], "sourcesContent": ["'use client'\n\nimport { motion } from 'framer-motion'\nimport { ArrowRight, ShoppingBag, Users, BarChart3, Shield, Star, CheckCircle } from 'lucide-react'\nimport Link from 'next/link'\nimport Image from 'next/image'\n\nexport default function LandingPage() {\n  const features = [\n    {\n      icon: ShoppingBag,\n      title: 'Product Lists Management',\n      description: 'Complete CRUD operations for your product inventory with image uploads and categorization.',\n    },\n    {\n      icon: Users,\n      title: 'Customer Debt Tracking',\n      description: 'Efficiently manage customer credit (utang) with detailed records and payment tracking.',\n    },\n    {\n      icon: BarChart3,\n      title: 'Business Analytics',\n      description: 'Visual charts and reports to understand your business performance and trends.',\n    },\n    {\n      icon: Shield,\n      title: 'Secure & Reliable',\n      description: 'Built with modern security practices and reliable cloud infrastructure.',\n    },\n  ]\n\n  const testimonials = [\n    {\n      name: '<PERSON>',\n      role: 'Store Owner',\n      content: 'Revantad Store has transformed how I manage my sari-sari store. The debt tracking feature is a game-changer!',\n      rating: 5,\n    },\n    {\n      name: '<PERSON>',\n      role: 'Business Owner',\n      content: 'The product management system is so easy to use. I can update my inventory anywhere, anytime.',\n      rating: 5,\n    },\n    {\n      name: '<PERSON>',\n      role: 'Entrepreneur',\n      content: 'Professional, reliable, and perfect for small businesses. Highly recommended!',\n      rating: 5,\n    },\n  ]\n\n  return (\n    <div className=\"min-h-screen bg-white dark:bg-slate-900\">\n      {/* Navigation */}\n      <nav className=\"fixed top-0 w-full z-50 glass-effect\">\n        <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n          <div className=\"flex justify-between items-center h-16\">\n            <motion.div \n              initial={{ opacity: 0, x: -20 }}\n              animate={{ opacity: 1, x: 0 }}\n              className=\"flex items-center space-x-2\"\n            >\n              <div className=\"w-8 h-8 hero-gradient rounded-lg flex items-center justify-center\">\n                <span className=\"text-white font-bold text-lg\">R</span>\n              </div>\n              <span className=\"text-xl font-bold text-gradient\">Revantad Store</span>\n            </motion.div>\n            \n            <motion.div\n              initial={{ opacity: 0, x: 20 }}\n              animate={{ opacity: 1, x: 0 }}\n              transition={{ delay: 0.2 }}\n              className=\"flex space-x-3\"\n            >\n              <Link href=\"/login\" className=\"btn-primary\">\n                Admin Login\n                <ArrowRight className=\"ml-2 h-4 w-4\" />\n              </Link>\n              <Link href=\"/admin\" className=\"btn-outline bg-white/10 border-white/30 text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-slate-700\">\n                Dashboard\n              </Link>\n            </motion.div>\n          </div>\n        </div>\n      </nav>\n\n      {/* Hero Section */}\n      <section className=\"pt-20 pb-16 hero-gradient\">\n        <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n          <div className=\"grid lg:grid-cols-2 gap-12 items-center\">\n            <motion.div\n              initial={{ opacity: 0, y: 30 }}\n              animate={{ opacity: 1, y: 0 }}\n              transition={{ duration: 0.8 }}\n              className=\"text-white\"\n            >\n              <h1 className=\"text-5xl lg:text-6xl font-bold font-display mb-6 leading-tight\">\n                Modernize Your\n                <span className=\"block text-yellow-300\">Sari-Sari Store</span>\n              </h1>\n              <p className=\"text-xl mb-8 text-green-100 leading-relaxed\">\n                Professional admin dashboard for managing products, customer debt, and business analytics. \n                Built specifically for Filipino entrepreneurs.\n              </p>\n              <div className=\"flex flex-col sm:flex-row gap-4\">\n                <Link href=\"/login\" className=\"btn-secondary inline-flex items-center justify-center\">\n                  Start Managing\n                  <ArrowRight className=\"ml-2 h-5 w-5\" />\n                </Link>\n                <Link href=\"/admin\" className=\"btn-outline bg-white/10 border-white/30 text-white hover:bg-white hover:text-green-600\">\n                  View Dashboard\n                </Link>\n              </div>\n            </motion.div>\n            \n            <motion.div\n              initial={{ opacity: 0, scale: 0.8 }}\n              animate={{ opacity: 1, scale: 1 }}\n              transition={{ duration: 0.8, delay: 0.2 }}\n              className=\"relative\"\n            >\n              <div className=\"relative z-10 bg-white/10 backdrop-blur-lg rounded-2xl p-8 border border-white/20\">\n                <div className=\"space-y-4\">\n                  <div className=\"flex items-center justify-between p-4 bg-white/20 rounded-lg\">\n                    <span className=\"text-white font-medium\">Total Products</span>\n                    <span className=\"text-yellow-300 font-bold text-2xl\">247</span>\n                  </div>\n                  <div className=\"flex items-center justify-between p-4 bg-white/20 rounded-lg\">\n                    <span className=\"text-white font-medium\">Customer Debts</span>\n                    <span className=\"text-yellow-300 font-bold text-2xl\">₱12,450</span>\n                  </div>\n                  <div className=\"flex items-center justify-between p-4 bg-white/20 rounded-lg\">\n                    <span className=\"text-white font-medium\">Monthly Revenue</span>\n                    <span className=\"text-yellow-300 font-bold text-2xl\">₱45,200</span>\n                  </div>\n                </div>\n              </div>\n              <div className=\"absolute -top-4 -right-4 w-72 h-72 bg-yellow-400/20 rounded-full blur-3xl\"></div>\n              <div className=\"absolute -bottom-4 -left-4 w-72 h-72 bg-green-400/20 rounded-full blur-3xl\"></div>\n            </motion.div>\n          </div>\n        </div>\n      </section>\n\n      {/* Features Section */}\n      <section className=\"py-20 bg-gray-50 dark:bg-slate-800\">\n        <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n          <motion.div\n            initial={{ opacity: 0, y: 20 }}\n            whileInView={{ opacity: 1, y: 0 }}\n            transition={{ duration: 0.6 }}\n            className=\"text-center mb-16\"\n          >\n            <h2 className=\"text-4xl font-bold text-gray-900 dark:text-white mb-4\">\n              Everything You Need to <span className=\"text-gradient\">Succeed</span>\n            </h2>\n            <p className=\"text-xl text-gray-600 dark:text-gray-300 max-w-3xl mx-auto\">\n              Comprehensive tools designed specifically for Filipino sari-sari store owners\n            </p>\n          </motion.div>\n\n          <div className=\"grid md:grid-cols-2 lg:grid-cols-4 gap-8\">\n            {features.map((feature, index) => (\n              <motion.div\n                key={feature.title}\n                initial={{ opacity: 0, y: 20 }}\n                whileInView={{ opacity: 1, y: 0 }}\n                transition={{ duration: 0.6, delay: index * 0.1 }}\n                className=\"card p-6 text-center hover:shadow-lg transition-all duration-300\"\n              >\n                <div className=\"w-16 h-16 hero-gradient rounded-full flex items-center justify-center mx-auto mb-4\">\n                  <feature.icon className=\"h-8 w-8 text-white\" />\n                </div>\n                <h3 className=\"text-xl font-semibold text-gray-900 dark:text-white mb-3\">\n                  {feature.title}\n                </h3>\n                <p className=\"text-gray-600 dark:text-gray-300\">\n                  {feature.description}\n                </p>\n              </motion.div>\n            ))}\n          </div>\n        </div>\n      </section>\n\n      {/* Testimonials Section */}\n      <section className=\"py-20 bg-white dark:bg-slate-900\">\n        <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n          <motion.div\n            initial={{ opacity: 0, y: 20 }}\n            whileInView={{ opacity: 1, y: 0 }}\n            transition={{ duration: 0.6 }}\n            className=\"text-center mb-16\"\n          >\n            <h2 className=\"text-4xl font-bold text-gray-900 dark:text-white mb-4\">\n              Trusted by <span className=\"text-gradient\">Store Owners</span>\n            </h2>\n            <p className=\"text-xl text-gray-600 dark:text-gray-300\">\n              See what our users say about Revantad Store\n            </p>\n          </motion.div>\n\n          <div className=\"grid md:grid-cols-3 gap-8\">\n            {testimonials.map((testimonial, index) => (\n              <motion.div\n                key={testimonial.name}\n                initial={{ opacity: 0, y: 20 }}\n                whileInView={{ opacity: 1, y: 0 }}\n                transition={{ duration: 0.6, delay: index * 0.1 }}\n                className=\"card p-6\"\n              >\n                <div className=\"flex items-center mb-4\">\n                  {[...Array(testimonial.rating)].map((_, i) => (\n                    <Star key={i} className=\"h-5 w-5 text-yellow-400 fill-current\" />\n                  ))}\n                </div>\n                <p className=\"text-gray-600 dark:text-gray-300 mb-4 italic\">\n                  \"{testimonial.content}\"\n                </p>\n                <div>\n                  <p className=\"font-semibold text-gray-900 dark:text-white\">\n                    {testimonial.name}\n                  </p>\n                  <p className=\"text-sm text-gray-500 dark:text-gray-400\">\n                    {testimonial.role}\n                  </p>\n                </div>\n              </motion.div>\n            ))}\n          </div>\n        </div>\n      </section>\n\n      {/* CTA Section */}\n      <section className=\"py-20 hero-gradient\">\n        <div className=\"max-w-4xl mx-auto text-center px-4 sm:px-6 lg:px-8\">\n          <motion.div\n            initial={{ opacity: 0, y: 20 }}\n            whileInView={{ opacity: 1, y: 0 }}\n            transition={{ duration: 0.6 }}\n            className=\"text-white\"\n          >\n            <h2 className=\"text-4xl font-bold mb-6\">\n              Ready to Transform Your Business?\n            </h2>\n            <p className=\"text-xl mb-8 text-green-100\">\n              Join hundreds of store owners who have modernized their operations with Revantad Store\n            </p>\n            <Link href=\"/login\" className=\"btn-secondary inline-flex items-center text-lg px-8 py-4\">\n              Get Started Today\n              <ArrowRight className=\"ml-2 h-5 w-5\" />\n            </Link>\n          </motion.div>\n        </div>\n      </section>\n\n      {/* Footer */}\n      <footer className=\"bg-gray-900 text-white py-12\">\n        <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n          <div className=\"text-center\">\n            <div className=\"flex items-center justify-center space-x-2 mb-4\">\n              <div className=\"w-8 h-8 hero-gradient rounded-lg flex items-center justify-center\">\n                <span className=\"text-white font-bold text-lg\">R</span>\n              </div>\n              <span className=\"text-xl font-bold text-gradient\">Revantad Store</span>\n            </div>\n            <p className=\"text-gray-400 mb-4\">\n              Professional admin dashboard for modern sari-sari stores\n            </p>\n            <p className=\"text-gray-500 text-sm\">\n              © 2024 Revantad Store. All rights reserved.\n            </p>\n          </div>\n        </div>\n      </footer>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AAJA;;;;;AAOe,SAAS;IACtB,MAAM,WAAW;QACf;YACE,MAAM,uNAAA,CAAA,cAAW;YACjB,OAAO;YACP,aAAa;QACf;QACA;YACE,MAAM,uMAAA,CAAA,QAAK;YACX,OAAO;YACP,aAAa;QACf;QACA;YACE,MAAM,qNAAA,CAAA,YAAS;YACf,OAAO;YACP,aAAa;QACf;QACA;YACE,MAAM,yMAAA,CAAA,SAAM;YACZ,OAAO;YACP,aAAa;QACf;KACD;IAED,MAAM,eAAe;QACnB;YACE,MAAM;YACN,MAAM;YACN,SAAS;YACT,QAAQ;QACV;QACA;YACE,MAAM;YACN,MAAM;YACN,SAAS;YACT,QAAQ;QACV;QACA;YACE,MAAM;YACN,MAAM;YACN,SAAS;YACT,QAAQ;QACV;KACD;IAED,qBACE,6LAAC;QAAI,WAAU;;0BAEb,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC;wBAAI,WAAU;;0CACb,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;gCACT,SAAS;oCAAE,SAAS;oCAAG,GAAG,CAAC;gCAAG;gCAC9B,SAAS;oCAAE,SAAS;oCAAG,GAAG;gCAAE;gCAC5B,WAAU;;kDAEV,6LAAC;wCAAI,WAAU;kDACb,cAAA,6LAAC;4CAAK,WAAU;sDAA+B;;;;;;;;;;;kDAEjD,6LAAC;wCAAK,WAAU;kDAAkC;;;;;;;;;;;;0CAGpD,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;gCACT,SAAS;oCAAE,SAAS;oCAAG,GAAG;gCAAG;gCAC7B,SAAS;oCAAE,SAAS;oCAAG,GAAG;gCAAE;gCAC5B,YAAY;oCAAE,OAAO;gCAAI;gCACzB,WAAU;;kDAEV,6LAAC,+JAAA,CAAA,UAAI;wCAAC,MAAK;wCAAS,WAAU;;4CAAc;0DAE1C,6LAAC,qNAAA,CAAA,aAAU;gDAAC,WAAU;;;;;;;;;;;;kDAExB,6LAAC,+JAAA,CAAA,UAAI;wCAAC,MAAK;wCAAS,WAAU;kDAAoH;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAS1J,6LAAC;gBAAQ,WAAU;0BACjB,cAAA,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC;wBAAI,WAAU;;0CACb,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;gCACT,SAAS;oCAAE,SAAS;oCAAG,GAAG;gCAAG;gCAC7B,SAAS;oCAAE,SAAS;oCAAG,GAAG;gCAAE;gCAC5B,YAAY;oCAAE,UAAU;gCAAI;gCAC5B,WAAU;;kDAEV,6LAAC;wCAAG,WAAU;;4CAAiE;0DAE7E,6LAAC;gDAAK,WAAU;0DAAwB;;;;;;;;;;;;kDAE1C,6LAAC;wCAAE,WAAU;kDAA8C;;;;;;kDAI3D,6LAAC;wCAAI,WAAU;;0DACb,6LAAC,+JAAA,CAAA,UAAI;gDAAC,MAAK;gDAAS,WAAU;;oDAAwD;kEAEpF,6LAAC,qNAAA,CAAA,aAAU;wDAAC,WAAU;;;;;;;;;;;;0DAExB,6LAAC,+JAAA,CAAA,UAAI;gDAAC,MAAK;gDAAS,WAAU;0DAAyF;;;;;;;;;;;;;;;;;;0CAM3H,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;gCACT,SAAS;oCAAE,SAAS;oCAAG,OAAO;gCAAI;gCAClC,SAAS;oCAAE,SAAS;oCAAG,OAAO;gCAAE;gCAChC,YAAY;oCAAE,UAAU;oCAAK,OAAO;gCAAI;gCACxC,WAAU;;kDAEV,6LAAC;wCAAI,WAAU;kDACb,cAAA,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DAAK,WAAU;sEAAyB;;;;;;sEACzC,6LAAC;4DAAK,WAAU;sEAAqC;;;;;;;;;;;;8DAEvD,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DAAK,WAAU;sEAAyB;;;;;;sEACzC,6LAAC;4DAAK,WAAU;sEAAqC;;;;;;;;;;;;8DAEvD,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DAAK,WAAU;sEAAyB;;;;;;sEACzC,6LAAC;4DAAK,WAAU;sEAAqC;;;;;;;;;;;;;;;;;;;;;;;kDAI3D,6LAAC;wCAAI,WAAU;;;;;;kDACf,6LAAC;wCAAI,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAOvB,6LAAC;gBAAQ,WAAU;0BACjB,cAAA,6LAAC;oBAAI,WAAU;;sCACb,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;4BACT,SAAS;gCAAE,SAAS;gCAAG,GAAG;4BAAG;4BAC7B,aAAa;gCAAE,SAAS;gCAAG,GAAG;4BAAE;4BAChC,YAAY;gCAAE,UAAU;4BAAI;4BAC5B,WAAU;;8CAEV,6LAAC;oCAAG,WAAU;;wCAAwD;sDAC7C,6LAAC;4CAAK,WAAU;sDAAgB;;;;;;;;;;;;8CAEzD,6LAAC;oCAAE,WAAU;8CAA6D;;;;;;;;;;;;sCAK5E,6LAAC;4BAAI,WAAU;sCACZ,SAAS,GAAG,CAAC,CAAC,SAAS,sBACtB,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;oCAET,SAAS;wCAAE,SAAS;wCAAG,GAAG;oCAAG;oCAC7B,aAAa;wCAAE,SAAS;wCAAG,GAAG;oCAAE;oCAChC,YAAY;wCAAE,UAAU;wCAAK,OAAO,QAAQ;oCAAI;oCAChD,WAAU;;sDAEV,6LAAC;4CAAI,WAAU;sDACb,cAAA,6LAAC,QAAQ,IAAI;gDAAC,WAAU;;;;;;;;;;;sDAE1B,6LAAC;4CAAG,WAAU;sDACX,QAAQ,KAAK;;;;;;sDAEhB,6LAAC;4CAAE,WAAU;sDACV,QAAQ,WAAW;;;;;;;mCAbjB,QAAQ,KAAK;;;;;;;;;;;;;;;;;;;;;0BAsB5B,6LAAC;gBAAQ,WAAU;0BACjB,cAAA,6LAAC;oBAAI,WAAU;;sCACb,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;4BACT,SAAS;gCAAE,SAAS;gCAAG,GAAG;4BAAG;4BAC7B,aAAa;gCAAE,SAAS;gCAAG,GAAG;4BAAE;4BAChC,YAAY;gCAAE,UAAU;4BAAI;4BAC5B,WAAU;;8CAEV,6LAAC;oCAAG,WAAU;;wCAAwD;sDACzD,6LAAC;4CAAK,WAAU;sDAAgB;;;;;;;;;;;;8CAE7C,6LAAC;oCAAE,WAAU;8CAA2C;;;;;;;;;;;;sCAK1D,6LAAC;4BAAI,WAAU;sCACZ,aAAa,GAAG,CAAC,CAAC,aAAa,sBAC9B,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;oCAET,SAAS;wCAAE,SAAS;wCAAG,GAAG;oCAAG;oCAC7B,aAAa;wCAAE,SAAS;wCAAG,GAAG;oCAAE;oCAChC,YAAY;wCAAE,UAAU;wCAAK,OAAO,QAAQ;oCAAI;oCAChD,WAAU;;sDAEV,6LAAC;4CAAI,WAAU;sDACZ;mDAAI,MAAM,YAAY,MAAM;6CAAE,CAAC,GAAG,CAAC,CAAC,GAAG,kBACtC,6LAAC,qMAAA,CAAA,OAAI;oDAAS,WAAU;mDAAb;;;;;;;;;;sDAGf,6LAAC;4CAAE,WAAU;;gDAA+C;gDACxD,YAAY,OAAO;gDAAC;;;;;;;sDAExB,6LAAC;;8DACC,6LAAC;oDAAE,WAAU;8DACV,YAAY,IAAI;;;;;;8DAEnB,6LAAC;oDAAE,WAAU;8DACV,YAAY,IAAI;;;;;;;;;;;;;mCAnBhB,YAAY,IAAI;;;;;;;;;;;;;;;;;;;;;0BA6B/B,6LAAC;gBAAQ,WAAU;0BACjB,cAAA,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;wBACT,SAAS;4BAAE,SAAS;4BAAG,GAAG;wBAAG;wBAC7B,aAAa;4BAAE,SAAS;4BAAG,GAAG;wBAAE;wBAChC,YAAY;4BAAE,UAAU;wBAAI;wBAC5B,WAAU;;0CAEV,6LAAC;gCAAG,WAAU;0CAA0B;;;;;;0CAGxC,6LAAC;gCAAE,WAAU;0CAA8B;;;;;;0CAG3C,6LAAC,+JAAA,CAAA,UAAI;gCAAC,MAAK;gCAAS,WAAU;;oCAA2D;kDAEvF,6LAAC,qNAAA,CAAA,aAAU;wCAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAO9B,6LAAC;gBAAO,WAAU;0BAChB,cAAA,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;kDACb,cAAA,6LAAC;4CAAK,WAAU;sDAA+B;;;;;;;;;;;kDAEjD,6LAAC;wCAAK,WAAU;kDAAkC;;;;;;;;;;;;0CAEpD,6LAAC;gCAAE,WAAU;0CAAqB;;;;;;0CAGlC,6LAAC;gCAAE,WAAU;0CAAwB;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQjD;KA/QwB", "debugId": null}}]}