{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 148, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/tindahan/sari-sari-admin/src/lib/supabase.ts"], "sourcesContent": ["import { createClient } from '@supabase/supabase-js'\n\nconst supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL!\nconst supabaseAnonKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!\n\nexport const supabase = createClient(supabaseUrl, supabase<PERSON>nonKey)\n\n// Database Types\nexport interface Product {\n  id: string\n  name: string\n  image_url?: string\n  net_weight: string\n  price: number\n  stock_quantity: number\n  category: string\n  created_at: string\n  updated_at: string\n}\n\nexport interface CustomerDebt {\n  id: string\n  customer_name: string\n  customer_family_name: string\n  product_name: string\n  product_price: number\n  quantity: number\n  total_amount: number\n  debt_date: string\n  created_at: string\n  updated_at: string\n}\n\n// Product Categories\nexport const PRODUCT_CATEGORIES = [\n  'Snacks',\n  'Canned Goods',\n  'Beverages',\n  'Personal Care',\n  'Household Items',\n  'Condiments',\n  'Rice & Grains',\n  'Instant Foods',\n  'Dairy Products',\n  'Others'\n] as const\n\nexport type ProductCategory = typeof PRODUCT_CATEGORIES[number]\n"], "names": [], "mappings": ";;;;AAAA;;AAEA,MAAM;AACN,MAAM;AAEC,MAAM,WAAW,CAAA,GAAA,yLAAA,CAAA,eAAY,AAAD,EAAE,aAAa;AA6B3C,MAAM,qBAAqB;IAChC;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;CACD", "debugId": null}}, {"offset": {"line": 175, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/tindahan/sari-sari-admin/src/app/api/debts/route.ts"], "sourcesContent": ["import { NextRequest, NextResponse } from 'next/server'\nimport { supabase } from '@/lib/supabase'\n\n// GET - Fetch all customer debts\nexport async function GET() {\n  try {\n    const { data: debts, error } = await supabase\n      .from('customer_debts')\n      .select('*')\n      .order('created_at', { ascending: false })\n\n    if (error) {\n      return NextResponse.json({ error: error.message }, { status: 500 })\n    }\n\n    return NextResponse.json({ debts })\n  } catch {\n    return NextResponse.json(\n      { error: 'Failed to fetch customer debts' },\n      { status: 500 }\n    )\n  }\n}\n\n// POST - Create new customer debt\nexport async function POST(request: NextRequest) {\n  try {\n    const body = await request.json()\n    const {\n      customer_name,\n      customer_family_name,\n      product_name,\n      product_price,\n      quantity,\n      debt_date,\n    } = body\n\n    // Validate required fields\n    if (\n      !customer_name ||\n      !customer_family_name ||\n      !product_name ||\n      !product_price ||\n      !quantity\n    ) {\n      return NextResponse.json(\n        { error: 'Missing required fields' },\n        { status: 400 }\n      )\n    }\n\n    const { data: debt, error } = await supabase\n      .from('customer_debts')\n      .insert([\n        {\n          customer_name,\n          customer_family_name,\n          product_name,\n          product_price: parseFloat(product_price),\n          quantity: parseInt(quantity),\n          debt_date: debt_date || new Date().toISOString().split('T')[0],\n        },\n      ])\n      .select()\n      .single()\n\n    if (error) {\n      return NextResponse.json({ error: error.message }, { status: 500 })\n    }\n\n    return NextResponse.json({ debt }, { status: 201 })\n  } catch {\n    return NextResponse.json(\n      { error: 'Failed to create customer debt' },\n      { status: 500 }\n    )\n  }\n}\n"], "names": [], "mappings": ";;;;AAAA;AACA;;;AAGO,eAAe;IACpB,IAAI;QACF,MAAM,EAAE,MAAM,KAAK,EAAE,KAAK,EAAE,GAAG,MAAM,wHAAA,CAAA,WAAQ,CAC1C,IAAI,CAAC,kBACL,MAAM,CAAC,KACP,KAAK,CAAC,cAAc;YAAE,WAAW;QAAM;QAE1C,IAAI,OAAO;YACT,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;gBAAE,OAAO,MAAM,OAAO;YAAC,GAAG;gBAAE,QAAQ;YAAI;QACnE;QAEA,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;YAAE;QAAM;IACnC,EAAE,OAAM;QACN,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;YAAE,OAAO;QAAiC,GAC1C;YAAE,QAAQ;QAAI;IAElB;AACF;AAGO,eAAe,KAAK,OAAoB;IAC7C,IAAI;QACF,MAAM,OAAO,MAAM,QAAQ,IAAI;QAC/B,MAAM,EACJ,aAAa,EACb,oBAAoB,EACpB,YAAY,EACZ,aAAa,EACb,QAAQ,EACR,SAAS,EACV,GAAG;QAEJ,2BAA2B;QAC3B,IACE,CAAC,iBACD,CAAC,wBACD,CAAC,gBACD,CAAC,iBACD,CAAC,UACD;YACA,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBAAE,OAAO;YAA0B,GACnC;gBAAE,QAAQ;YAAI;QAElB;QAEA,MAAM,EAAE,MAAM,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,wHAAA,CAAA,WAAQ,CACzC,IAAI,CAAC,kBACL,MAAM,CAAC;YACN;gBACE;gBACA;gBACA;gBACA,eAAe,WAAW;gBAC1B,UAAU,SAAS;gBACnB,WAAW,aAAa,IAAI,OAAO,WAAW,GAAG,KAAK,CAAC,IAAI,CAAC,EAAE;YAChE;SACD,EACA,MAAM,GACN,MAAM;QAET,IAAI,OAAO;YACT,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;gBAAE,OAAO,MAAM,OAAO;YAAC,GAAG;gBAAE,QAAQ;YAAI;QACnE;QAEA,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;YAAE;QAAK,GAAG;YAAE,QAAQ;QAAI;IACnD,EAAE,OAAM;QACN,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;YAAE,OAAO;QAAiC,GAC1C;YAAE,QAAQ;QAAI;IAElB;AACF", "debugId": null}}]}