'use client'

import { useState } from 'react'
import { BarChart3, History, Calendar, Settings, ChevronLeft } from 'lucide-react'
import { useTheme } from 'next-themes'

interface SidebarProps {
  activeSection: string
  setActiveSection: (section: string) => void
}

export default function Sidebar({ activeSection, setActiveSection }: SidebarProps) {
  const { resolvedTheme } = useTheme()
  const [isCollapsed, setIsCollapsed] = useState(false)

  const toggleSidebar = () => {
    const button = document.querySelector('.sidebar-toggle-btn')

    // Add animation class based on current state
    if (isCollapsed) {
      button?.classList.add('expanding')
      setTimeout(() => button?.classList.remove('expanding'), 600)
    } else {
      button?.classList.add('collapsing')
      setTimeout(() => button?.classList.remove('collapsing'), 600)
    }

    setIsCollapsed(!isCollapsed)
  }

  const menuItems = [
    {
      id: 'api-graphing',
      label: 'API Graphing & Visuals',
      icon: BarChart3,
      description: 'ECharts analytics and data visualization'
    },
    {
      id: 'history',
      label: 'History',
      icon: History,
      description: 'Transaction and activity logs'
    },
    {
      id: 'calendar',
      label: 'Calendar',
      icon: Calendar,
      description: 'Events and scheduling'
    },
    {
      id: 'settings',
      label: 'Settings',
      icon: Settings,
      description: 'Store configuration and preferences'
    },
  ]

  return (
    <div
      className={`shadow-xl border-r sticky top-16 h-[calc(100vh-4rem)] overflow-hidden sidebar-transition ${
        isCollapsed ? 'sidebar-collapsed' : 'sidebar-expanded'
      }`}
      style={{
        backgroundColor: resolvedTheme === 'dark' ? '#1e293b' : '#ffffff',
        borderColor: resolvedTheme === 'dark' ? '#334155' : '#e5e7eb',
        borderWidth: '1px',
        boxShadow: resolvedTheme === 'dark'
          ? '0 25px 50px -12px rgba(0, 0, 0, 0.5), 0 0 0 1px rgba(255, 255, 255, 0.05)'
          : '0 25px 50px -12px rgba(0, 0, 0, 0.25), 0 0 0 1px rgba(0, 0, 0, 0.05)'
      }}
    >
      {/* Professional Chevron Toggle Button */}
      <button
        onClick={toggleSidebar}
        className="sidebar-toggle-btn group"
        style={{
          background: resolvedTheme === 'dark'
            ? 'linear-gradient(135deg, #22c55e 0%, #16a34a 100%)'
            : 'linear-gradient(135deg, #16a34a 0%, #15803d 100%)',
          borderColor: resolvedTheme === 'dark' ? '#4ade80' : '#22c55e',
          color: '#ffffff',
          boxShadow: resolvedTheme === 'dark'
            ? '0 4px 12px rgba(34, 197, 94, 0.3), inset 0 1px 0 rgba(255, 255, 255, 0.1)'
            : '0 4px 12px rgba(22, 163, 74, 0.3), inset 0 1px 0 rgba(255, 255, 255, 0.2)'
        }}
        title={isCollapsed
          ? 'Expand sidebar (Click to show full menu with descriptions)'
          : 'Collapse sidebar (Click to show icons only with tooltips)'
        }
        aria-label={isCollapsed ? 'Expand sidebar' : 'Collapse sidebar'}
        aria-expanded={!isCollapsed}
      >
        <ChevronLeft
          className="h-4 w-4 sidebar-toggle-icon"
          strokeWidth={2.5}
          style={{
            transition: 'all 0.4s cubic-bezier(0.4, 0, 0.2, 1)',
            filter: 'drop-shadow(0 1px 2px rgba(0, 0, 0, 0.2))'
          }}
        />
      </button>
      <div className="flex flex-col h-full">
        {/* Enhanced Fixed Header Section */}
        <div
          className={`sticky top-0 z-20 transition-all duration-300 backdrop-blur-md ${
            isCollapsed ? 'px-3 py-4' : 'px-6 py-5'
          }`}
          style={{
            background: resolvedTheme === 'dark'
              ? 'linear-gradient(135deg, rgba(30, 41, 59, 0.98) 0%, rgba(51, 65, 85, 0.95) 100%)'
              : 'linear-gradient(135deg, rgba(255, 255, 255, 0.98) 0%, rgba(249, 250, 251, 0.95) 100%)',
            borderBottom: resolvedTheme === 'dark'
              ? '1px solid rgba(148, 163, 184, 0.2)'
              : '1px solid rgba(229, 231, 235, 0.8)',
            boxShadow: resolvedTheme === 'dark'
              ? '0 4px 6px -1px rgba(0, 0, 0, 0.3), 0 2px 4px -1px rgba(0, 0, 0, 0.2)'
              : '0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06)'
          }}
        >
          <div className={`flex items-center ${isCollapsed ? 'justify-center mb-2' : 'mb-3'}`}>
            <div
              className={`rounded-lg flex items-center justify-center transition-all duration-300 collapsed-header-icon ${
                isCollapsed ? 'w-8 h-8' : 'w-8 h-8 mr-3'
              }`}
              style={{
                background: resolvedTheme === 'dark'
                  ? 'linear-gradient(135deg, #22c55e 0%, #16a34a 100%)'
                  : 'linear-gradient(135deg, #10b981 0%, #059669 100%)',
                boxShadow: isCollapsed
                  ? '0 6px 12px rgba(34, 197, 94, 0.4)'
                  : '0 4px 8px rgba(34, 197, 94, 0.3)'
              }}
              title={isCollapsed ? 'Additional Tools' : undefined}
            >
              <span className={`text-white font-bold ${isCollapsed ? 'text-sm' : 'text-sm'}`}>⚡</span>
            </div>
            {!isCollapsed && (
              <h2
                className={`text-lg font-bold transition-all duration-300 crisp-text ${
                  isCollapsed ? 'sidebar-content-hidden' : 'sidebar-content-visible'
                }`}
                style={{
                  color: resolvedTheme === 'dark' ? '#f8fafc' : '#111827',
                  textShadow: resolvedTheme === 'dark' ? '0 1px 2px rgba(0, 0, 0, 0.3)' : 'none'
                }}
              >
                Additional Tools
              </h2>
            )}
          </div>
          {!isCollapsed && (
            <p
              className={`text-xs font-medium transition-all duration-300 crisp-text ${
                isCollapsed ? 'sidebar-content-hidden' : 'sidebar-content-visible'
              }`}
              style={{
                color: resolvedTheme === 'dark' ? '#94a3b8' : '#64748b',
                letterSpacing: '0.025em'
              }}
            >
              Advanced features and utilities
            </p>
          )}
        </div>

        {/* Enhanced Scrollable Navigation Section */}
        <div className="flex-1 relative">
          {/* Scroll container with enhanced scrollbar */}
          <div className="absolute inset-0 overflow-hidden">
            <nav className={`h-full py-6 overflow-y-auto sidebar-nav-scroll transition-all duration-300 ${
              isCollapsed ? 'px-2 space-y-4' : 'px-4 pr-2 space-y-3'
            }`} style={{ marginRight: isCollapsed ? '0' : '-6px', paddingRight: isCollapsed ? '8px' : '10px' }}>
        {menuItems.map((item) => {
          const Icon = item.icon
          const isActive = activeSection === item.id

          return (
            <button
              key={item.id}
              onClick={() => setActiveSection(item.id)}
              className={`w-full flex items-start text-left transition-all duration-300 group sidebar-nav-item crisp-text relative overflow-visible ${
                isCollapsed
                  ? `sidebar-icon-only ${isActive ? 'active' : ''}`
                  : 'p-4 rounded-2xl overflow-hidden'
              }`}
              style={{
                background: isActive
                  ? (resolvedTheme === 'dark'
                    ? 'linear-gradient(135deg, rgba(34, 197, 94, 0.2) 0%, rgba(16, 185, 129, 0.15) 100%)'
                    : 'linear-gradient(135deg, rgba(34, 197, 94, 0.1) 0%, rgba(16, 185, 129, 0.08) 100%)')
                  : 'transparent',
                border: isActive
                  ? (resolvedTheme === 'dark' ? '1px solid rgba(34, 197, 94, 0.4)' : '1px solid rgba(34, 197, 94, 0.3)')
                  : '1px solid transparent',
                boxShadow: isActive
                  ? (resolvedTheme === 'dark'
                    ? '0 4px 12px rgba(34, 197, 94, 0.2), inset 0 1px 0 rgba(255, 255, 255, 0.1)'
                    : '0 4px 12px rgba(34, 197, 94, 0.15), inset 0 1px 0 rgba(255, 255, 255, 0.8)')
                  : 'none'
              }}
              onMouseEnter={(e) => {
                if (!isActive) {
                  e.currentTarget.style.background = resolvedTheme === 'dark'
                    ? 'linear-gradient(135deg, rgba(71, 85, 105, 0.4) 0%, rgba(51, 65, 85, 0.3) 100%)'
                    : 'linear-gradient(135deg, rgba(249, 250, 251, 0.9) 0%, rgba(243, 244, 246, 0.8) 100%)'
                  e.currentTarget.style.boxShadow = resolvedTheme === 'dark'
                    ? '0 4px 12px rgba(0, 0, 0, 0.2), inset 0 1px 0 rgba(255, 255, 255, 0.05)'
                    : '0 4px 12px rgba(0, 0, 0, 0.1), inset 0 1px 0 rgba(255, 255, 255, 0.9)'
                  e.currentTarget.style.border = resolvedTheme === 'dark'
                    ? '1px solid rgba(148, 163, 184, 0.3)'
                    : '1px solid rgba(229, 231, 235, 0.8)'
                }
              }}
              onMouseLeave={(e) => {
                if (!isActive) {
                  e.currentTarget.style.background = 'transparent'
                  e.currentTarget.style.boxShadow = 'none'
                  e.currentTarget.style.border = '1px solid transparent'
                }
              }}
            >
              <div
                className={`transition-all duration-300 relative ${
                  isCollapsed
                    ? 'icon-container p-3 rounded-xl overflow-visible'
                    : 'p-3 rounded-xl mr-4 overflow-hidden'
                }`}
                style={{
                  background: isActive
                    ? (resolvedTheme === 'dark'
                      ? 'linear-gradient(135deg, rgba(34, 197, 94, 0.3) 0%, rgba(16, 185, 129, 0.25) 100%)'
                      : 'linear-gradient(135deg, rgba(34, 197, 94, 0.2) 0%, rgba(16, 185, 129, 0.15) 100%)')
                    : (resolvedTheme === 'dark'
                      ? 'linear-gradient(135deg, rgba(71, 85, 105, 0.5) 0%, rgba(51, 65, 85, 0.4) 100%)'
                      : 'linear-gradient(135deg, rgba(243, 244, 246, 0.9) 0%, rgba(229, 231, 235, 0.7) 100%)'),
                  boxShadow: isActive
                    ? (resolvedTheme === 'dark'
                      ? '0 2px 8px rgba(34, 197, 94, 0.3), inset 0 1px 0 rgba(255, 255, 255, 0.1)'
                      : '0 2px 8px rgba(34, 197, 94, 0.2), inset 0 1px 0 rgba(255, 255, 255, 0.8)')
                    : (resolvedTheme === 'dark'
                      ? 'inset 0 1px 0 rgba(255, 255, 255, 0.05)'
                      : 'inset 0 1px 0 rgba(255, 255, 255, 0.9)')
                }}
              >
                <Icon
                  className={`h-5 w-5 transition-all duration-300 relative z-10 ${
                    isCollapsed ? 'collapsed-icon' : ''
                  }`}
                  style={{
                    color: isActive
                      ? (resolvedTheme === 'dark' ? '#4ade80' : '#16a34a')
                      : (resolvedTheme === 'dark' ? '#e2e8f0' : '#64748b'),
                    filter: isActive ? 'drop-shadow(0 1px 2px rgba(0, 0, 0, 0.2))' : 'none'
                  }}
                />
                {/* Enhanced Tooltip for Collapsed Mode */}
                {isCollapsed && (
                  <div className="sidebar-tooltip">
                    <div className="font-medium">{item.label}</div>
                    <div className="text-xs opacity-80 mt-1">{item.description}</div>
                  </div>
                )}
              </div>
              {!isCollapsed && (
                <div className={`flex-1 sidebar-text ${
                  isCollapsed ? 'sidebar-content-hidden' : 'sidebar-content-visible'
                }`}>
                  <h3
                    className="font-medium text-sm transition-colors duration-300 leading-tight"
                    style={{
                      color: isActive
                        ? (resolvedTheme === 'dark' ? '#4ade80' : '#16a34a')
                        : (resolvedTheme === 'dark' ? '#f8fafc' : '#111827'),
                      textShadow: 'none',
                      fontWeight: isActive ? '600' : '500'
                    }}
                  >
                    {item.label}
                  </h3>
                  <p
                    className="text-xs mt-1 transition-colors duration-300 leading-relaxed"
                    style={{
                      color: isActive
                        ? (resolvedTheme === 'dark' ? '#22c55e' : '#15803d')
                        : (resolvedTheme === 'dark' ? '#cbd5e1' : '#6b7280'),
                      textShadow: 'none',
                      opacity: 0.9
                    }}
                  >
                    {item.description}
                  </p>
                </div>
              )}
            </button>
          )
        })}
            </nav>
          </div>
        </div>

        {/* Enhanced Sticky Footer Section */}
        <div
          className={`sticky bottom-0 z-20 transition-all duration-300 backdrop-blur-md ${
            isCollapsed ? 'px-3 py-4' : 'px-6 py-5'
          }`}
          style={{
            background: resolvedTheme === 'dark'
              ? 'linear-gradient(135deg, rgba(15, 23, 42, 0.98) 0%, rgba(30, 41, 59, 0.95) 100%)'
              : 'linear-gradient(135deg, rgba(249, 250, 251, 0.98) 0%, rgba(255, 255, 255, 0.95) 100%)',
            borderTop: resolvedTheme === 'dark'
              ? '1px solid rgba(148, 163, 184, 0.2)'
              : '1px solid rgba(229, 231, 235, 0.8)',
            boxShadow: resolvedTheme === 'dark'
              ? '0 -4px 6px -1px rgba(0, 0, 0, 0.3), 0 -2px 4px -1px rgba(0, 0, 0, 0.2)'
              : '0 -4px 6px -1px rgba(0, 0, 0, 0.1), 0 -2px 4px -1px rgba(0, 0, 0, 0.06)'
          }}
        >
          <div
            className="text-sm transition-colors duration-300"
            style={{
              color: resolvedTheme === 'dark' ? '#94a3b8' : '#64748b'
            }}
          >
            <div className={`flex items-center mb-3 ${isCollapsed ? 'justify-center space-x-0' : 'space-x-3'}`}>
              <div
                className={`rounded-xl flex items-center justify-center relative overflow-hidden collapsed-footer-icon ${
                  isCollapsed ? 'w-8 h-8' : 'w-8 h-8'
                }`}
                style={{
                  background: 'linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%)',
                  boxShadow: isCollapsed
                    ? '0 6px 12px rgba(59, 130, 246, 0.4)'
                    : '0 4px 8px rgba(59, 130, 246, 0.3)'
                }}
                title={isCollapsed ? 'Revantad Store - Professional Business Management' : undefined}
              >
                <span className={`text-white font-bold relative z-10 ${isCollapsed ? 'text-sm' : 'text-sm'}`}>R</span>
                <div
                  className="absolute inset-0 opacity-20"
                  style={{
                    background: 'linear-gradient(45deg, transparent 30%, rgba(255, 255, 255, 0.5) 50%, transparent 70%)'
                  }}
                />
                {/* Enhanced Tooltip for Footer */}
                {isCollapsed && (
                  <div className="sidebar-tooltip">
                    <div className="font-medium">Revantad Store</div>
                    <div className="text-xs opacity-80 mt-1">Professional Business Management</div>
                    <div className="text-xs opacity-60 mt-1">Admin Dashboard v2.0</div>
                  </div>
                )}
              </div>
              {!isCollapsed && (
                <div className={`${isCollapsed ? 'sidebar-content-hidden' : 'sidebar-content-visible'}`}>
                  <span
                    className="font-bold text-sm transition-colors duration-300 block"
                    style={{
                      color: resolvedTheme === 'dark' ? '#f8fafc' : '#1e293b',
                      textShadow: resolvedTheme === 'dark' ? '0 1px 2px rgba(0, 0, 0, 0.3)' : 'none'
                    }}
                  >
                    Revantad Store
                  </span>
                  <span
                    className="text-xs font-medium"
                    style={{
                      color: resolvedTheme === 'dark' ? '#64748b' : '#94a3b8',
                      letterSpacing: '0.025em'
                    }}
                  >
                    Professional Business Management
                  </span>
                </div>
              )}
            </div>
            {!isCollapsed && (
              <div
                className={`text-xs font-medium px-3 py-2 rounded-lg ${
                  isCollapsed ? 'sidebar-content-hidden' : 'sidebar-content-visible'
                }`}
                style={{
                  backgroundColor: resolvedTheme === 'dark' ? 'rgba(71, 85, 105, 0.3)' : 'rgba(243, 244, 246, 0.8)',
                  color: resolvedTheme === 'dark' ? '#cbd5e1' : '#6b7280',
                  border: resolvedTheme === 'dark' ? '1px solid rgba(148, 163, 184, 0.2)' : '1px solid rgba(229, 231, 235, 0.6)'
                }}
              >
                Admin Dashboard v2.0
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  )
}
